{"permissions": {"allow": ["Bash(ls:*)", "Bash(pnpm docs:build)", "Bash(pnpm add:*)", "Bash(pnpm docs:dev)", "<PERSON><PERSON>(mkdir:*)", "Bash(node:*)", "Bash(git checkout:*)", "Bash(pnpm:*)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/belay-dune.pdf /home/<USER>/xv6/docs/public/assets/mit6.1810/belay-dune)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/homework/belay-dune.pdf /home/<USER>/xv6/docs/mit6.1810/homework/belay-dune)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/l-fs2.pdf /home/<USER>/xv6/docs/public/assets/mit6.1810/l-fs2)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/homework/l-fs2.pdf /home/<USER>/xv6/docs/mit6.1810/homework/l-fs2)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/meltdown.pdf /home/<USER>/xv6/docs/public/assets/mit6.1810/meltdown)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/homework/meltdown.pdf /home/<USER>/xv6/docs/mit6.1810/homework/meltdown)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/microkernel.pdf /home/<USER>/xv6/docs/public/assets/mit6.1810/microkernel)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/homework/microkernel.pdf /home/<USER>/xv6/docs/mit6.1810/homework/microkernel)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/mogul96usenix.pdf /home/<USER>/xv6/docs/public/assets/mit6.1810/mogul96usenix)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/homework/mogul96usenix.pdf /home/<USER>/xv6/docs/mit6.1810/homework/mogul96usenix)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/rcu-decade-later.pdf /home/<USER>/xv6/docs/public/assets/mit6.1810/rcu-decade-later)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/homework/rcu-decade-later.pdf /home/<USER>/xv6/docs/mit6.1810/homework/rcu-decade-later)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/redleaf.pdf /home/<USER>/xv6/docs/public/assets/mit6.1810/redleaf)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/homework/redleaf.pdf /home/<USER>/xv6/docs/mit6.1810/homework/redleaf)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/16550.pdf /home/<USER>/xv6/docs/public/assets/mit6.1810/16550)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/lec/16550.pdf /home/<USER>/xv6/docs/mit6.1810/lec/16550)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/gdb_slides.pdf /home/<USER>/xv6/docs/public/assets/mit6.1810/gdb_slides)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/lec/gdb_slides.pdf /home/<USER>/xv6/docs/mit6.1810/lec/gdb_slides)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/l-c_slides.pdf /home/<USER>/xv6/docs/public/assets/mit6.1810/l-c_slides)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/lec/l-c/l-c_slides.pdf /home/<USER>/xv6/docs/mit6.1810/lec/l-c/l-c_slides)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/l-fs1.pdf /home/<USER>/xv6/docs/public/assets/mit6.1810/l-fs1)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/lec/l-fs1.pdf /home/<USER>/xv6/docs/mit6.1810/lec/l-fs1)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/appel-li.pdf /home/<USER>/xv6/docs/public/assets/mit6.1810/appel-li)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/readings/appel-li.pdf /home/<USER>/xv6/docs/mit6.1810/readings/appel-li)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/riscv-calling.pdf /home/<USER>/xv6/docs/public/assets/mit6.1810/riscv-calling)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/readings/riscv-calling.pdf /home/<USER>/xv6/docs/mit6.1810/readings/riscv-calling)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/book-riscv-rev4.pdf /home/<USER>/xv6/docs/public/assets/mit6.1810/book-riscv-rev4)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/xv6/book-riscv-rev4.pdf /home/<USER>/xv6/docs/mit6.1810/xv6/book-riscv-rev4)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/book_zh.pdf /home/<USER>/xv6/docs/public/assets/mit6.1810/book_zh)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/xv6/book_zh.pdf /home/<USER>/xv6/docs/mit6.1810/xv6/book_zh)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/belay-dune /home/<USER>/xv6/docs/public/assets/mit6.1810/belay-dune.pdf)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/homework/belay-dune /home/<USER>/xv6/docs/mit6.1810/homework/belay-dune.pdf)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/l-fs2 /home/<USER>/xv6/docs/public/assets/mit6.1810/l-fs2.pdf)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/homework/l-fs2 /home/<USER>/xv6/docs/mit6.1810/homework/l-fs2.pdf)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/meltdown /home/<USER>/xv6/docs/public/assets/mit6.1810/meltdown.pdf)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/homework/meltdown /home/<USER>/xv6/docs/mit6.1810/homework/meltdown.pdf)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/microkernel /home/<USER>/xv6/docs/public/assets/mit6.1810/microkernel.pdf)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/homework/microkernel /home/<USER>/xv6/docs/mit6.1810/homework/microkernel.pdf)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/mogul96usenix /home/<USER>/xv6/docs/public/assets/mit6.1810/mogul96usenix.pdf)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/homework/mogul96usenix /home/<USER>/xv6/docs/mit6.1810/homework/mogul96usenix.pdf)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/rcu-decade-later /home/<USER>/xv6/docs/public/assets/mit6.1810/rcu-decade-later.pdf)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/homework/rcu-decade-later /home/<USER>/xv6/docs/mit6.1810/homework/rcu-decade-later.pdf)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/redleaf /home/<USER>/xv6/docs/public/assets/mit6.1810/redleaf.pdf)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/homework/redleaf /home/<USER>/xv6/docs/mit6.1810/homework/redleaf.pdf)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/16550 /home/<USER>/xv6/docs/public/assets/mit6.1810/16550.pdf)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/lec/16550 /home/<USER>/xv6/docs/mit6.1810/lec/16550.pdf)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/gdb_slides /home/<USER>/xv6/docs/public/assets/mit6.1810/gdb_slides.pdf)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/lec/gdb_slides /home/<USER>/xv6/docs/mit6.1810/lec/gdb_slides.pdf)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/l-c_slides /home/<USER>/xv6/docs/public/assets/mit6.1810/l-c_slides.pdf)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/lec/l-c/l-c_slides /home/<USER>/xv6/docs/mit6.1810/lec/l-c/l-c_slides.pdf)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/l-fs1 /home/<USER>/xv6/docs/public/assets/mit6.1810/l-fs1.pdf)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/lec/l-fs1 /home/<USER>/xv6/docs/mit6.1810/lec/l-fs1.pdf)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/appel-li /home/<USER>/xv6/docs/public/assets/mit6.1810/appel-li.pdf)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/readings/appel-li /home/<USER>/xv6/docs/mit6.1810/readings/appel-li.pdf)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/riscv-calling /home/<USER>/xv6/docs/public/assets/mit6.1810/riscv-calling.pdf)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/readings/riscv-calling /home/<USER>/xv6/docs/mit6.1810/readings/riscv-calling.pdf)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/book-riscv-rev4 /home/<USER>/xv6/docs/public/assets/mit6.1810/book-riscv-rev4.pdf)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/xv6/book-riscv-rev4 /home/<USER>/xv6/docs/mit6.1810/xv6/book-riscv-rev4.pdf)", "Bash(mv /home/<USER>/xv6/docs/public/assets/mit6.1810/book_zh /home/<USER>/xv6/docs/public/assets/mit6.1810/book_zh.pdf)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/xv6/book_zh /home/<USER>/xv6/docs/mit6.1810/xv6/book_zh.pdf)", "Bash(mv /home/<USER>/xv6/docs/mit6.1810/homework/l-crash.txt /home/<USER>/xv6/docs/mit6.1810/homework/l-crash.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/homework/l-journal.txt /home/<USER>/xv6/docs/mit6.1810/homework/l-journal.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/homework/l-uservm.txt /home/<USER>/xv6/docs/mit6.1810/homework/l-uservm.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/homework/uservm-faq.txt /home/<USER>/xv6/docs/mit6.1810/homework/uservm-faq.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/dune-faq.txt /home/<USER>/xv6/docs/mit6.1810/lec/dune-faq.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/l-QA1.txt /home/<USER>/xv6/docs/mit6.1810/lec/l-QA1.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/l-c.txt /home/<USER>/xv6/docs/mit6.1810/lec/l-c.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/l-fs.txt /home/<USER>/xv6/docs/mit6.1810/lec/l-fs.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/l-internal.txt /home/<USER>/xv6/docs/mit6.1810/lec/l-internal.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/l-interrupt.txt /home/<USER>/xv6/docs/mit6.1810/lec/l-interrupt.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/l-lockv2.txt /home/<USER>/xv6/docs/mit6.1810/lec/l-lockv2.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/l-meltdown.txt /home/<USER>/xv6/docs/mit6.1810/lec/l-meltdown.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/l-net.txt /home/<USER>/xv6/docs/mit6.1810/lec/l-net.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/l-organization.txt /home/<USER>/xv6/docs/mit6.1810/lec/l-organization.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/l-os.txt /home/<USER>/xv6/docs/mit6.1810/lec/l-os.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/l-overview.txt /home/<USER>/xv6/docs/mit6.1810/lec/l-overview.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/l-pgfaults.txt /home/<USER>/xv6/docs/mit6.1810/lec/l-pgfaults.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/l-rcu.txt /home/<USER>/xv6/docs/mit6.1810/lec/l-rcu.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/l-redleaf.txt /home/<USER>/xv6/docs/mit6.1810/lec/l-redleaf.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/l-riscv.txt /home/<USER>/xv6/docs/mit6.1810/lec/l-riscv.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/l-threads.txt /home/<USER>/xv6/docs/mit6.1810/lec/l-threads.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/l-vm.txt /home/<USER>/xv6/docs/mit6.1810/lec/l-vm.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/l-vmm.txt /home/<USER>/xv6/docs/mit6.1810/lec/l-vmm.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/l4-faq.txt /home/<USER>/xv6/docs/mit6.1810/lec/l4-faq.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/livelock-faq.txt /home/<USER>/xv6/docs/mit6.1810/lec/livelock-faq.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/meltdown-faq.txt /home/<USER>/xv6/docs/mit6.1810/lec/meltdown-faq.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/rcu-faq.txt /home/<USER>/xv6/docs/mit6.1810/lec/rcu-faq.md)", "Bash(__NEW_LINE__ mv /home/<USER>/xv6/docs/mit6.1810/lec/redleaf-faq.txt /home/<USER>/xv6/docs/mit6.1810/lec/redleaf-faq.md)", "Bash(find /home/<USER>/xv6/docs -type f -name \"*.md\" -exec sed -i 's/\\\\.txt/.md/g' {} +)", "Bash(FILE=\"/home/<USER>/xv6/docs/.vitepress/sidebar-mit6.1810.js\")", "Bash(__NEW_LINE__ sed -i 's/\\\\.txt\\\\.pdf/.md/g' \"$FILE\")", "Bash(__NEW_LINE__ sed -i 's/\\\\.md\\\\.pdf/.md/g' \"$FILE\")", "Bash(__NEW_LINE__ sed -i 's/\\\\.c\\\\.pdf/.c/g' \"$FILE\")", "Bash(__NEW_LINE__ sed -i 's/\\\\.pdf//g' \"$FILE\")", "Bash(git -C xv6-riscv status)", "Bash(sed -i 's/\\\\.txt\\\\.pdf/.md/g' \"$FILE\")", "Bash(sed -i 's/\\\\.md\\\\.pdf/.md/g' \"$FILE\")", "Bash(sed -i 's/\\\\.c\\\\.pdf/.c/g' \"$FILE\")", "Bash(sed -i 's/\\\\.pdf//g' \"$FILE\")"], "deny": []}}