### 前言和致谢

这是一份为操作系统课编写的教学草案。它通过研究一个名为 xv6 的操作系统内核来解释操作系统中的主要概念。xv6 是 <PERSON> Ritchie 和 Ken Thompson 合著的 Unix Version 6（v6）操作系统的重新实现。xv6 在一定程度上遵守 v6 的结构和风格，但它是用 ANSI C 实现的，并且是基于 x86 多核处理器的。

这本教材应该和 xv6 源代码一起阅读，这是 John Lion 在 Unix 6th Edition（Peer to Peer Communications；ISBN：1-57398-013-7；第一版（2000年7月14日）的评注中推荐的学习方式。 参见[http://pdos.csail.mit.edu/6.828](http://pdos.csail.mit.edu/6.828) 上有关于 v6 和 xv6 的资料。

我们已经在 6.828 —— MIT 的操作系统课程中使用了这本教材。我们向参与 6.828 的教职员工、助教和学生表示感谢，他们都直接或间接向 xv6 做出了贡献。此处我们要特别感谢 Austin Clements 和 <PERSON>。
