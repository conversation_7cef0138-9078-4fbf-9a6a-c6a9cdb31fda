{"name": "docs", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "docs:prepare": "node scripts/prepare-source-docs.cjs", "docs:dev": "pnpm docs:prepare && vitepress dev .", "docs:build": "pnpm docs:prepare && vitepress build .", "docs:preview": "vitepress preview d"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "devDependencies": {"cytoscape": "^3.33.0", "cytoscape-cose-bilkent": "^4.1.0", "dayjs": "^1.11.13", "debug": "^4.4.1", "mermaid": "^11.9.0", "vitepress": "^1.6.4", "vitepress-plugin-llms": "^1.7.2", "vitepress-plugin-mermaid": "^2.0.17", "vue": "^3.5.18"}, "pnpm": {"overrides": {"@braintree/sanitize-url": "6.0.4"}}, "dependencies": {"fs-extra": "^11.3.1", "markdown-it": "^14.1.0", "markdown-it-footnote": "^4.0.0", "shiki": "^3.9.2"}}