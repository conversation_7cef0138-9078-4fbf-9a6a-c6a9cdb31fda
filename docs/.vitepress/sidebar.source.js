export default [
  {
    "text": "xv6-riscv 源码",
    "items": [
      {
        "text": ".git",
        "collapsed": true,
        "items": [
          {
            "text": "hooks",
            "collapsed": true,
            "items": []
          },
          {
            "text": "info",
            "collapsed": true,
            "items": []
          },
          {
            "text": "logs",
            "collapsed": true,
            "items": [
              {
                "text": "refs",
                "collapsed": true,
                "items": [
                  {
                    "text": "heads",
                    "collapsed": true,
                    "items": []
                  },
                  {
                    "text": "remotes",
                    "collapsed": true,
                    "items": [
                      {
                        "text": "origin",
                        "collapsed": true,
                        "items": []
                      }
                    ]
                  }
                ]
              }
            ]
          },
          {
            "text": "objects",
            "collapsed": true,
            "items": [
              {
                "text": "01",
                "collapsed": true,
                "items": []
              },
              {
                "text": "03",
                "collapsed": true,
                "items": []
              },
              {
                "text": "04",
                "collapsed": true,
                "items": []
              },
              {
                "text": "05",
                "collapsed": true,
                "items": []
              },
              {
                "text": "06",
                "collapsed": true,
                "items": []
              },
              {
                "text": "08",
                "collapsed": true,
                "items": []
              },
              {
                "text": "09",
                "collapsed": true,
                "items": []
              },
              {
                "text": "0a",
                "collapsed": true,
                "items": []
              },
              {
                "text": "0d",
                "collapsed": true,
                "items": []
              },
              {
                "text": "0e",
                "collapsed": true,
                "items": []
              },
              {
                "text": "0f",
                "collapsed": true,
                "items": []
              },
              {
                "text": "10",
                "collapsed": true,
                "items": []
              },
              {
                "text": "11",
                "collapsed": true,
                "items": []
              },
              {
                "text": "12",
                "collapsed": true,
                "items": []
              },
              {
                "text": "15",
                "collapsed": true,
                "items": []
              },
              {
                "text": "16",
                "collapsed": true,
                "items": []
              },
              {
                "text": "1b",
                "collapsed": true,
                "items": []
              },
              {
                "text": "1d",
                "collapsed": true,
                "items": []
              },
              {
                "text": "1f",
                "collapsed": true,
                "items": []
              },
              {
                "text": "22",
                "collapsed": true,
                "items": []
              },
              {
                "text": "25",
                "collapsed": true,
                "items": []
              },
              {
                "text": "29",
                "collapsed": true,
                "items": []
              },
              {
                "text": "2a",
                "collapsed": true,
                "items": []
              },
              {
                "text": "2b",
                "collapsed": true,
                "items": []
              },
              {
                "text": "2c",
                "collapsed": true,
                "items": []
              },
              {
                "text": "2d",
                "collapsed": true,
                "items": []
              },
              {
                "text": "2f",
                "collapsed": true,
                "items": []
              },
              {
                "text": "30",
                "collapsed": true,
                "items": []
              },
              {
                "text": "31",
                "collapsed": true,
                "items": []
              },
              {
                "text": "32",
                "collapsed": true,
                "items": []
              },
              {
                "text": "33",
                "collapsed": true,
                "items": []
              },
              {
                "text": "34",
                "collapsed": true,
                "items": []
              },
              {
                "text": "37",
                "collapsed": true,
                "items": []
              },
              {
                "text": "39",
                "collapsed": true,
                "items": []
              },
              {
                "text": "3b",
                "collapsed": true,
                "items": []
              },
              {
                "text": "3d",
                "collapsed": true,
                "items": []
              },
              {
                "text": "3e",
                "collapsed": true,
                "items": []
              },
              {
                "text": "40",
                "collapsed": true,
                "items": []
              },
              {
                "text": "42",
                "collapsed": true,
                "items": []
              },
              {
                "text": "46",
                "collapsed": true,
                "items": []
              },
              {
                "text": "4d",
                "collapsed": true,
                "items": []
              },
              {
                "text": "50",
                "collapsed": true,
                "items": []
              },
              {
                "text": "53",
                "collapsed": true,
                "items": []
              },
              {
                "text": "57",
                "collapsed": true,
                "items": []
              },
              {
                "text": "58",
                "collapsed": true,
                "items": []
              },
              {
                "text": "5a",
                "collapsed": true,
                "items": []
              },
              {
                "text": "5b",
                "collapsed": true,
                "items": []
              },
              {
                "text": "5d",
                "collapsed": true,
                "items": []
              },
              {
                "text": "5f",
                "collapsed": true,
                "items": []
              },
              {
                "text": "61",
                "collapsed": true,
                "items": []
              },
              {
                "text": "63",
                "collapsed": true,
                "items": []
              },
              {
                "text": "66",
                "collapsed": true,
                "items": []
              },
              {
                "text": "6a",
                "collapsed": true,
                "items": []
              },
              {
                "text": "6b",
                "collapsed": true,
                "items": []
              },
              {
                "text": "6c",
                "collapsed": true,
                "items": []
              },
              {
                "text": "6d",
                "collapsed": true,
                "items": []
              },
              {
                "text": "6f",
                "collapsed": true,
                "items": []
              },
              {
                "text": "72",
                "collapsed": true,
                "items": []
              },
              {
                "text": "76",
                "collapsed": true,
                "items": []
              },
              {
                "text": "77",
                "collapsed": true,
                "items": []
              },
              {
                "text": "78",
                "collapsed": true,
                "items": []
              },
              {
                "text": "79",
                "collapsed": true,
                "items": []
              },
              {
                "text": "7c",
                "collapsed": true,
                "items": []
              },
              {
                "text": "7d",
                "collapsed": true,
                "items": []
              },
              {
                "text": "7e",
                "collapsed": true,
                "items": []
              },
              {
                "text": "80",
                "collapsed": true,
                "items": []
              },
              {
                "text": "81",
                "collapsed": true,
                "items": []
              },
              {
                "text": "87",
                "collapsed": true,
                "items": []
              },
              {
                "text": "88",
                "collapsed": true,
                "items": []
              },
              {
                "text": "89",
                "collapsed": true,
                "items": []
              },
              {
                "text": "8d",
                "collapsed": true,
                "items": []
              },
              {
                "text": "8e",
                "collapsed": true,
                "items": []
              },
              {
                "text": "90",
                "collapsed": true,
                "items": []
              },
              {
                "text": "92",
                "collapsed": true,
                "items": []
              },
              {
                "text": "93",
                "collapsed": true,
                "items": []
              },
              {
                "text": "97",
                "collapsed": true,
                "items": []
              },
              {
                "text": "99",
                "collapsed": true,
                "items": []
              },
              {
                "text": "9a",
                "collapsed": true,
                "items": []
              },
              {
                "text": "9c",
                "collapsed": true,
                "items": []
              },
              {
                "text": "9e",
                "collapsed": true,
                "items": []
              },
              {
                "text": "9f",
                "collapsed": true,
                "items": []
              },
              {
                "text": "a0",
                "collapsed": true,
                "items": []
              },
              {
                "text": "a2",
                "collapsed": true,
                "items": []
              },
              {
                "text": "a3",
                "collapsed": true,
                "items": []
              },
              {
                "text": "a7",
                "collapsed": true,
                "items": []
              },
              {
                "text": "a9",
                "collapsed": true,
                "items": []
              },
              {
                "text": "ab",
                "collapsed": true,
                "items": []
              },
              {
                "text": "af",
                "collapsed": true,
                "items": []
              },
              {
                "text": "b1",
                "collapsed": true,
                "items": []
              },
              {
                "text": "b3",
                "collapsed": true,
                "items": []
              },
              {
                "text": "b5",
                "collapsed": true,
                "items": []
              },
              {
                "text": "b6",
                "collapsed": true,
                "items": []
              },
              {
                "text": "b7",
                "collapsed": true,
                "items": []
              },
              {
                "text": "b9",
                "collapsed": true,
                "items": []
              },
              {
                "text": "ba",
                "collapsed": true,
                "items": []
              },
              {
                "text": "bb",
                "collapsed": true,
                "items": []
              },
              {
                "text": "bd",
                "collapsed": true,
                "items": []
              },
              {
                "text": "be",
                "collapsed": true,
                "items": []
              },
              {
                "text": "bf",
                "collapsed": true,
                "items": []
              },
              {
                "text": "c0",
                "collapsed": true,
                "items": []
              },
              {
                "text": "c1",
                "collapsed": true,
                "items": []
              },
              {
                "text": "c5",
                "collapsed": true,
                "items": []
              },
              {
                "text": "c8",
                "collapsed": true,
                "items": []
              },
              {
                "text": "c9",
                "collapsed": true,
                "items": []
              },
              {
                "text": "ca",
                "collapsed": true,
                "items": []
              },
              {
                "text": "cd",
                "collapsed": true,
                "items": []
              },
              {
                "text": "ce",
                "collapsed": true,
                "items": []
              },
              {
                "text": "cf",
                "collapsed": true,
                "items": []
              },
              {
                "text": "d3",
                "collapsed": true,
                "items": []
              },
              {
                "text": "d5",
                "collapsed": true,
                "items": []
              },
              {
                "text": "d6",
                "collapsed": true,
                "items": []
              },
              {
                "text": "d7",
                "collapsed": true,
                "items": []
              },
              {
                "text": "dc",
                "collapsed": true,
                "items": []
              },
              {
                "text": "dd",
                "collapsed": true,
                "items": []
              },
              {
                "text": "de",
                "collapsed": true,
                "items": []
              },
              {
                "text": "e0",
                "collapsed": true,
                "items": []
              },
              {
                "text": "e1",
                "collapsed": true,
                "items": []
              },
              {
                "text": "e2",
                "collapsed": true,
                "items": []
              },
              {
                "text": "e4",
                "collapsed": true,
                "items": []
              },
              {
                "text": "e5",
                "collapsed": true,
                "items": []
              },
              {
                "text": "e8",
                "collapsed": true,
                "items": []
              },
              {
                "text": "e9",
                "collapsed": true,
                "items": []
              },
              {
                "text": "ed",
                "collapsed": true,
                "items": []
              },
              {
                "text": "ee",
                "collapsed": true,
                "items": []
              },
              {
                "text": "f0",
                "collapsed": true,
                "items": []
              },
              {
                "text": "f2",
                "collapsed": true,
                "items": []
              },
              {
                "text": "f3",
                "collapsed": true,
                "items": []
              },
              {
                "text": "f5",
                "collapsed": true,
                "items": []
              },
              {
                "text": "f6",
                "collapsed": true,
                "items": []
              },
              {
                "text": "f7",
                "collapsed": true,
                "items": []
              },
              {
                "text": "f8",
                "collapsed": true,
                "items": []
              },
              {
                "text": "fa",
                "collapsed": true,
                "items": []
              },
              {
                "text": "fc",
                "collapsed": true,
                "items": []
              },
              {
                "text": "fd",
                "collapsed": true,
                "items": []
              },
              {
                "text": "ff",
                "collapsed": true,
                "items": []
              },
              {
                "text": "info",
                "collapsed": true,
                "items": []
              },
              {
                "text": "pack",
                "collapsed": true,
                "items": []
              }
            ]
          },
          {
            "text": "refs",
            "collapsed": true,
            "items": [
              {
                "text": "heads",
                "collapsed": true,
                "items": []
              },
              {
                "text": "remotes",
                "collapsed": true,
                "items": [
                  {
                    "text": "origin",
                    "collapsed": true,
                    "items": []
                  }
                ]
              },
              {
                "text": "tags",
                "collapsed": true,
                "items": []
              }
            ]
          }
        ]
      },
      {
        "text": "kernel",
        "collapsed": true,
        "items": [
          {
            "text": "bio.c",
            "link": "/source/xv6-riscv/kernel/bio.c"
          },
          {
            "text": "buf.h",
            "link": "/source/xv6-riscv/kernel/buf.h"
          },
          {
            "text": "console.c",
            "link": "/source/xv6-riscv/kernel/console.c"
          },
          {
            "text": "defs.h",
            "link": "/source/xv6-riscv/kernel/defs.h"
          },
          {
            "text": "elf.h",
            "link": "/source/xv6-riscv/kernel/elf.h"
          },
          {
            "text": "entry.S",
            "link": "/source/xv6-riscv/kernel/entry.S"
          },
          {
            "text": "exec.c",
            "link": "/source/xv6-riscv/kernel/exec.c"
          },
          {
            "text": "fcntl.h",
            "link": "/source/xv6-riscv/kernel/fcntl.h"
          },
          {
            "text": "file.c",
            "link": "/source/xv6-riscv/kernel/file.c"
          },
          {
            "text": "file.h",
            "link": "/source/xv6-riscv/kernel/file.h"
          },
          {
            "text": "fs.c",
            "link": "/source/xv6-riscv/kernel/fs.c"
          },
          {
            "text": "fs.h",
            "link": "/source/xv6-riscv/kernel/fs.h"
          },
          {
            "text": "kalloc.c",
            "link": "/source/xv6-riscv/kernel/kalloc.c"
          },
          {
            "text": "kernel.ld",
            "link": "/source/xv6-riscv/kernel/kernel.ld"
          },
          {
            "text": "kernelvec.S",
            "link": "/source/xv6-riscv/kernel/kernelvec.S"
          },
          {
            "text": "log.c",
            "link": "/source/xv6-riscv/kernel/log.c"
          },
          {
            "text": "main.c",
            "link": "/source/xv6-riscv/kernel/main.c"
          },
          {
            "text": "memlayout.h",
            "link": "/source/xv6-riscv/kernel/memlayout.h"
          },
          {
            "text": "param.h",
            "link": "/source/xv6-riscv/kernel/param.h"
          },
          {
            "text": "pipe.c",
            "link": "/source/xv6-riscv/kernel/pipe.c"
          },
          {
            "text": "plic.c",
            "link": "/source/xv6-riscv/kernel/plic.c"
          },
          {
            "text": "power.c",
            "link": "/source/xv6-riscv/kernel/power.c"
          },
          {
            "text": "printf.c",
            "link": "/source/xv6-riscv/kernel/printf.c"
          },
          {
            "text": "proc.c",
            "link": "/source/xv6-riscv/kernel/proc.c"
          },
          {
            "text": "proc.h",
            "link": "/source/xv6-riscv/kernel/proc.h"
          },
          {
            "text": "riscv.h",
            "link": "/source/xv6-riscv/kernel/riscv.h"
          },
          {
            "text": "sleeplock.c",
            "link": "/source/xv6-riscv/kernel/sleeplock.c"
          },
          {
            "text": "sleeplock.h",
            "link": "/source/xv6-riscv/kernel/sleeplock.h"
          },
          {
            "text": "spinlock.c",
            "link": "/source/xv6-riscv/kernel/spinlock.c"
          },
          {
            "text": "spinlock.h",
            "link": "/source/xv6-riscv/kernel/spinlock.h"
          },
          {
            "text": "start.c",
            "link": "/source/xv6-riscv/kernel/start.c"
          },
          {
            "text": "stat.h",
            "link": "/source/xv6-riscv/kernel/stat.h"
          },
          {
            "text": "string.c",
            "link": "/source/xv6-riscv/kernel/string.c"
          },
          {
            "text": "swtch.S",
            "link": "/source/xv6-riscv/kernel/swtch.S"
          },
          {
            "text": "syscall.c",
            "link": "/source/xv6-riscv/kernel/syscall.c"
          },
          {
            "text": "syscall.h",
            "link": "/source/xv6-riscv/kernel/syscall.h"
          },
          {
            "text": "sysfile.c",
            "link": "/source/xv6-riscv/kernel/sysfile.c"
          },
          {
            "text": "sysproc.c",
            "link": "/source/xv6-riscv/kernel/sysproc.c"
          },
          {
            "text": "trampoline.S",
            "link": "/source/xv6-riscv/kernel/trampoline.S"
          },
          {
            "text": "trap.c",
            "link": "/source/xv6-riscv/kernel/trap.c"
          },
          {
            "text": "types.h",
            "link": "/source/xv6-riscv/kernel/types.h"
          },
          {
            "text": "uart.c",
            "link": "/source/xv6-riscv/kernel/uart.c"
          },
          {
            "text": "version.c",
            "link": "/source/xv6-riscv/kernel/version.c"
          },
          {
            "text": "virtio_disk.c",
            "link": "/source/xv6-riscv/kernel/virtio_disk.c"
          },
          {
            "text": "virtio.h",
            "link": "/source/xv6-riscv/kernel/virtio.h"
          },
          {
            "text": "vm.c",
            "link": "/source/xv6-riscv/kernel/vm.c"
          }
        ]
      },
      {
        "text": "mkfs",
        "collapsed": true,
        "items": [
          {
            "text": "mkfs.c",
            "link": "/source/xv6-riscv/mkfs/mkfs.c"
          }
        ]
      },
      {
        "text": "user",
        "collapsed": true,
        "items": [
          {
            "text": "cat.c",
            "link": "/source/xv6-riscv/user/cat.c"
          },
          {
            "text": "clear.c",
            "link": "/source/xv6-riscv/user/clear.c"
          },
          {
            "text": "echo.c",
            "link": "/source/xv6-riscv/user/echo.c"
          },
          {
            "text": "forktest.c",
            "link": "/source/xv6-riscv/user/forktest.c"
          },
          {
            "text": "grep.c",
            "link": "/source/xv6-riscv/user/grep.c"
          },
          {
            "text": "grind.c",
            "link": "/source/xv6-riscv/user/grind.c"
          },
          {
            "text": "init.c",
            "link": "/source/xv6-riscv/user/init.c"
          },
          {
            "text": "initcode.S",
            "link": "/source/xv6-riscv/user/initcode.S"
          },
          {
            "text": "kill.c",
            "link": "/source/xv6-riscv/user/kill.c"
          },
          {
            "text": "ln.c",
            "link": "/source/xv6-riscv/user/ln.c"
          },
          {
            "text": "ls.c",
            "link": "/source/xv6-riscv/user/ls.c"
          },
          {
            "text": "mkdir.c",
            "link": "/source/xv6-riscv/user/mkdir.c"
          },
          {
            "text": "printf.c",
            "link": "/source/xv6-riscv/user/printf.c"
          },
          {
            "text": "reboot.c",
            "link": "/source/xv6-riscv/user/reboot.c"
          },
          {
            "text": "rm.c",
            "link": "/source/xv6-riscv/user/rm.c"
          },
          {
            "text": "sh.c",
            "link": "/source/xv6-riscv/user/sh.c"
          },
          {
            "text": "shutdown.c",
            "link": "/source/xv6-riscv/user/shutdown.c"
          },
          {
            "text": "sleep.c",
            "link": "/source/xv6-riscv/user/sleep.c"
          },
          {
            "text": "stressfs.c",
            "link": "/source/xv6-riscv/user/stressfs.c"
          },
          {
            "text": "ulib.c",
            "link": "/source/xv6-riscv/user/ulib.c"
          },
          {
            "text": "umalloc.c",
            "link": "/source/xv6-riscv/user/umalloc.c"
          },
          {
            "text": "user.h",
            "link": "/source/xv6-riscv/user/user.h"
          },
          {
            "text": "user.ld",
            "link": "/source/xv6-riscv/user/user.ld"
          },
          {
            "text": "usertests.c",
            "link": "/source/xv6-riscv/user/usertests.c"
          },
          {
            "text": "usys.S",
            "link": "/source/xv6-riscv/user/usys.S"
          },
          {
            "text": "wc.c",
            "link": "/source/xv6-riscv/user/wc.c"
          },
          {
            "text": "zombie.c",
            "link": "/source/xv6-riscv/user/zombie.c"
          }
        ]
      },
      {
        "text": "Makefile",
        "link": "/source/xv6-riscv/Makefile"
      }
    ],
    "collapsible": true,
    "collapsed": false
  }
];