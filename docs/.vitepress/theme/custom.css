/**
 * Custom CSS for VitePress
 */

/*
  The following code is a workaround for a bug in VitePress where the dark theme for code blocks is not applied correctly.
  It forces the background and text colors to the correct values when the dark theme is active.
*/


html.dark .shiki,
html.dark .shiki span {
  background-color: var(--shiki-dark-bg, #121212) !important;
  color: var(--shiki-dark, #dbd7ca) !important;
}

.custom-comment-tip {
  padding: 0.1rem 1.5rem;
  border-radius: 8px;
  border-left: 5px solid #42b883;
  background-color: #f3f5f7;
  margin: 1rem 0;
  color: #606266;
}

html.dark .custom-comment-tip {
    background-color: #2e2e2e;
    color: #c7c7c7;
    border-left-color: #33a373;
}



div[class*="language-"] {
  position: relative;
}

.line {
    position: relative;
}


#floating-tooltip {
    position: absolute;
    background: var(--vp-c-bg-soft);
    border: 1px solid var(--vp-c-border);
    padding: 12px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    z-index: 1001;
    max-width: 400px;
    display: none;
    pointer-events: none;
    transition: opacity 0.2s;
}

#floating-tooltip.active {
    display: block;
    pointer-events: all;
    opacity: 1;
}


.function-call {
    cursor: pointer;
    text-decoration: underline;
    color: #0066cc !important;
    transition: color 0.2s;
}

.function-call:hover {
    color: #004499 !important;
    text-decoration: underline;
}

html.dark .function-call {
    color: #4da6ff !important;
}

html.dark .function-call:hover {
    color: #66b3ff !important;
}


/* 同行注释样式 */
.with-inline-comment {
    position: relative;
    cursor: help;
}

.with-inline-comment::after {
    content: " 💬";
    opacity: 0.6;
    font-size: 0.8em;
    color: #666;
    margin-left: 4px;
}

html.dark .with-inline-comment::after {
    color: #999;
}

.with-inline-comment:hover::after {
    opacity: 1;
}

/* 行注释tooltip */
#inline-comment-tooltip {
    position: absolute;
    background: var(--vp-c-bg-soft);
    border: 1px solid var(--vp-c-border);
    padding: 8px 12px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 1002;
    max-width: 300px;
    display: none;
    pointer-events: none;
    font-size: 0.9em;
    line-height: 1.4;
    white-space: pre-wrap;
}

#inline-comment-tooltip.active {
    display: block;
    opacity: 1;
}

/* 去除旧的代码注释容器样式 */
.code-comment-container {
    display: none;
}

pre, code {
  white-space: pre-wrap !important;
  overflow-wrap: break-word !important;
}