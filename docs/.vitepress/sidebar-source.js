export default [
  {
    "text": "kernel",
    "collapsed": true,
    "items": [
      {
        "text": "bio.c",
        "link": "/source/xv6-riscv/kernel/bio.c"
      },
      {
        "text": "buf.h",
        "link": "/source/xv6-riscv/kernel/buf.h"
      },
      {
        "text": "console.c",
        "link": "/source/xv6-riscv/kernel/console.c"
      },
      {
        "text": "defs.h",
        "link": "/source/xv6-riscv/kernel/defs.h"
      },
      {
        "text": "elf.h",
        "link": "/source/xv6-riscv/kernel/elf.h"
      },
      {
        "text": "entry.S",
        "link": "/source/xv6-riscv/kernel/entry.S"
      },
      {
        "text": "exec.c",
        "link": "/source/xv6-riscv/kernel/exec.c"
      },
      {
        "text": "fcntl.h",
        "link": "/source/xv6-riscv/kernel/fcntl.h"
      },
      {
        "text": "file.c",
        "link": "/source/xv6-riscv/kernel/file.c"
      },
      {
        "text": "file.h",
        "link": "/source/xv6-riscv/kernel/file.h"
      },
      {
        "text": "fs.c",
        "link": "/source/xv6-riscv/kernel/fs.c"
      },
      {
        "text": "fs.h",
        "link": "/source/xv6-riscv/kernel/fs.h"
      },
      {
        "text": "kalloc.c",
        "link": "/source/xv6-riscv/kernel/kalloc.c"
      },
      {
        "text": "kernel.ld",
        "link": "/source/xv6-riscv/kernel/kernel.ld"
      },
      {
        "text": "kernelvec.S",
        "link": "/source/xv6-riscv/kernel/kernelvec.S"
      },
      {
        "text": "log.c",
        "link": "/source/xv6-riscv/kernel/log.c"
      },
      {
        "text": "main.c",
        "link": "/source/xv6-riscv/kernel/main.c"
      },
      {
        "text": "memlayout.h",
        "link": "/source/xv6-riscv/kernel/memlayout.h"
      },
      {
        "text": "param.h",
        "link": "/source/xv6-riscv/kernel/param.h"
      },
      {
        "text": "pipe.c",
        "link": "/source/xv6-riscv/kernel/pipe.c"
      },
      {
        "text": "plic.c",
        "link": "/source/xv6-riscv/kernel/plic.c"
      },
      {
        "text": "power.c",
        "link": "/source/xv6-riscv/kernel/power.c"
      },
      {
        "text": "printf.c",
        "link": "/source/xv6-riscv/kernel/printf.c"
      },
      {
        "text": "proc.c",
        "link": "/source/xv6-riscv/kernel/proc.c"
      },
      {
        "text": "proc.h",
        "link": "/source/xv6-riscv/kernel/proc.h"
      },
      {
        "text": "riscv.h",
        "link": "/source/xv6-riscv/kernel/riscv.h"
      },
      {
        "text": "sleeplock.c",
        "link": "/source/xv6-riscv/kernel/sleeplock.c"
      },
      {
        "text": "sleeplock.h",
        "link": "/source/xv6-riscv/kernel/sleeplock.h"
      },
      {
        "text": "spinlock.c",
        "link": "/source/xv6-riscv/kernel/spinlock.c"
      },
      {
        "text": "spinlock.h",
        "link": "/source/xv6-riscv/kernel/spinlock.h"
      },
      {
        "text": "start.c",
        "link": "/source/xv6-riscv/kernel/start.c"
      },
      {
        "text": "stat.h",
        "link": "/source/xv6-riscv/kernel/stat.h"
      },
      {
        "text": "string.c",
        "link": "/source/xv6-riscv/kernel/string.c"
      },
      {
        "text": "swtch.S",
        "link": "/source/xv6-riscv/kernel/swtch.S"
      },
      {
        "text": "syscall.c",
        "link": "/source/xv6-riscv/kernel/syscall.c"
      },
      {
        "text": "syscall.h",
        "link": "/source/xv6-riscv/kernel/syscall.h"
      },
      {
        "text": "sysfile.c",
        "link": "/source/xv6-riscv/kernel/sysfile.c"
      },
      {
        "text": "sysproc.c",
        "link": "/source/xv6-riscv/kernel/sysproc.c"
      },
      {
        "text": "trampoline.S",
        "link": "/source/xv6-riscv/kernel/trampoline.S"
      },
      {
        "text": "trap.c",
        "link": "/source/xv6-riscv/kernel/trap.c"
      },
      {
        "text": "types.h",
        "link": "/source/xv6-riscv/kernel/types.h"
      },
      {
        "text": "uart.c",
        "link": "/source/xv6-riscv/kernel/uart.c"
      },
      {
        "text": "virtio_disk.c",
        "link": "/source/xv6-riscv/kernel/virtio_disk.c"
      },
      {
        "text": "virtio.h",
        "link": "/source/xv6-riscv/kernel/virtio.h"
      },
      {
        "text": "vm.c",
        "link": "/source/xv6-riscv/kernel/vm.c"
      }
    ]
  },
  {
    "text": "mkfs",
    "collapsed": true,
    "items": [
      {
        "text": "mkfs.c",
        "link": "/source/xv6-riscv/mkfs/mkfs.c"
      }
    ]
  },
  {
    "text": "user",
    "collapsed": true,
    "items": [
      {
        "text": "cat.c",
        "link": "/source/xv6-riscv/user/cat.c"
      },
      {
        "text": "clear.c",
        "link": "/source/xv6-riscv/user/clear.c"
      },
      {
        "text": "echo.c",
        "link": "/source/xv6-riscv/user/echo.c"
      },
      {
        "text": "forktest.c",
        "link": "/source/xv6-riscv/user/forktest.c"
      },
      {
        "text": "grep.c",
        "link": "/source/xv6-riscv/user/grep.c"
      },
      {
        "text": "grind.c",
        "link": "/source/xv6-riscv/user/grind.c"
      },
      {
        "text": "init.c",
        "link": "/source/xv6-riscv/user/init.c"
      },
      {
        "text": "initcode.S",
        "link": "/source/xv6-riscv/user/initcode.S"
      },
      {
        "text": "kill.c",
        "link": "/source/xv6-riscv/user/kill.c"
      },
      {
        "text": "ln.c",
        "link": "/source/xv6-riscv/user/ln.c"
      },
      {
        "text": "ls.c",
        "link": "/source/xv6-riscv/user/ls.c"
      },
      {
        "text": "mkdir.c",
        "link": "/source/xv6-riscv/user/mkdir.c"
      },
      {
        "text": "printf.c",
        "link": "/source/xv6-riscv/user/printf.c"
      },
      {
        "text": "reboot.c",
        "link": "/source/xv6-riscv/user/reboot.c"
      },
      {
        "text": "rm.c",
        "link": "/source/xv6-riscv/user/rm.c"
      },
      {
        "text": "sh.c",
        "link": "/source/xv6-riscv/user/sh.c"
      },
      {
        "text": "shutdown.c",
        "link": "/source/xv6-riscv/user/shutdown.c"
      },
      {
        "text": "sleep.c",
        "link": "/source/xv6-riscv/user/sleep.c"
      },
      {
        "text": "stressfs.c",
        "link": "/source/xv6-riscv/user/stressfs.c"
      },
      {
        "text": "ulib.c",
        "link": "/source/xv6-riscv/user/ulib.c"
      },
      {
        "text": "umalloc.c",
        "link": "/source/xv6-riscv/user/umalloc.c"
      },
      {
        "text": "user.h",
        "link": "/source/xv6-riscv/user/user.h"
      },
      {
        "text": "user.ld",
        "link": "/source/xv6-riscv/user/user.ld"
      },
      {
        "text": "usertests.c",
        "link": "/source/xv6-riscv/user/usertests.c"
      },
      {
        "text": "usys.S",
        "link": "/source/xv6-riscv/user/usys.S"
      },
      {
        "text": "wc.c",
        "link": "/source/xv6-riscv/user/wc.c"
      },
      {
        "text": "zombie.c",
        "link": "/source/xv6-riscv/user/zombie.c"
      }
    ]
  },
  {
    "text": "Makefile",
    "link": "/source/xv6-riscv/Makefile"
  }
];