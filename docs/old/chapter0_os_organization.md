# 第 0 章: 操作系统组织

本章旨在介绍操作系统的核心组织结构与基本概念。学完本章，你将能够：

*   理解操作系统的三个基本角色：资源抽象、多路复用与隔离。
*   掌握用户模式与监管者模式的区别，以及它们对系统安全的重要性。
*   了解单体内核与微内核这两种主流的内核设计理念。
*   熟悉 xv6 操作系统的完整启动流程，以及第一个用户进程是如何被创建和执行的。

## 1. 操作系统的角色：抽象、多路复用与隔离

操作系统（OS）是计算机硬件和应用程序之间的桥梁。如果没有操作系统，应用程序将需要直接与复杂的硬件打交道，这不仅极其低效，而且充满了风险。一个设计良好的操作系统需要满足三个核心要求：

*   **多路复用 (Multiplexing)**: 计算机的资源（如 CPU、内存）是有限的。当多个程序需要同时运行时，操作系统必须在它们之间分时共享这些资源。例如，即使用户启动的进程数量超过了 CPU 核心数，操作系统也通过快速切换，让每个进程都有机会执行，创造出所有进程在同时运行的假象。
*   **隔离 (Isolation)**: 应用程序是不可信的，它们可能包含错误（bug）甚至恶意代码。操作系统必须在进程之间建立强大的隔离机制。如果一个进程因为自身的错误而崩溃，它不应该影响到其他正在运行的进程或操作系统内核本身。
*   **交互 (Interaction)**: 完全的隔离是不现实的，因为进程之间常常需要协作。例如，shell 通过管道（pipe）将一个命令的输出传递给另一个命令。因此，操作系统必须提供受控的、安全的交互机制。

为了同时实现这三个看似矛盾的目标，操作系统采用的核心策略是**抽象物理资源**。它将复杂的、底层的硬件资源（如磁盘、CPU、物理内存）封装成一系列更简单、更易于使用的服务，并通过一组称为**系统调用 (System Calls)** 的接口提供给应用程序。

*   **文件系统**: 应用程序通过 [`open`](/source/xv6-riscv/user/user.h.md), [`read`](/source/xv6-riscv/user/user.h.md), [`write`](/source/xv6-riscv/user/user.h.md) 等系统调用来操作文件，而无需关心数据具体存储在磁盘的哪个扇区。操作系统负责管理磁盘空间和文件结构。
*   **进程**: 操作系统将 CPU 抽象为进程，每个进程都感觉自己独占了整个 CPU。操作系统负责在不同进程间透明地保存和恢复 CPU 状态，实现分时复用。
*   **地址空间**: 操作系统为每个进程提供了一个私有的、连续的虚拟地址空间，使得进程无需关心数据在物理内存中的具体位置，同时也防止了它访问其他进程的内存。

通过这些抽象，操作系统将自己置于应用程序和硬件之间，从而获得了对系统资源的完全控制权，实现了有效的资源管理、隔离和受控交互。

## 2. 用户模式与监管者模式

要实现强隔离，仅靠软件层面的抽象是不够的，必须有硬件的支持。CPU 为此提供了不同的执行模式。RISC-V 架构定义了三种主要模式：

*   **机器模式 (Machine Mode)**: 权限最高的模式。CPU 在上电时进入此模式，主要用于配置计算机的核心硬件。xv6 在启动时会执行一小段机器模式代码，然后迅速切换到监管者模式。
*   **监管者模式 (Supervisor Mode)**: 操作系统内核运行在该模式下。在该模式下，CPU 可以执行一系列**特权指令 (privileged instructions)**，例如：
    *   启用或禁用中断。
    *   读写控制页表地址的寄存器（`satp`）。
    *   访问物理内存保护（PMP）相关的寄存器。
*   **用户模式 (User Mode)**: 应用程序运行在该模式下。在此模式下，程序可执行的指令受到严格限制。如果一个用户程序尝试执行一条特权指令，CPU 不会执行它，而是会触发一个**异常 (exception)**，强制将控制权切换到监管者模式。内核的异常处理程序会接管控制，通常会因为应用程序的非法操作而终止它。

这种硬件层面的区分是实现系统安全的基石。运行在用户模式的程序被称为在**用户空间 (user space)** 执行，而运行在监管者模式的软件（即内核）则在**内核空间 (kernel space)** 执行。

当用户程序需要执行一项需要特权的操作时（例如，读写文件），它不能直接调用内核中的函数，而必须通过一个特殊的指令——`ecall`——来“请求”内核的服务。`ecall` 指令会：
1.  将 CPU 从用户模式切换到监管者模式。
2.  跳转到内核预先设定好的唯一入口点（陷阱处理程序）。

内核接管控制后，会验证系统调用的参数是否合法，检查进程是否有权限执行该操作，然后代表该进程执行相应的服务。完成后，内核再通过 `sret` 指令将 CPU 切换回用户模式，并将控制权交还给应用程序。这种严格控制的转换过程确保了用户程序无法绕过内核的检查，从而保障了整个系统的稳定和安全。

## 3. 内核组织：单体内核 vs. 微内核

既然内核拥有最高权限，那么一个关键的设计问题就是：操作系统的哪些部分应该作为内核，在监管者模式下运行？对此，主要有两种设计哲学：

### 单体内核 (Monolithic Kernel)

在这种设计中，整个操作系统（包括文件系统、进程管理、内存管理、设备驱动等）都作为一个单一的、巨大的程序运行在内核空间。

*   **优点**:
    *   **高效**: 内核的不同子系统（如文件系统和虚拟内存系统）之间可以紧密集成，直接调用函数进行通信，性能非常高。
    *   **简单**: 设计者无需划分哪些功能需要特权，所有核心服务都在一个地址空间内。
*   **缺点**:
    *   **可靠性差**: 内核代码极其复杂，任何一个模块的 bug 都可能导致整个系统崩溃（内核恐慌, kernel panic）。
    *   **维护困难**: 模块间高度耦合，修改一个部分很可能引发意想不到的连锁反应。

包括 Linux、FreeBSD 以及我们学习的 xv6 在内的大多数 Unix-like 系统都采用了单体内核设计。

### 微内核 (Microkernel)

微内核的设计理念是，只将最核心、最基本的功能（如进程间通信（IPC）、基本的进程调度和内存管理）保留在内核中，而将其他大部分操作系统服务（如文件系统、网络协议栈、设备驱动等）都作为运行在用户空间的普通进程（称为**服务器, server**）来实现。

![微内核示意图](https://www.cs.princeton.edu/courses/archive/fall20/cos318/L1-Intro/mkernel.png)

*   **优点**:
    *   **可靠性高**: 如果文件系统服务器出现 bug 崩溃了，只会影响到文件相关的服务，而内核和其他服务进程可以继续运行，系统更加稳定。
    *   **灵活性和扩展性好**: 可以方便地替换或升级某个服务进程，而无需重新编译整个内核。
*   **缺点**:
    *   **性能较低**: 用户程序和服务器之间、以及服务器与服务器之间的通信都需要通过内核的 IPC 机制，这涉及多次用户态和内核态的切换，开销比单体内核中的函数调用大得多。

MINIX 3 和 QNX 是微内核设计的著名例子。尽管两种架构各有优劣，并且在现实世界中界限也逐渐模糊，但它们所涉及的核心技术（如系统调用、进程管理、内存管理等）是共通的。xv6 作为一个教学用的单体内核，能让我们很好地理解这些核心概念。

## 4. xv6 启动流程与第一个进程

现在，让我们跟随 xv6 的启动过程，具体看看它是如何从零开始，最终运行第一个用户程序的。

### 阶段一：机器模式启动 (entry.S -> start.c)

1.  **QEMU 加载内核**: 当我们使用 `qemu` 启动 xv6 时，QEMU 会将编译好的内核镜像加载到物理地址 `0x80000000` 处。
2.  **CPU 从 `_entry` 开始执行**: 所有 CPU 核心（hart）都会在机器模式下，从 [`kernel/entry.S`](/source/xv6-riscv/kernel/entry.S.md) 的 `_entry` 标签处开始执行。此时，分页机制是关闭的，虚拟地址直接等于物理地址。
3.  **设置初始栈**: `_entry` 的首要任务是为 C 代码的执行准备一个栈。它读取当前核心的 `mhartid`，计算出该核心在 `stack0` 数组中的专属栈空间，并将栈顶指针 `sp` 指向该区域的末尾（因为 RISC-V 的栈是向下增长的）。
    
```
assembly
    # kernel/entry.S
    _entry:
            # 为 C 代码设置一个栈。
            la sp, stack0
            li a0, 1024*4
            csrr a1, mhartid
            addi a1, a1, 1
            mul a0, a0, a1
            add sp, sp, a0
    
```

4.  **跳转到 [`start`](/source/xv6-riscv/user/ulib.c.md) 函数**: 栈设置好后，`_entry` 通过 `call` 指令跳转到 C 函数 [`start()`](/source/xv6-riscv/kernel/start.c.md)。
5.  **切换到监管者模式**: [`start()`](/source/xv6-riscv/kernel/start.c.md) 函数执行一系列只能在机器模式下完成的配置：
    *   设置 `mstatus` 寄存器，告诉 `mret` 指令下次要切换到监管者模式（S-mode）。
    *   将 [`main`](/source/xv6-riscv/user/zombie.c.md) 函数的地址写入 `mepc` 寄存器，作为 `mret` 的返回地址。
    *   通过将 `satp` 寄存器置零，暂时禁用 S-mode 下的虚拟地址转换。
    *   将所有中断和异常委托给 S-mode 处理。
    *   配置物理内存保护（PMP），允许 S-mode 访问所有物理内存。
    *   调用 [`timerinit()`](/source/xv6-riscv/kernel/start.c.md#timerinit-kernel-start-c) 初始化时钟中断。
    *   最后，执行 `mret` 指令。CPU 的特权级别降为监管者模式，并跳转到 `mepc` 中保存的地址——[`main`](/source/xv6-riscv/user/zombie.c.md) 函数。

### 阶段二：内核初始化 (main.c)

[`main`](/source/xv6-riscv/user/zombie.c.md) 函数是内核的 C 代码主入口。它由 hart 0（主 CPU）和其它 hart 并发执行。

1.  **主 CPU 初始化**: hart 0 负责执行一系列一次性的初始化工作：
    *   [`consoleinit()`](/source/xv6-riscv/kernel/console.c.md), [`printfinit()`](/source/xv6-riscv/kernel/printf.c.md): 初始化控制台和打印函数，这样内核才能输出信息。
    *   [`kinit()`](/source/xv6-riscv/kernel/kalloc.c.md): 初始化物理内存分配器。
    *   [`kvminit()`](/source/xv6-riscv/kernel/vm.c.md), [`kvminithart()`](/source/xv6-riscv/kernel/vm.c.md): 创建并启用内核页表，开启分页机制。
    *   [`procinit()`](/source/xv6-riscv/kernel/proc.c.md): 初始化进程表。
    *   [`trapinit()`](/source/xv6-riscv/kernel/trap.c.md), [`trapinithart()`](/source/xv6-riscv/kernel/trap.c.md): 初始化陷阱处理。
    *   ... 初始化其他各种设备和子系统 ...
    *   **[`userinit()`](/source/xv6-riscv/kernel/proc.c.md): 创建第一个用户进程！**
    *   最后，设置 `started` 标志为 1，通知其他 CPU 初始化已完成。
2.  **其他 CPU 等待**: 其他 hart 会在一个 `while` 循环中自旋，等待 `started` 标志变为 1。之后，它们会各自完成自己的 [`kvminithart()`](/source/xv6-riscv/kernel/vm.c.md#kvminithart-kernel-vm-c) 和 [`trapinithart()`](/source/xv6-riscv/kernel/trap.c.md#trapinithart-kernel-trap-c) 等初始化，然后和主 CPU 一样，进入调度器循环。
3.  **进入调度器**: 所有 CPU 在完成初始化后，都会调用 [`scheduler()`](/source/xv6-riscv/kernel/proc.c.md#scheduler-kernel-proc-c)，开始永不返回的进程调度循环。

### 阶段三：创建第一个进程 (proc.c -> initcode.S -> init.c)

这是整个启动流程的高潮部分。

1.  **[`userinit()`](/source/xv6-riscv/kernel/proc.c.md#userinit-kernel-proc-c) 创建进程**: [`main()`](/source/xv6-riscv/kernel/main.c.md) 调用的 [`userinit()`](/source/xv6-riscv/kernel/proc.c.md) 函数负责创建第一个进程，通常称为 `init` 进程。
    *   调用 [`allocproc()`](/source/xv6-riscv/kernel/proc.c.md#allocproc-kernel-proc-c) 分配一个 `proc` 结构体。
    *   调用 [`uvmfirst()`](/source/xv6-riscv/kernel/vm.c.md#uvmfirst-kernel-vm-c) 分配一页内存，并将一小段硬编码的二进制程序 `initcode` 复制进去。这段代码来自 [`user/initcode.S`](/source/xv6-riscv/user/initcode.S.md)。
    *   设置进程的陷阱帧（`trapframe`），使得当进程从内核态“返回”到用户态时，程序计数器 `epc` 指向地址 0，栈指针 `sp` 指向该页的最高地址。
    *   将进程状态设置为 `RUNNABLE`，表示它已经准备好被调度器执行了。

2.  **`initcode.S` 执行 [`exec`](/source/xv6-riscv/user/user.h.md)**: 当调度器第一次选中这个进程并运行时，它会从地址 0 开始执行 `initcode` 的代码。这段汇编代码非常短，只做一件事：执行 [`exec`](/source/xv6-riscv/user/user.h.md) 系统调用。
    
```
assembly
    # user/initcode.S
    start:
            la a0, init     # 参数1: 要执行的程序路径 "/init"
            la a1, argv     # 参数2: 命令行参数
            li a7, SYS_exec # 系统调用号
            ecall           # 陷入内核
    
```

    `ecall` 指令再次将控制权交给内核。

3.  **内核执行 [`exec`](/source/xv6-riscv/user/user.h.md)**: 内核的系统调用处理程序会找到 [`exec`](/source/xv6-riscv/user/user.h.md) 对应的实现 [`sys_exec`](/source/xv6-riscv/kernel/sysfile.c.md)。[`exec`](/source/xv6-riscv/user/user.h.md) 系统调用会用新的程序（这里是 `/init`，其代码在 [`user/init.c`](/source/xv6-riscv/user/init.c.md) 中）替换掉当前进程的内存和寄存器，然后返回用户空间。

4.  **`init.c` 运行**: 控制权返回用户空间后，进程开始执行 `/init` 程序的 [`main`](/source/xv6-riscv/user/zombie.c.md) 函数。[`init.c`](/source/xv6-riscv/user/init.c.md) 的主要工作是：
    *   确保控制台文件描述符被正确设置。
    *   在一个无限循环中，[`fork`](/source/xv6-riscv/user/user.h.md) 一个子进程。
    *   在子进程中，`exec("sh", ...)` 启动一个 shell。
    *   父进程（`init`）则调用 [`wait()`](/source/xv6-riscv/kernel/sysproc.c.md#wait-kernel-sysproc-c) 等待 shell 退出。如果 shell 意外退出，`init` 会再次循环，重新启动一个新的 shell，从而确保用户总能与系统交互。

至此，系统完全启动，第一个真正的用户交互程序——shell——已经运行起来，等待用户的输入。

---

## 实验要求

1.  向 xv6 添加一个系统调用，该系统调用可以返回当前系统中可用（空闲）物理内存的总字节数。
