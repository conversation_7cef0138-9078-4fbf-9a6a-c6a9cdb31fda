## 现实世界

像大多数操作系统一样，xv6 使用分页硬件进行内存保护和映射。大多数操作系统通过结合分页和缺页异常，对分页的使用比 xv6 复杂得多，我们将在第 4 章中讨论。

Xv6 因为内核使用虚拟地址和物理地址之间的直接映射，以及它假设在地址 0x80000000 处有物理 RAM（内核期望被加载到那里）而得以简化。这在 QEMU 中可行，但在真实硬件上却是个坏主意；真实硬件将 RAM 和设备放置在不可预测的物理地址，因此（例如）在 0x80000000 可能没有 RAM，而 xv6 期望能够存储内核。更严肃的内核设计利用页表将任意硬件物理内存布局转换为可预测的内核虚拟地址布局。

RISC-V 支持物理地址级别的保护，但 xv6 没有使用该功能。

在拥有大量内存的机器上，使用 RISC-V 对“超级页”的支持可能是有意义的。当物理内存较小时，小页面是有意义的，以便以细粒度进行分配和页面换出到磁盘。例如，如果一个程序只使用 8 千字节的内存，给它一个完整的 4 兆字节的超级页物理内存是浪费的。在拥有大量 RAM 的机器上，较大的页面更有意义，并且可以减少页表操作的开销。

xv6 内核缺乏一个类似 [`malloc`](/source/xv6-riscv/user/umalloc.c.md) 的分配器，可以为小对象提供内存，这使得内核无法使用需要动态分配的复杂数据结构。一个更复杂的内核可能会分配许多不同大小的小块，而不是（像在 xv6 中那样）只分配 4096 字节的块；一个真正的内核分配器需要处理小分配和大的分配。

内存分配是一个常年热门的话题，基本问题是有效利用有限的内存和为未知的未来请求做准备。如今，人们更关心速度而不是空间效率。