# 中断和设备驱动程序

驱动程序 (driver) 是操作系统中管理特定设备的代码：它配置设备硬件，告诉设备执行操作，处理由此产生的中断，并与可能正在等待设备 I/O 的进程进行交互。驱动程序代码可能很棘手，因为驱动程序与其管理的设备同时执行。此外，驱动程序必须了解设备的硬件接口，而这些接口可能很复杂且文档记录不完善。

需要操作系统关注的设备通常可以配置为产生中断，中断是陷阱的一种类型。内核陷阱处理代码识别设备何时引发中断，并调用驱动程序的中断处理程序；在 xv6 中，此分派发生在 [[`devintr`](/source/xv6-riscv/kernel/trap.c.md)](../source/xv6-riscv/kernel/trap.c.md)。

许多设备驱动程序在两个上下文中执行代码：一个在进程的内核线程中运行的**上半部分**(top half)，以及一个在中断时执行的**下半部分**(bottom half)。上半部分通过诸如 [`read`](/source/xv6-riscv/user/user.h.md) 和 [`write`](/source/xv6-riscv/user/user.h.md) 等希望设备执行 I/O 的系统调用来调用。此代码可能会要求硬件启动一个操作（例如，要求磁盘读取一个块）；然后代码等待操作完成。最终设备完成操作并引发中断。驱动程序的中断处理程序作为下半部分，确定已完成的操作，在适当时唤醒等待的进程，并告诉硬件开始处理任何等待的下一个操作。