# 代码: 休眠与唤醒

Xv6的[`sleep`](/source/xv6-riscv/user/user.h.md)和[`wakeup`](/source/xv6-riscv/kernel/defs.h.md)提供了上一个例子中使用的接口。基本思想是让[`sleep`](/source/xv6-riscv/user/user.h.md)将当前进程标记为`SLEEPING`，然后调用[`sched`](/source/xv6-riscv/kernel/defs.h.md)以释放CPU；[`wakeup`](/source/xv6-riscv/kernel/defs.h.md)寻找在给定等待通道上休眠的进程，并将其标记为`RUNNABLE`。[`sleep`](/source/xv6-riscv/user/user.h.md)和[`wakeup`](/source/xv6-riscv/kernel/defs.h.md)的调用者可以使用任何相互方便的数字作为通道。Xv6通常使用与等待相关的内核数据结构的地址。

[`sleep`](/source/xv6-riscv/user/user.h.md)获取`p->lock`，然后*才*释放`lk`。正如我们将看到的，[`sleep`](/source/xv6-riscv/user/user.h.md)在任何时候都持有这两个锁中的一个或另一个，这阻止了并发的[`wakeup`](/source/xv6-riscv/kernel/defs.h.md)（它必须获取并持有这两个锁）采取行动。现在[`sleep`](/source/xv6-riscv/user/user.h.md)只持有`p->lock`，它可以通过记录休眠通道、将进程状态更改为`SLEEPING`并调用[`sched`](/source/xv6-riscv/kernel/defs.h.md)来使进程休眠。稍后就会清楚，为什么在进程被标记为`SLEEPING`之后，`p->lock`不被（由[`scheduler`](/source/xv6-riscv/kernel/proc.c.md)）释放是至关重要的。

在某个时刻，一个进程将获取条件锁，设置休眠者正在等待的条件，并调用`wakeup(chan)`。重要的是[`wakeup`](/source/xv6-riscv/kernel/defs.h.md)在持有条件锁的同时被调用。[`wakeup`](/source/xv6-riscv/kernel/defs.h.md)遍历进程表。它获取它检查的每个进程的`p->lock`。当[`wakeup`](/source/xv6-riscv/kernel/defs.h.md)发现一个处于`SLEEPING`状态且具有匹配`chan`的进程时，它将该进程的状态更改为`RUNNABLE`。下一次[`scheduler`](/source/xv6-riscv/kernel/proc.c.md)运行时，它将看到该进程已准备好运行。

为什么[`sleep`](/source/xv6-riscv/user/user.h.md)和[`wakeup`](/source/xv6-riscv/kernel/defs.h.md)的锁定规则能确保一个即将进入休眠的进程不会错过一个并发的唤醒？即将进入休眠的进程从*检查条件之前*到*被标记为`SLEEPING`之后*，都持有条件锁或它自己的`p->lock`，或两者兼有。调用[`wakeup`](/source/xv6-riscv/kernel/defs.h.md)的进程在[`wakeup`](/source/xv6-riscv/kernel/defs.h.md)的循环中持有*两个*锁。因此，唤醒者要么在消费线程检查条件之前使条件为真；要么唤醒者的[`wakeup`](/source/xv6-riscv/kernel/defs.h.md)严格地在休眠线程被标记为`SLEEPING`之后检查它。然后[`wakeup`](/source/xv6-riscv/kernel/defs.h.md)将看到休眠的进程并唤醒它（除非有其他东西先唤醒它）。

有时多个进程在同一个通道上休眠；例如，多个进程从一个管道读取。一个对[`wakeup`](/source/xv6-riscv/kernel/defs.h.md)的调用将唤醒所有这些进程。其中一个将首先运行并获取[`sleep`](/source/xv6-riscv/user/user.h.md)被调用时所持有的锁，并（在管道的情况下）读取任何等待的数据。其他进程会发现，尽管被唤醒，但没有数据可读。从它们的角度来看，唤醒是“虚假的”，它们必须再次休眠。因此，[`sleep`](/source/xv6-riscv/user/user.h.md)总是在一个检查条件的循环内调用。

如果两个sleep/wakeup的使用意外地选择了同一个通道，也不会造成伤害：它们会看到虚假的唤醒，但如上所述的循环将容忍这个问题。sleep/wakeup的魅力很大程度上在于它既是轻量级的（不需要创建特殊的数据结构来充当休眠通道），又提供了一层间接性（调用者不需要知道它们正在与哪个特定的进程交互）。