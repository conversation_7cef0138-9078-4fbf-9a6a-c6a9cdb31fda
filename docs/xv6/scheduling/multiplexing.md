# 多路复用

Xv6通过在两种情况下将每个CPU从一个进程切换到另一个进程来实现多路复用。首先，当一个进程进行阻塞（必须等待一个事件）的系统调用时，xv6的[`sleep`](/source/xv6-riscv/user/user.h.md)和[`wakeup`](/source/xv6-riscv/kernel/defs.h.md)机制会进行切换，这通常发生在[`read`](/source/xv6-riscv/user/user.h.md)、[`wait`](/source/xv6-riscv/user/user.h.md)或[`sleep`](/source/xv6-riscv/user/user.h.md)中。其次，xv6会周期性地强制切换，以处理那些长时间计算而不阻塞的进程。前者是自愿切换；后者被称为非自愿切换。这种多路复用创造了每个进程都拥有自己CPU的假象。

实现多路复用带来了一些挑战。首先，如何从一个进程切换到另一个进程？基本思想是保存和恢复CPU寄存器，但C语言无法直接表达这一点，使其变得棘手。其次，如何以对用户进程透明的方式强制切换？Xv6使用了标准技术，即硬件定时器中断驱动上下文切换。第三，所有的CPU都在同一组进程之间切换，因此需要一个锁方案来避免竞争。第四，当一个进程退出时，必须释放其内存和其他资源，但它无法自己完成所有这些工作，因为（例如）它不能在使用自己的内核栈时释放它。第五，多核机器的每个CPU都必须记住它正在执行哪个进程，以便系统调用影响正确进程的内核状态。最后，[`sleep`](/source/xv6-riscv/user/user.h.md)和[`wakeup`](/source/xv6-riscv/kernel/defs.h.md)允许一个进程放弃CPU并等待被另一个进程或中断唤醒。需要小心避免导致唤醒通知丢失的竞争。