# 代码: Wait, exit, 和 kill

[`sleep`](/source/xv6-riscv/user/user.h.md)和[`wakeup`](/source/xv6-riscv/kernel/defs.h.md)可以用于多种等待。一个有趣的例子，在第1章中介绍过，是子进程的[`exit`](/source/xv6-riscv/kernel/defs.h.md)和其父进程的[`wait`](/source/xv6-riscv/user/user.h.md)之间的交互。在子进程死亡时，父进程可能已经在[`wait`](/source/xv6-riscv/user/user.h.md)中休眠，或者可能在做其他事情；在后一种情况下，后续对[`wait`](/source/xv6-riscv/user/user.h.md)的调用必须观察到子进程的死亡，可能是在它调用[`exit`](/source/xv6-riscv/kernel/defs.h.md)很久之后。xv6记录子进程死亡直到[`wait`](/source/xv6-riscv/user/user.h.md)观察到它的方式是让[`exit`](/source/xv6-riscv/kernel/defs.h.md)将调用者置于`ZOMBIE`状态，它一直保持在该状态，直到父进程的[`wait`](/source/xv6-riscv/user/user.h.md)注意到它，将子进程的状态更改为`UNUSED`，复制子进程的退出状态，并将子进程的进程ID返回给父进程。如果父进程在子进程之前退出，父进程会将子进程交给`init`进程，该进程会永久调用[`wait`](/source/xv6-riscv/user/user.h.md)；因此每个子进程都有一个父进程来为其清理。一个挑战是避免同时发生的父子进程的[`wait`](/source/xv6-riscv/user/user.h.md)和[`exit`](/source/xv6-riscv/kernel/defs.h.md)以及同时发生的[`exit`](/source/xv6-riscv/kernel/defs.h.md)和[`exit`](/source/xv6-riscv/kernel/defs.h.md)之间的竞争和死锁。

[`wait`](/source/xv6-riscv/user/user.h.md)开始时获取`wait_lock`，它充当条件锁，帮助确保[`wait`](/source/xv6-riscv/user/user.h.md)不会错过来自退出子进程的[`wakeup`](/source/xv6-riscv/kernel/defs.h.md)。然后[`wait`](/source/xv6-riscv/user/user.h.md)扫描进程表。如果它找到一个处于`ZOMBIE`状态的子进程，它会释放该子进程的资源及其`proc`结构，将子进程的退出状态复制到提供给[`wait`](/source/xv6-riscv/user/user.h.md)的地址（如果不是0），并返回子进程的进程ID。如果[`wait`](/source/xv6-riscv/user/user.h.md)找到子进程但没有一个退出，它会调用[`sleep`](/source/xv6-riscv/user/user.h.md)来等待它们中的任何一个退出，然后再次扫描。[`wait`](/source/xv6-riscv/user/user.h.md)通常持有两个锁，`wait_lock`和某个进程的`pp->lock`；避免死锁的顺序是先`wait_lock`然后是`pp->lock`。

[`exit`](/source/xv6-riscv/kernel/defs.h.md)记录退出状态，释放一些资源，调用[`reparent`](/source/xv6-riscv/user/usertests.c.md)将其子进程交给`init`进程，唤醒父进程以防它在[`wait`](/source/xv6-riscv/user/user.h.md)中，将调用者标记为僵尸，并永久让出CPU。[`exit`](/source/xv6-riscv/kernel/defs.h.md)在此序列中同时持有`wait_lock`和`p->lock`。[`exit`](/source/xv6-riscv/kernel/defs.h.md)持有`wait_lock`是因为它是`wakeup(p->parent)`的条件锁，防止在[`wait`](/source/xv6-riscv/user/user.h.md)中的父进程丢失唤醒。[`exit`](/source/xv6-riscv/kernel/defs.h.md)也必须持有`p->lock`，以防止在[`wait`](/source/xv6-riscv/user/user.h.md)中的父进程在子进程最终调用[`swtch`](/source/xv6-riscv/kernel/defs.h.md)之前看到子进程处于`ZOMBIE`状态。[`exit`](/source/xv6-riscv/kernel/defs.h.md)以与[`wait`](/source/xv6-riscv/user/user.h.md)相同的顺序获取这些锁以避免死锁。

[`exit`](/source/xv6-riscv/kernel/defs.h.md)在将其状态设置为`ZOMBIE`之前唤醒父进程可能看起来不正确，但这是安全的：虽然[`wakeup`](/source/xv6-riscv/kernel/defs.h.md)可能会导致父进程运行，但[`wait`](/source/xv6-riscv/user/user.h.md)中的循环在子进程的`p->lock`被[`scheduler`](/source/xv6-riscv/kernel/proc.c.md)释放之前无法检查子进程，所以[`wait`](/source/xv6-riscv/user/user.h.md)在[`exit`](/source/xv6-riscv/kernel/defs.h.md)将其状态设置为`ZOMBIE`很久之后才能看到退出的进程。

虽然[`exit`](/source/xv6-riscv/kernel/defs.h.md)允许一个进程自己终止，但[`kill`](/source/xv6-riscv/user/user.h.md)允许一个进程请求另一个进程终止。让[`kill`](/source/xv6-riscv/user/user.h.md)直接销毁受害者进程会过于复杂，因为受害者可能正在另一个CPU上执行，可能正在对内核数据结构进行敏感的更新序列。因此[`kill`](/source/xv6-riscv/user/user.h.md)做得很少：它只是设置受害者的`p->killed`，如果它正在休眠，就唤醒它。最终受害者将进入或离开内核，此时[`usertrap`](/source/xv6-riscv/kernel/trap.c.md)中的代码如果`p->killed`被设置就会调用[`exit`](/source/xv6-riscv/kernel/defs.h.md)（它通过调用[`killed`](/source/xv6-riscv/kernel/defs.h.md)来检查）。如果受害者在用户空间运行，它很快就会通过进行系统调用或因为定时器（或其他设备）中断而进入内核。

如果受害者进程在[`sleep`](/source/xv6-riscv/user/user.h.md)中，[`kill`](/source/xv6-riscv/user/user.h.md)对[`wakeup`](/source/xv6-riscv/kernel/defs.h.md)的调用将导致受害者从[`sleep`](/source/xv6-riscv/user/user.h.md)返回。这可能有潜在的危险，因为正在等待的条件可能不为真。然而，xv6对[`sleep`](/source/xv6-riscv/user/user.h.md)的调用总是被包装在一个`while`循环中，该循环在[`sleep`](/source/xv6-riscv/user/user.h.md)返回后重新测试条件。一些对[`sleep`](/source/xv6-riscv/user/user.h.md)的调用也在循环中测试`p->killed`，并在设置时放弃当前活动。这只有在放弃是正确的情况下才会这样做。例如，管道读写代码在killed标志被设置时返回；最终代码将返回到trap，trap将再次检查`p->killed`并退出。

一些xv6的[`sleep`](/source/xv6-riscv/user/user.h.md)循环不检查`p->killed`，因为代码正处于一个应该原子化的多步系统调用的中间。virtio驱动程序是一个例子：它不检查`p->killed`，因为一个磁盘操作可能是一组写操作中的一个，而所有这些写操作都是为了让文件系统处于一个正确的状态所必需的。一个在等待磁盘I/O时被杀死的进程在完成当前系统调用并且[`usertrap`](/source/xv6-riscv/kernel/trap.c.md)看到killed标志之前不会退出。