# 现实世界

Unix的“标准”文件描述符、管道和方便的shell语法的组合，是编写通用可重用程序的一个重大进步。
这个想法激发了一种“软件工具”文化，这是Unix强大功能和普及的主要原因，而shell是第一个所谓的“脚本语言”。
Unix系统调用接口至今仍在BSD、Linux和macOS等系统中存在。

Unix系统调用接口已通过可移植操作系统接口（POSIX）标准进行了标准化。
Xv6*不*符合POSIX标准：它缺少许多系统调用（包括像`lseek`这样的基本调用），并且它提供的许多系统调用与标准不同。
我们对xv6的主要目标是简单和清晰，同时提供一个简单的类UNIX系统调用接口。
有几个人通过添加更多的系统调用和一个简单的C库来扩展xv6，以便运行基本的Unix程序。然而，现代内核提供的系统调用和内核服务种类比xv6多得多。例如，它们支持网络、窗口系统、用户级线程、许多设备的驱动程序等等。现代内核不断快速发展，并提供许多超出POSIX的功能。

Unix用一组文件名称和文件描述符接口统一了对多种类型资源（文件、目录和设备）的访问。
这个想法可以扩展到更多种类的资源；一个很好的例子是Plan 9，它将“资源即文件”的概念应用于网络、图形等。
然而，大多数源自Unix的操作系统并没有走这条路。

文件系统和文件描述符是强大的抽象。
即便如此，还有其他操作系统接口的模型。
Multics是Unix的前身，它以一种使其看起来像内存的方式抽象文件存储，产生了一种截然不同的接口风格。
Multics设计的复杂性直接影响了Unix的设计者，他们的目标是构建更简单的东西。

Xv6不提供用户或保护一个用户免受另一个用户侵害的概念；用Unix的术语来说，所有xv6进程都以root身份运行。

本书探讨了xv6如何实现其类Unix接口，但这些思想和概念不仅适用于Unix。
任何操作系统都必须将进程复用到基础硬件上，将进程相互隔离，并提供受控的进程间通信机制。
学习xv6之后，您应该能够看到其他更复杂的操作系统，并在这些系统中看到xv6的基本概念。