# 文件系统

文件系统的目的是组织和存储数据。文件系统通常支持用户和应用程序之间的数据共享，以及持久性，以便数据在重启后仍然可用。

xv6 文件系统提供类 Unix 的文件、目录和路径名（参见第 1 章），并将其数据存储在 virtio 磁盘上以实现持久性。该文件系统解决了几个挑战：

*   文件系统需要磁盘上的数据结构来表示命名的目录和文件树，记录每个文件内容所在的块的标识，并记录磁盘的哪些区域是空闲的。

*   文件系统必须支持崩溃恢复。也就是说，如果发生崩溃（例如，电源故障），文件系统必须在重启后仍能正常工作。风险在于崩溃可能会中断一系列更新，并留下不一致的磁盘数据结构（例如，一个块既在文件中使用又被标记为空闲）。

*   不同的进程可能同时操作文件系统，因此文件系统代码必须协调以维护不变量。

*   访问磁盘比访问内存慢几个数量级，因此文件系统必须维护一个内存中的常用块缓存。

本章的其余部分将解释 xv6 如何应对这些挑战。