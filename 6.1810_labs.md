# 6.1810 实验详细内容

## Lab util: Unix utilities
- **目标**: 熟悉 xv6 和其系统调用。
- **任务**:
  - 实现 `sleep` 程序：
    - 在 `user/sleep.c` 中实现一个用户级的 `sleep` 程序，暂停指定的时间。
    - 使用 `atoi` 转换命令行参数为整数。
    - 在 `Makefile` 中添加到 `UPROGS`。
  - 实现 `pingpong` 程序：
    - 使用管道在父子进程之间传递字节。
    - 在 `user/pingpong.c` 中实现。
  - 实现 `primes` 并发素数筛选程序：
    - 使用管道和 `fork` 实现多进程筛选。
    - 在 `user/primes.c` 中实现。
  - 实现 `find` 和 `xargs`：
    - `find`：递归查找特定文件名。
    - `xargs`：从标准输入读取行并作为参数传递给命令。
- **提交要求**:
  - 提交时间记录文件 `time.txt`。
  - 提交答案文件 `answers-*.txt`。

## Lab syscall: System calls
- **目标**: 添加新的系统调用。
- **任务**:
  - 使用 GDB 调试 xv6：
    - 设置断点并查看堆栈回溯。
    - 分析 `syscall` 的调用链。
  - 实现系统调用跟踪功能：
    - 添加 `trace` 系统调用，记录系统调用的返回值。
    - 修改 `kernel/syscall.c` 和 `kernel/sysproc.c`。
  - 攻击 xv6 的内存漏洞：
    - 利用未清零的内存读取其他进程的秘密数据。
    - 修改 `user/attack.c` 实现攻击。
- **提交要求**:
  - 提交时间记录文件 `time.txt`。
  - 提交答案文件 `answers-*.txt`。

## Lab pgtbl: Page tables
- **目标**: 探索和修改页表。
- **任务**:
  - 检查用户进程的页表：
    - 使用 `pgpte` 系统调用打印页表条目。
    - 分析页表的权限位和物理地址。
  - 加速系统调用：
    - 为 `getpid` 系统调用添加共享只读页。
    - 修改 `kernel/proc.c` 和 `kernel/vm.c`。
  - 打印页表：
    - 实现 `kpgtbl` 系统调用，打印页表结构。
    - 使用 `vmprint` 函数格式化输出。
  - 使用超级页：
    - 修改 `sbrk` 支持 2MB 的超级页。
    - 实现 `superalloc` 和 `superfree` 函数。
- **提交要求**:
  - 提交时间记录文件 `time.txt`。
  - 提交答案文件 `answers-*.txt`。

## Lab traps: Traps
- **目标**: 理解系统调用的实现。
- **任务**:
  - 理解 RISC-V 汇编：
    - 分析 `user/call.asm` 中的函数调用和寄存器使用。
    - 理解小端和大端的字节序。
  - 实现回溯功能：
    - 在 `kernel/printf.c` 中实现 `backtrace` 函数。
    - 使用 `addr2line` 工具解析地址到源码行号。
  - 添加 `sigalarm` 系统调用：
    - 定期调用用户定义的处理函数。
    - 修改 `kernel/trap.c` 和 `kernel/proc.c`。
- **提交要求**:
  - 提交时间记录文件 `time.txt`。
  - 提交答案文件 `answers-*.txt`。

## Lab cow: Copy-on-Write Fork
- **目标**: 实现写时复制的 `fork`。
- **任务**:
  - 修改 `uvmcopy` 实现共享物理页：
    - 清除 PTE 的写位以标记为只读。
  - 修改 `usertrap` 处理页错误：
    - 分配新页并复制旧页内容。
    - 更新 PTE 为可写。
  - 实现物理页的引用计数：
    - 在 `kernel/kalloc.c` 中维护引用计数。
    - 确保页在最后一个引用释放时被回收。
- **提交要求**:
  - 提交时间记录文件 `time.txt`。
  - 提交答案文件 `answers-*.txt`。

## Lab lock: Locks
- **目标**: 提高并行性。
- **任务**:
  - 重新设计内存分配器以减少锁争用：
    - 为每个 CPU 创建独立的空闲列表。
    - 实现跨 CPU 的内存偷取机制。
  - 修改块缓存以减少锁争用：
    - 使用哈希表和每个桶的锁。
    - 确保缓存块的一致性和唯一性。
- **提交要求**:
  - 提交时间记录文件 `time.txt`。
  - 提交答案文件 `answers-*.txt`。

## Lab fs: File system
- **目标**: 增加文件系统的功能。
- **任务**:
  - 支持大文件：
    - 修改 `bmap` 支持双重间接块。
    - 确保 `bigfile` 测试通过。
  - 添加符号链接：
    - 实现 `symlink` 系统调用。
    - 修改 `open` 支持递归解析符号链接。
- **提交要求**:
  - 提交时间记录文件 `time.txt`。
  - 提交答案文件 `answers-*.txt`。

## Lab mmap: Memory-mapped files
- **目标**: 添加 `mmap` 和 `munmap` 系统调用。
- **任务**:
  - 实现文件的内存映射：
    - 在 `usertrap` 中处理页错误。
    - 使用 `readi` 从文件中读取数据到物理页。
  - 实现 `munmap` 解除映射：
    - 释放映射的页并写回修改。
  - 修改 `fork` 支持映射区域：
    - 确保子进程继承父进程的映射。
    - 在页错误时分配新页。
- **提交要求**:
  - 提交时间记录文件 `time.txt`。
  - 提交答案文件 `answers-*.txt`。

---

**注意**: 以上内容基于 2024 年课程安排，可能会有更新。
