# 6.1810: Operating System Engineering 课程时间表

## 课程时间表

### 9月
- **9月4日**: LEC 1 - Introduction and examples
  - **准备材料**: 阅读 Chapter 1
  - **作业**: Lab util: Unix utilities
- **9月9日**: LEC 2 - C in xv6, slides, and examples
  - **准备材料**: 阅读 K&R 第 2.9 节、第 5.1 至 5.6 节、第 6.4 节
- **9月11日**: LEC 3 - OS design
  - **准备材料**: 阅读 Chapter 2 和 xv6 代码
  - **作业**: Lab syscall: System calls
- **9月16日**: LEC 4 - Page tables
  - **准备材料**: 阅读 Chapter 3 和相关 xv6 代码
  - **作业**: Lab pgtbl: Page tables

### 10月
- **10月7日**: LEC 10 - Locking
  - **准备材料**: 阅读 "Locking" 和相关 xv6 代码
  - **作业**: Lab lock: Parallelism/locking
- **10月9日**: LEC 11 - Scheduling 1
  - **准备材料**: 阅读 "Scheduling" 和相关 xv6 代码

### 11月
- **11月13日**: LEC 16 - Virtual memory for applications
  - **准备材料**: 阅读 "Virtual Memory Primitives for User Programs (1991)"
  - **作业**: Lab fs: File system
- **11月18日**: LEC 17 - OS Organization
  - **准备材料**: 阅读 "The Performance of micro-Kernel-Based Systems (1997)"
  - **作业**: Lab mmap: Mmap

### 12月
- **12月2日**: LEC 20 - Networking
  - **准备材料**: 阅读 "Receive Livelock (1996)"
  - **作业**: Lab mmap
- **12月9日**: LEC 21 - Meltdown
  - **准备材料**: 阅读 "Meltdown (2018)"

## 实验安排
- **Lab util**: Unix utilities, 截止日期: 9月12日
- **Lab syscall**: System calls, 截止日期: 9月19日
- **Lab pgtbl**: Page tables, 截止日期: 9月26日
- **Lab traps**: Traps, 截止日期: 10月3日
- **Lab cow**: Copy-on-write fork, 截止日期: 10月17日
- **Lab lock**: Parallelism/locking, 截止日期: 11月14日
- **Lab fs**: File system, 截止日期: 11月21日
- **Lab mmap**: Mmap, 截止日期: 12月6日

## 参考资料
- [6.1810 课程主页](https://pdos.csail.mit.edu/6.828/2024/index.html)
- [课程参考文献](https://pdos.csail.mit.edu/6.828/2024/reference.html)
- [Piazza 页面](https://piazza.com/mit/fall2024/61810)

---

**注意**: 以上内容基于 2024 年课程安排，可能会有更新。
