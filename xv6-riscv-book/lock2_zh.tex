\chapter{再谈并发}
\label{CH:LOCK2}

在内核设计中，同时获得良好的并行性能、并发下的正确性以及易于理解的代码是一个巨大的挑战。
直接使用锁是通往正确性的最佳途径，但这并非总是可行。
本章重点介绍 xv6 被迫以复杂方式使用锁的例子，以及 xv6 使用类似锁的技术但并非锁的例子。

\section{锁模式}

缓存项的加锁通常是一个挑战。
例如，文件系统的块缓存 \lineref{kernel/bio.c:/^struct/} 存储了多达 {\tt NBUF} 个磁盘块的副本。
至关重要的是，一个给定的磁盘块在缓存中最多只能有一个副本；否则，不同的进程可能会对本应是同一个块的不同副本进行冲突的更改。
每个缓存的块都存储在一个 {\tt struct buf} \lineref{kernel/buf.h:1} 中。
一个 {\tt struct buf} 有一个锁字段，这有助于确保在同一时间只有一个进程使用一个给定的磁盘块。
然而，这个锁是不够的：如果一个块根本不存在于缓存中，而两个进程想同时使用它呢？
由于块尚未被缓存，所以没有 {\tt struct buf}，因此也就没有东西可以锁定。
Xv6 通过将一个额外的锁（{\tt bcache.lock}）与缓存块的身份集合相关联来处理这种情况。
需要检查一个块是否被缓存的代码（例如，{\tt bget} \lineref{kernel/bio.c:/^bget/}），或者改变缓存块集合的代码，都必须持有 {\tt bcache.lock}；在该代码找到它需要的块和 {\tt struct buf} 之后，它可以释放 {\tt bcache.lock}，只锁定特定的块。
这是一个常见的模式：一个锁用于项目集合，每个项目再加一个锁。

通常，获取锁的函数也会释放它。
但更精确的看法是，锁是在一个必须以原子方式出现的操作序列开始时获取的，并在该序列结束时释放。
如果序列在不同的函数、不同的线程或不同的 CPU 上开始和结束，那么锁的获取和释放也必须这样做。
锁的功能是强制其他使用者等待，而不是将一段数据固定在某个特定的代理上。
一个例子是 {\tt yield} \lineref{kernel/proc.c:/^yield/} 中的 {\tt acquire}，它在调度器线程中被释放，而不是在获取它的进程中。
另一个例子是 {\tt ilock} \lineref{kernel/fs.c:/^ilock/} 中的 {\tt acquiresleep}；这段代码在读取磁盘时经常会休眠；它可能会在另一个 CPU 上被唤醒，这意味着锁可能会在不同的 CPU 上被获取和释放。

释放一个受嵌入在对象中的锁保护的对象是一件微妙的事情，因为拥有锁并不足以保证释放是正确的。
问题出现在当其他线程在 {\tt acquire} 中等待使用该对象时；释放该对象会隐式地释放嵌入的锁，这将导致等待的线程发生故障。
一个解决方案是跟踪该对象存在多少引用，以便仅在最后一个引用消失时才释放它。
参见 {\tt pipeclose} \lineref{kernel/pipe.c:/^pipeclose/} 的例子；
{\tt pi->readopen} 和 {\tt pi->writeopen} 跟踪管道是否有文件描述符引用它。

通常我们看到锁围绕着对一组相关项目的读写序列；锁确保其他线程只看到完整的更新序列（只要它们也加锁）。
那么，当更新只是对单个共享变量的简单写入时，情况又如何呢？
例如，\texttt{setkilled} 和 \texttt{killed} \lineref{kernel/proc.c:/^setkilled/} 在它们对 \lstinline{p->killed} 的简单使用周围加锁。
如果没有锁，一个线程可能在另一个线程读取 \lstinline{p->killed} 的同时写入它。
这是一个\indextextx{竞争}，C语言规范说竞争会产生\indextext{未定义行为}，这意味着程序可能会崩溃或产生不正确的结果\footnote{``Threads and data races'' in \url{https://en.cppreference.com/w/c/language/memory_model}}。
锁可以防止竞争并避免未定义行为。

竞争可能破坏程序的一个原因是，如果没有锁或等效的构造，编译器可能会生成与原始 C 代码非常不同的读写内存的机器代码。
例如，调用 \texttt{killed} 的线程的机器代码可能会将 \lstinline{p->killed} 复制到一个寄存器中，并且只读取那个缓存的值；这意味着该线程可能永远看不到对 \lstinline{p->killed} 的任何写入。
锁可以防止这种缓存。

\section{类锁模式}

在许多地方，xv6 使用引用计数或标志以类似锁的方式来指示一个对象已被分配，不应被释放或重用。
进程的 {\tt p->state} 就是这样起作用的，{\tt file}、{\tt inode} 和 {\tt buf} 结构中的引用计数也是如此。
虽然在每种情况下都有一个锁来保护标志或引用计数，但正是后者防止了对象被过早释放。

文件系统使用 {\tt struct inode} 引用计数作为一种可以被多个进程持有的共享锁，以避免如果代码使用普通锁会发生的死锁。
例如，{\tt namex} \lineref{kernel/fs.c:/^namex/} 中的循环会依次锁定每个路径名组件所命名的目录。
然而，{\tt namex} 必须在循环结束时释放每个锁，因为如果它持有多个锁，当路径名包含一个点（例如，{\tt a/./b}）时，它可能会与自身发生死锁。
它也可能与涉及该目录和 {\tt ..} 的并发查找发生死锁。
正如第~\ref{CH:FS}章所解释的，解决方案是让循环将目录 inode 带到下一次迭代，其引用计数增加，但未被锁定。

一些数据项在不同时间受不同机制的保护，并且有时可能通过 xv6 代码的结构隐式地免受并发访问，而不是通过显式锁。
例如，当一个物理页空闲时，它受 \texttt{kmem.lock} \lineref{kernel/kalloc.c:/^. kmem;/} 保护。
如果该页随后被分配为一个管道 \lineref{kernel/pipe.c:/^pipealloc/}，它将受到一个不同的锁（嵌入的 \lstinline{pi->lock}）的保护。
如果该页被重新分配给一个新进程的用户内存，它根本不受锁的保护。
相反，分配器不会将该页分配给任何其他进程（直到它被释放）这一事实保护了它免受并发访问。
一个新进程内存的所有权是复杂的：首先父进程在 {\tt fork} 中分配和操作它，然后子进程使用它，并且（在子进程退出后）父进程再次拥有该内存并将其传递给 {\tt kfree}。
这里有两个教训：一个数据对象在其生命周期的不同点可能以不同的方式受到并发保护，并且这种保护可能采取隐式结构的形式，而不是显式锁。

最后一个类锁的例子是在调用 {\tt mycpu()} \lineref{kernel/proc.c:/^myproc/} 前后需要禁用中断。
禁用中断使得调用代码相对于可能强制进行上下文切换的定时器中断是原子的，从而将进程移动到不同的 CPU。

\section{完全没有锁}

有几个地方 xv6 在完全没有锁的情况下共享可变数据。
一个是在自旋锁的实现中，尽管可以认为 RISC-V 原子指令依赖于硬件中实现的锁。
另一个是 {\tt main.c} \lineref{kernel/main.c:/^volatile/} 中的 {\tt started} 变量，用于防止其他 CPU 在 CPU 0 完成 xv6 初始化之前运行；
{\tt volatile} 确保编译器实际生成加载和存储指令。

Xv6 包含这样的情况：一个 CPU 或线程写入一些数据，而另一个 CPU 或线程读取这些数据，但没有专门用于保护这些数据的特定锁。
例如，在 {\tt fork} 中，父进程写入子进程的用户内存页，而子进程（一个不同的线程，可能在不同的 CPU 上）读取这些页；没有锁明确保护这些页。
这严格来说不是一个锁问题，因为子进程在父进程完成写入之前不会开始执行。
这是一个潜在的内存排序问题（参见第~\ref{CH:LOCK}章），因为没有内存屏障，就没有理由期望一个 CPU 能看到另一个 CPU 的写入。
然而，由于父进程释放锁，并且子进程在启动时获取锁，{\tt acquire} 和 {\tt release} 中的内存屏障确保了子进程的 CPU 能看到父进程的写入。

\section{并行性}

加锁主要是为了正确性而抑制并行性。
因为性能也很重要，内核设计者通常必须考虑如何使用锁，既能实现正确性又能允许并行性。
虽然 xv6 并非系统地为高性能而设计，但考虑哪些 xv6 操作可以并行执行，以及哪些可能在锁上发生冲突，仍然是值得的。

xv6 中的管道是一个相当好的并行性例子。
每个管道都有自己的锁，因此不同的进程可以在不同的 CPU 上并行地读写不同的管道。
然而，对于一个给定的管道，写入者和读取者必须等待对方释放锁；它们不能同时读/写同一个管道。
同样，从空管道读取（或向满管道写入）也必须阻塞，但这并非由于锁方案。

上下文切换是一个更复杂的例子。
两个内核线程，每个都在自己的 CPU 上执行，可以同时调用 {\tt yield}、{\tt sched} 和 {\tt swtch}，并且这些调用将并行执行。
每个线程都持有一个锁，但它们是不同的锁，所以它们不必等待对方。
然而，一旦进入 {\tt scheduler}，两个 CPU 在搜索进程表中寻找一个 {\tt RUNNABLE} 的进程时，可能会在锁上发生冲突。
也就是说，xv6 很可能在上下文切换期间从多个 CPU 中获得性能提升，但可能没有它所能达到的那么多。

另一个例子是来自不同 CPU 上不同进程的并发 {\tt fork} 调用。
这些调用可能需要相互等待 {\tt pid\_lock} 和 {\tt kmem.lock}，以及为了在进程表中搜索一个 {\tt UNUSED} 进程所需的每个进程的锁。
另一方面，两个分叉的进程可以完全并行地复制用户内存页和格式化页表页。

在上述每个例子中，锁方案在某些情况下牺牲了并行性能。
在每种情况下，都有可能使用更精巧的设计来获得更多的并行性。
这是否值得取决于细节：相关操作被调用的频率，代码持有有争议的锁的时间，可能同时运行冲突操作的 CPU 数量，以及代码的其他部分是否是更严格的瓶颈。
很难猜测一个给定的锁方案是否会导致性能问题，或者一个新的设计是否明显更好，因此通常需要对现实的工作负载进行测量。

\section{练习}

\begin{enumerate}

\item 修改 xv6 的管道实现，允许对同一个管道的读和写在不同的 CPU 上并行进行。

\item 修改 xv6 的 \texttt{scheduler()}，以减少不同 CPU 同时寻找可运行进程时的锁竞争。

\item 消除 xv6 的 \texttt{fork()} 中的一些串行化。

\end{enumerate}