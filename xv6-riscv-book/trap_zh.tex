%    关于 panic 的旁注：
% 	panic 是内核的最后手段：不可能发生的事情发生了，
% 	内核不知道如何继续。在 xv6 中，panic 会...
\chapter{陷阱和系统调用}
\label{CH:TRAP}

有三种事件会导致 CPU 搁置正常的指令执行，并强制将控制权转移到处理该事件的特殊代码。一种情况是系统调用，当用户程序执行 {\tt ecall} 指令请求内核为其执行某项操作时。另一种情况是\indextext{异常}：一条指令（用户或内核）执行了非法操作，例如除以零或使用无效的虚拟地址。第三种情况是设备\indextext{中断}，当设备发出需要注意的信号时，例如当磁盘硬件完成读或写请求时。

本书使用\indextext{陷阱}作为这些情况的通用术语。通常，在陷阱发生时正在执行的任何代码稍后都需要恢复，并且不应该意识到发生了任何特殊情况。也就是说，我们通常希望陷阱是透明的；这对于设备中断尤其重要，因为被中断的代码通常不期望发生中断。通常的顺序是，陷阱强制将控制权转移到内核；内核保存寄存器和其他状态，以便可以恢复执行；内核执行适当的处理程序代码（例如，系统调用实现或设备驱动程序）；内核恢复保存的状态并从陷阱中返回；原始代码从中断处继续执行。

Xv6 在内核中处理所有陷阱；陷阱不会传递给用户代码。在内核中处理陷阱对于系统调用来说是很自然的。对于中断来说，这是有道理的，因为隔离要求只允许内核使用设备，并且因为内核是多个进程之间共享设备的便捷机制。对于异常来说，这也是有道理的，因为 xv6 对所有来自用户空间的异常的响应都是终止有问题的程序。

Xv6 的陷阱处理分为四个阶段：RISC-V CPU 采取的硬件操作、为内核 C 代码准备的一些汇编指令、一个决定如何处理陷阱的 C 函数，以及系统调用或设备驱动程序服务例程。虽然三种陷阱类型之间的共性表明内核可以用单个代码路径处理所有陷阱，但事实证明，为两种不同情况分别设置代码会更方便：来自用户空间的陷阱和来自内核空间的陷阱。处理陷阱的内核代码（汇编或 C）通常被称为\indextext{处理程序}；第一个处理程序的指令通常用汇编（而不是 C）编写，有时被称为\indextext{向量}。


\section{RISC-V 陷阱机制}

每个 RISC-V CPU 都有一组控制寄存器，内核通过写入这些寄存器来告诉 CPU 如何处理陷阱，内核也可以读取这些寄存器来了解已发生的陷阱。RISC-V 文档包含了完整的故事~\cite{riscv:priv}。{\tt riscv.h} \lineref{kernel/riscv.h:1} 包含了 xv6 使用的定义。以下是最重要寄存器的纲要：

\begin{itemize}

\item \indexcode{stvec}: 内核在此处写入其陷阱处理程序的地址；RISC-V 跳转到 {\tt stvec} 中的地址来处理陷阱。

\item \indexcode{sepc}: 发生陷阱时，RISC-V 在此处保存程序计数器（因为 {\tt pc} 随后会被 {\tt stvec} 中的值覆盖）。{\tt sret}（从陷阱返回）指令将 {\tt sepc} 复制到 {\tt pc}。内核可以写入 {\tt sepc} 来控制 {\tt sret} 的去向。

\item \indexcode{scause}: RISC-V 在此处放置一个数字，描述陷阱的原因。

\item \indexcode{sscratch}: 陷阱处理程序代码使用 {\tt sscratch} 来帮助其在保存用户寄存器之前避免覆盖它们。

\item \indexcode{sstatus}: {\tt sstatus} 中的 SIE 位控制是否启用设备中断。如果内核清除 SIE，RISC-V 将延迟设备中断，直到内核设置 SIE。SPP 位指示陷阱是来自用户模式还是监督者模式，并控制 {\tt sret} 返回到哪种模式。

\end{itemize}

上述寄存器与在监督者模式下处理的陷阱有关，不能在用户模式下读取或写入。

多核芯片上的每个 CPU 都有自己的一组这些寄存器，并且在任何给定时间可能有多个 CPU 在处理陷阱。

当需要强制陷阱时，RISC-V 硬件对所有陷阱类型执行以下操作：

\begin{enumerate}

\item 如果陷阱是设备中断，并且 {\tt sstatus} 的 SIE 位被清除，则不执行以下任何操作。

\item 通过清除 {\tt sstatus} 中的 SIE 位来禁用中断。

\item 将 {\tt pc} 复制到 {\tt sepc}。

\item 在 {\tt sstatus} 的 SPP 位中保存当前模式（用户或监督者）。

\item 设置 {\tt scause} 以反映陷阱的原因。

\item 将模式设置为监督者。

\item 将 {\tt stvec} 复制到 {\tt pc}。

\item 在新的 {\tt pc} 处开始执行。

\end{enumerate}

请注意，CPU 不会切换到内核页表，不会切换到内核中的堆栈，也不会保存除 {\tt pc} 之外的任何寄存器。内核软件必须执行这些任务。CPU 在陷阱期间只做最少的工作的一个原因是为软件提供灵活性；例如，一些操作系统在某些情况下省略页表切换以提高陷阱性能。

值得思考的是，上面列出的步骤中是否有任何一个可以省略，也许是为了追求更快的陷阱。虽然在某些情况下更简单的序列可以工作，但通常省略许多步骤是危险的。例如，假设 CPU 没有切换程序计数器。那么来自用户空间的陷阱可以在仍在运行用户指令的情况下切换到监督者模式。这些用户指令可能会破坏用户/内核隔离，例如通过修改 {\tt satp} 寄存器以指向一个允许访问所有物理内存的页表。因此，CPU 切换到内核指定的指令地址，即 {\tt stvec}，是很重要的。

\section{来自用户空间的陷阱}

Xv6 根据陷阱是在内核中执行还是在用户代码中执行来区别处理。以下是来自用户代码的陷阱的故事；第~\ref{s:ktraps}节描述了来自内核代码的陷阱。

如果在用户空间执行时发生陷阱，可能是用户程序进行了系统调用（{\tt ecall} 指令），或者执行了非法操作，或者设备中断。来自用户空间的陷阱的高级路径是 {\tt uservec} \lineref{kernel/trampoline.S:/^uservec/}，然后是 {\tt usertrap} \lineref{kernel/trap.c:/^usertrap/}；返回时是 {\tt usertrapret} \lineref{kernel/trap.c:/^usertrapret/}，然后是 {\tt userret} \lineref{kernel/trampoline.S:/^userret/}。

% 谈论为什么 RISC-V 不切换页表

xv6 陷阱处理设计的一个主要限制是 RISC-V 硬件在强制陷阱时不切换页表。这意味着 {\tt stvec} 中的陷阱处理程序地址必须在用户页表中具有有效映射，因为在陷阱处理代码开始执行时，该页表是有效的。此外，xv6 的陷阱处理代码需要切换到内核页表；为了能够在切换后继续执行，内核页表也必须具有对 {\tt stvec} 指向的处理程序的映射。

Xv6 使用\indextext{蹦床}页来满足这些要求。蹦床页包含 {\tt uservec}，即 {\tt stvec} 指向的 xv6 陷阱处理代码。蹦床页在每个进程的页表中映射在地址 \indexcode{TRAMPOLINE}，该地址位于虚拟地址空间的顶部，以便它位于程序自己使用的内存之上。蹦床页也映射在内核页表中的地址 {\tt TRAMPOLINE}。参见图~\ref{fig:as}和图~\ref{fig:xv6_layout}。因为蹦床页映射在用户页表中，所以陷阱可以在监督者模式下在那里开始执行。因为蹦床页在内核地址空间中映射在相同的地址，所以陷阱处理程序可以在切换到内核页表后继续执行。

{\tt uservec} 陷阱处理程序的代码在 {\tt trampoline.S} \lineref{kernel/trampoline.S:/^uservec/} 中。当 {\tt uservec} 启动时，所有 32 个寄存器都包含被中断的用户代码所拥有的值。这 32 个值需要保存在内存中的某个地方，以便稍后内核可以在返回用户空间之前恢复它们。存储到内存需要使用一个寄存器来保存地址，但此时没有可用的通用寄存器！幸运的是，RISC-V 以 {\tt sscratch} 寄存器的形式提供了帮助。{\tt uservec} 开头的 {\tt csrw} 指令将 {\tt a0} 保存在 {\tt sscratch} 中。现在 {\tt uservec} 有一个寄存器（{\tt a0}）可以使用。

{\tt uservec} 的下一个任务是保存 32 个用户寄存器。内核为每个进程分配一页内存用于一个 {\tt trapframe} 结构，该结构（除其他外）有空间保存 32 个用户寄存器 \lineref{kernel/proc.h:/^struct.trapframe/}。因为 {\tt satp} 仍然引用用户页表，所以 {\tt uservec} 需要将陷阱帧映射在用户地址空间中。Xv6 将每个进程的陷阱帧映射在该进程的用户页表中的虚拟地址 {\tt TRAPFRAME}；{\tt TRAPFRAME} 就在 {\tt TRAMPOLINE} 下面。进程的 {\tt p->trapframe} 也指向陷阱帧，尽管是在其物理地址上，以便内核可以通过内核页表使用它。

因此，{\tt uservec} 将地址 {\tt TRAPFRAME} 加载到 {\tt a0} 中，并在那里保存所有用户寄存器，包括从 {\tt sscratch} 读回的用户 {\tt a0}。

{\tt trapframe} 包含当前进程的内核堆栈地址、当前 CPU 的 hartid、{\tt usertrap} 函数的地址以及内核页表的地址。{\tt uservec} 检索这些值，将 {\tt satp} 切换到内核页表，然后跳转到 {\tt usertrap}。

{\tt usertrap} 的工作是确定陷阱的原因，处理它，然后返回 \lineref{kernel/trap.c:/^usertrap/}。它首先更改 {\tt stvec}，以便在内核中的陷阱将由 {\tt kernelvec} 而不是 {\tt uservec} 处理。它保存 {\tt sepc} 寄存器（保存的用户程序计数器），因为 {\tt usertrap} 可能会调用 \lstinline{yield} 切换到另一个进程的内核线程，并且该进程可能会返回到用户空间，在此过程中它将修改 \lstinline{sepc}。如果陷阱是系统调用，{\tt usertrap} 调用 {\tt syscall} 来处理它；如果是设备中断，则调用 {\tt devintr}；否则是异常，内核将终止出错的进程。系统调用路径将保存的用户程序计数器加四，因为 RISC-V 在系统调用的情况下，会将程序指针指向 {\tt ecall} 指令，但用户代码需要在后续指令处恢复执行。在退出时，{\tt usertrap} 检查进程是否已被终止或应该让出 CPU（如果此陷阱是定时器中断）。

返回用户空间的第一步是调用 {\tt usertrapret} \lineref{kernel/trap.c:/^usertrapret/}。此函数设置 RISC-V 控制寄存器，为将来来自用户空间的陷阱做准备：将 {\tt stvec} 设置为 {\tt uservec} 并准备 {\tt uservec} 依赖的陷阱帧字段。{\tt usertrapret} 将 {\tt sepc} 设置为先前保存的用户程序计数器。最后，{\tt usertrapret} 在映射在用户和内核页表中的蹦床页上调用 {\tt userret}；原因是在 {\tt userret} 中的汇编代码将切换页表。

{\tt usertrapret} 对 {\tt userret} 的调用在 {\tt a0} 中传递一个指向进程用户页表的指针 \lineref{kernel/trampoline.S:/^userret/}。{\tt userret} 将 {\tt satp} 切换到进程的用户页表。回想一下，用户页表映射了蹦床页和 {\tt TRAPFRAME}，但没有映射内核的其他任何内容。在用户和内核页表中以相同虚拟地址映射的蹦床页允许 {\tt userret} 在更改 {\tt satp} 后继续执行。从这一点开始，{\tt userret} 唯一可以使用的数据是寄存器内容和陷阱帧的内容。{\tt userret} 将 {\tt TRAPFRAME} 地址加载到 {\tt a0} 中，通过 {\tt a0} 从陷阱帧中恢复保存的用户寄存器，恢复保存的用户 {\tt a0}，并执行 {\tt sret} 返回到用户空间。

\section{代码：调用系统调用}

第~\ref{CH:FIRST}章以 \indexcode{initcode.S} 调用 {\tt exec} 系统调用 \lineref{user/initcode.S:/SYS_exec/} 结束。让我们看看用户调用是如何到达内核中 {\tt exec} 系统调用实现的。

{\tt initcode.S} 将 \indexcode{exec} 的参数放在寄存器 {\tt a0} 和 {\tt a1} 中，并将系统调用号放在 \texttt{a7} 中。系统调用号与 {\tt syscalls} 数组中的条目匹配，这是一个函数指针表 \lineref{kernel/syscall.c:/syscalls/}。\lstinline{ecall} 指令陷入内核并导致 {\tt uservec}、{\tt usertrap}，然后是 {\tt syscall} 执行，如我们上面所见。

\indexcode{syscall} \lineref{kernel/syscall.c:/^syscall/} 从陷阱帧中保存的 \texttt{a7} 中检索系统调用号，并用它来索引 {\tt syscalls}。对于第一个系统调用，\texttt{a7} 包含 \indexcode{SYS_exec} \lineref{kernel/syscall.h:/SYS_exec/}，导致调用系统调用实现函数 \lstinline{sys_exec}。

当 \lstinline{sys_exec} 返回时，\lstinline{syscall} 将其返回值记录在 \lstinline{p->trapframe->a0} 中。这将导致原始用户空间对 {\tt exec()} 的调用返回该值，因为 RISC-V 上的 C 调用约定将返回值放在 {\tt a0} 中。系统调用通常返回负数表示错误，返回零或正数表示成功。如果系统调用号无效，\lstinline{syscall} 会打印错误并返回 -1。

\section{代码：系统调用参数}

内核中的系统调用实现需要找到用户代码传递的参数。因为用户代码调用系统调用包装函数，所以参数最初位于 RISC-V C 调用约定放置它们的位置：在寄存器中。内核陷阱代码将用户寄存器保存到当前进程的陷阱帧中，内核代码可以在那里找到它们。内核函数 \lstinline{argint}、\lstinline{argaddr} 和 \lstinline{argfd} 从陷阱帧中检索第 n 个系统调用参数，分别为整数、指针或文件描述符。它们都调用 {\tt argraw} 来检索适当的已保存用户寄存器 \lineref{kernel/syscall.c:/^argraw/}。

一些系统调用传递指针作为参数，内核必须使用这些指针来读取或写入用户内存。例如，{\tt exec} 系统调用向内核传递一个指向用户空间中字符串参数的指针数组。这些指针带来了两个挑战。首先，用户程序可能有错误或恶意，可能会向内核传递无效指针或旨在欺骗内核访问内核内存而不是用户内存的指针。其次，xv6 内核页表映射与用户页表映射不同，因此内核不能使用普通指令从用户提供的地址加载或存储。

内核实现了安全地将数据传入和传出用户提供地址的函数。{\tt fetchstr} 是一个例子 \lineref{kernel/syscall.c:/^fetchstr/}。诸如 {\tt exec} 之类的文件系统调用使用 {\tt fetchstr} 从用户空间检索字符串文件名参数。\lstinline{fetchstr} 调用 \lstinline{copyinstr} 来完成艰苦的工作。

\indexcode{copyinstr} \lineref{kernel/vm.c:/^copyinstr/} 将最多 \lstinline{max} 字节从用户页表 \lstinline{pagetable} 中的虚拟地址 \lstinline{srcva} 复制到 \lstinline{dst}。由于 \lstinline{pagetable} 不是当前页表，因此 \lstinline{copyinstr} 使用 {\tt walkaddr}（它调用 {\tt walk}）在 \lstinline{pagetable} 中查找 \lstinline{srcva}，从而产生物理地址 \lstinline{pa0}。内核的页表将所有物理 RAM 映射到与其物理地址相等的虚拟地址。这使得 {\tt copyinstr} 可以直接将字符串字节从 {\tt pa0} 复制到 {\tt dst}。{\tt walkaddr} \lineref{kernel/vm.c:/^walkaddr/} 检查用户提供的虚拟地址是进程的用户地址空间的一部分，因此程序无法欺骗内核读取其他内存。一个类似的函数 {\tt copyout} 将数据从内核复制到用户提供的地址。

\section{来自内核空间的陷阱}
\label{s:ktraps}

Xv6 处理来自内核代码的陷阱的方式与处理来自用户代码的陷阱不同。当进入内核时，{\tt usertrap} 将 {\tt stvec} 指向位于 {\tt kernelvec} \lineref{kernel/kernelvec.S:/^kernelvec/} 的汇编代码。由于 {\tt kernelvec} 仅在 xv6 已在内核中时执行，因此 {\tt kernelvec} 可以依赖于 {\tt satp} 已设置为内核页表，并且堆栈指针指向有效的内核堆栈。{\tt kernelvec} 将所有 32 个寄存器压入堆栈，稍后将从中恢复它们，以便被中断的内核代码可以不受干扰地继续执行。

{\tt kernelvec} 将寄存器保存在被中断的内核线程的堆栈上，这是有道理的，因为寄存器值属于该线程。如果陷阱导致切换到另一个线程，这一点尤其重要——在这种情况下，陷阱实际上将从新线程的堆栈返回，而被中断线程的已保存寄存器安全地留在其堆栈上。

{\tt kernelvec} 在保存寄存器后跳转到 {\tt kerneltrap} \lineref{kernel/trap.c:/^kerneltrap/}。{\tt kerneltrap} 为两种类型的陷阱做好了准备：设备中断和异常。它调用 {\tt devintr} \lineref{kernel/trap.c:/^devintr/} 来检查和处理前者。如果陷阱不是设备中断，则它必须是异常，如果在 xv6 内核中发生，这始终是致命错误；内核调用 \lstinline{panic} 并停止执行。

如果 {\tt kerneltrap} 是由于定时器中断而被调用的，并且一个进程的内核线程正在运行（而不是调度程序线程），{\tt kerneltrap} 会调用 {\tt yield} 以让其他线程有机会运行。在某个时候，其中一个线程会让步，让我们的线程和它的 {\tt kerneltrap} 再次恢复。第~\ref{CH:SCHED}章解释了 {\tt yield} 中发生的事情。

当 {\tt kerneltrap} 的工作完成时，它需要返回到被陷阱中断的任何代码。因为 {\tt yield} 可能已经扰乱了 {\tt sepc} 和 {\tt sstatus} 中的先前模式，所以 {\tt kerneltrap} 在启动时保存了它们。它现在恢复这些控制寄存器并返回到 {\tt kernelvec} \lineref{kernel/kernelvec.S:/call.kerneltrap$/}。{\tt kernelvec} 从堆栈中弹出已保存的寄存器并执行 {\tt sret}，它将 {\tt sepc} 复制到 {\tt pc} 并恢复被中断的内核代码。

值得思考的是，如果 {\tt kerneltrap} 由于定时器中断而调用了 {\tt yield}，陷阱返回是如何发生的。

Xv6 在一个 CPU 从用户空间进入内核时，将该 CPU 的 {\tt stvec} 设置为 {\tt kernelvec}；你可以在 {\tt usertrap} \lineref{kernel/trap.c:/stvec.*kernelvec/} 中看到这一点。有一段时间窗口，内核已经开始执行但 {\tt stvec} 仍设置为 {\tt uservec}，至关重要的是在该窗口期间不能发生设备中断。幸运的是，RISC-V 在开始处理陷阱时总是禁用中断，而 {\tt usertrap} 在设置 {\tt stvec} 之后才再次启用它们。

\section{页错误异常}
\label{sec:pagefaults}

Xv6 对异常的响应相当无聊：如果异常发生在用户空间，内核会杀死出错的进程。如果异常发生在内核中，内核会 panic。真正的操作系统通常会以更有趣的方式做出响应。

举个例子，许多内核使用页错误来实现\indextext{写时复制（COW）fork}。为了解释写时复制 fork，请考虑第~\ref{CH:MEM}章中描述的 xv6 的 \lstinline{fork}。\lstinline{fork} 导致子进程的初始内存内容与 fork 时父进程的内存内容相同。Xv6 使用 \lstinline{uvmcopy} \lineref{kernel/vm.c:/^uvmcopy/} 来实现 fork，它为子进程分配物理内存并将父进程的内存复制到其中。如果子进程和父进程可以共享父进程的物理内存，效率会更高。然而，直接实现这一点是行不通的，因为它会导致父进程和子进程通过对共享堆栈和堆的写入来相互干扰彼此的执行。

父进程和子进程可以通过适当使用页表权限和页错误来安全地共享物理内存。当使用没有映射的虚拟地址，或者映射的 \lstinline{PTE_V} 标志被清除，或者映射的权限位（\lstinline{PTE_R}、\lstinline{PTE_W}、\lstinline{PTE_X}、\lstinline{PTE_U}）禁止正在尝试的操作时，CPU 会引发\indextext{页错误异常}。RISC-V 区分三种类型的页错误：加载页错误（由加载指令引起）、存储页错误（由存储指令引起）和指令页错误（由获取要执行的指令引起）。\lstinline{scause} 寄存器指示页错误的类型，\indexcode{stval} 寄存器包含无法转换的地址。

COW fork 的基本计划是父进程和子进程最初共享所有物理页，但每个进程都将它们映射为只读（\lstinline{PTE_W} 标志被清除）。父进程和子进程可以从共享的物理内存中读取。如果任何一方写入给定页面，RISC-V CPU 会引发页错误异常。内核的陷阱处理程序通过分配一个新的物理内存页并将出错地址映射到的物理页复制到其中来响应。内核更改出错进程页表中的相关 PTE，以指向副本并允许写入和读取，然后在导致错误的指令处恢复出错进程。因为 PTE 现在允许写入，所以重新执行的指令将不会出现错误地执行。写时复制需要簿记来帮助决定何时可以释放物理页，因为每个页可以被可变数量的页表引用，具体取决于 fork、页错误、exec 和退出的历史记录。这种簿记允许一个重要的优化：如果一个进程发生存储页错误并且物理页仅从该进程的页表引用，则不需要复制。

写时复制使 \lstinline{fork} 更快，因为 \lstinline{fork} 不需要复制内存。一些内存稍后在写入时必须被复制，但通常情况下，大部分内存永远不需要被复制。一个常见的例子是 \lstinline{fork} 后跟 \lstinline{exec}：\lstinline{fork} 之后可能会写入几页，但随后子进程的 \lstinline{exec} 会释放从父进程继承的大部分内存。写时复制 \lstinline{fork} 消除了复制这部分内存的需要。此外，COW fork 是透明的：应用程序无需修改即可受益。

页表和页错误的结合开启了除 COW fork 之外的各种有趣的可能性。另一个广泛使用的功能称为\indextext{惰性分配}，它有两个部分。首先，当应用程序通过调用 \lstinline{sbrk} 请求更多内存时，内核会记录大小的增加，但不会分配物理内存，也不会为新的虚拟地址范围创建 PTE。其次，在这些新地址之一上发生页错误时，内核会分配一个物理内存页并将其映射到页表中。与 COW fork 一样，内核可以对应用程序透明地实现惰性分配。

由于应用程序通常会请求比它们需要的更多的内存，因此惰性分配是一个胜利：对于应用程序从不使用的页面，内核根本不需要做任何工作。此外，如果应用程序请求大幅增加地址空间，那么没有惰性分配的 \lstinline{sbrk} 是昂贵的：如果一个应用程序请求一千兆字节的内存，内核必须分配并清零 262,144 个 4096 字节的页面。惰性分配允许将此成本分摊到一段时间内。另一方面，惰性分配会带来页错误的额外开销，这涉及用户/内核转换。操作系统可以通过为每个页错误分配一批连续的页面而不是一个页面，以及通过专门化此类页错误的内核进入/退出代码来降低此成本。

利用页错误的另一个广泛使用的功能是\indextext{按需分页}。在 \lstinline{exec} 中，xv6 在启动应用程序之前将应用程序的所有文本和数据加载到内存中。由于应用程序可能很大并且从磁盘读取需要时间，因此这种启动成本对用户来说是显而易见的。为了减少启动时间，现代内核最初不会将可执行文件加载到内存中，而只是创建用户页表，并将所有 PTE 标记为无效。内核启动程序运行；每次程序第一次使用页面时，都会发生页错误，作为响应，内核从磁盘读取页面的内容并将其映射到用户地址空间中。与 COW fork 和惰性分配一样，内核可以对应用程序透明地实现此功能。

计算机上运行的程序可能需要比计算机拥有的 RAM 更多的内存。为了优雅地应对，操作系统可以实现\indextext{分页到磁盘}。其思想是仅在 RAM 中存储一小部分用户页面，其余部分存储在磁盘上的\indextext{分页区域}中。内核将对应于存储在分页区域中（因此不在 RAM 中）的内存的 PTE 标记为无效。如果应用程序尝试使用已“换出”到磁盘的页面之一，应用程序将发生页错误，并且该页面必须被“换入”：内核陷阱处理程序将分配一页物理 RAM，从磁盘将页面读入 RAM，并修改相关的 PTE 以指向 RAM。

如果需要换入一个页面但没有空闲的物理 RAM 会发生什么？在这种情况下，内核必须首先通过将其换出或“驱逐”到磁盘上的分页区域来释放一个物理页面，并将引用该物理页面的 PTE 标记为无效。驱逐是昂贵的，因此如果分页不频繁，其性能最佳：如果应用程序仅使用其内存页面的一个子集，并且这些子集的并集适合 RAM。此属性通常被称为具有良好的引用局部性。与许多虚拟内存技术一样，内核通常以对应用程序透明的方式实现分页到磁盘。

计算机通常在几乎没有或没有“空闲”物理内存的情况下运行，无论硬件提供多少 RAM。例如，云提供商在单台机器上多路复用许多客户，以经济高效地使用其硬件。另一个例子是，用户在少量物理内存的智能手机上运行许多应用程序。在这种情况下，分配一个页面可能需要首先驱逐一个现有页面。因此，当空闲物理内存稀缺时，分配是昂贵的。

当空闲内存稀缺且程序仅积极使用其分配内存的一小部分时，惰性分配和按需分页尤其有利。这些技术还可以避免在分配或加载但从未使用或在使用前被驱逐的页面上浪费的工作。

结合分页和页错误异常的其他功能包括自动扩展堆栈和\indextext{内存映射文件}，这些文件是程序使用 \texttt{mmap} 系统调用映射到其地址空间的文件，以便程序可以使用加载和存储指令来读写它们。

\section{现实世界}

蹦床和陷阱帧可能看起来过于复杂。一个驱动力是 RISC-V 有意在强制陷阱时尽可能少地做事情，以允许非常快速的陷阱处理的可能性，这被证明是重要的。结果是，内核陷阱处理程序的前几条指令实际上必须在用户环境中执行：用户页表和用户寄存器内容。并且陷阱处理程序最初不知道诸如正在运行的进程的身份或内核页表的地址之类的有用事实。一个解决方案是可能的，因为 RISC-V 提供了受保护的地方，内核可以在进入用户空间之前隐藏信息：{\tt sscratch} 寄存器和指向内核内存但受缺少 \lstinline{PTE_U} 保护的用户页表条目。Xv6 的蹦床和陷阱帧利用了这些 RISC-V 功能。

如果内核内存被映射到每个进程的用户页表中（\lstinline{PTE_U} 清除），则可以消除对特殊蹦床页的需求。这也将消除在从用户空间陷入内核时进行页表切换的需要。这反过来又允许内核中的系统调用实现利用当前进程的用户内存被映射的优势，从而允许内核代码直接解引用用户指针。许多操作系统已经使用这些思想来提高效率。Xv6 避免了它们，以减少由于无意中使用用户指针而导致内核中安全漏洞的机会，并减少为确保用户和内核虚拟地址不重叠所需的一些复杂性。

生产操作系统实现了写时复制 fork、惰性分配、按需分页、分页到磁盘、内存映射文件等。此外，生产操作系统会尝试在物理内存的所有区域中存储有用的东西，通常在进程不使用的内存中缓存文件内容。

生产操作系统还向应用程序提供系统调用来管理其地址空间，并通过 {\tt mmap}、{\tt munmap} 和 {\tt sigaction} 系统调用实现自己的页错误处理，以及提供将内存固定到 RAM 的调用（参见 {\tt mlock}）和建议内核应用程序计划如何使用其内存的调用（参见 {\tt madvise}）。

\section{练习}

\begin{enumerate}

\item 函数 {\tt copyin} 和 {\tt copyinstr} 在软件中遍历用户页表。设置内核页表，以便内核映射了用户程序，并且 {\tt copyin} 和 {\tt copyinstr} 可以使用 {\tt memcpy} 将系统调用参数复制到内核空间，依赖硬件来完成页表遍历。

\item 实现惰性内存分配。

\item 实现 COW fork。

\item 有没有办法消除每个用户地址空间中的特殊 {\tt TRAPFRAME} 页面映射？例如，是否可以修改 {\tt uservec} 以简单地将 32 个用户寄存器推送到内核堆栈，或将它们存储在 {\tt proc} 结构中？

\item xv6 是否可以修改以消除特殊的 {\tt TRAMPOLINE} 页面映射？

\item 实现 {\tt mmap}。

\end{enumerate}