\chapter{Summary}
\label{CH:SUM}

This text introduced the main ideas in operating systems by studying one
operating system, xv6, line by line.  Some code lines embody the essence of the
main ideas (e.g., context switching, user/kernel boundary, locks, etc.) and each
line is important; other code lines provide an illustration of how to implement
a particular operating system idea and could easily be done in different ways
(e.g., a better algorithm for scheduling, better on-disk data structures to
represent files, better logging to allow for concurrent transactions, etc.).
All the ideas were illustrated in the context of one particular, very successful
system call interface, the Unix interface, but those ideas carry over to the
design of other operating systems.

