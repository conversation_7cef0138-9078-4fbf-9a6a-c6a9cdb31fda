\chapter{锁}
\label{CH:LOCK}

大多数内核，包括 xv6 在内，都会交错执行多个活动。交错的一个来源是多处理器硬件：具有多个独立执行 CPU 的计算机，例如 xv6 的 RISC-V。这些多个 CPU 共享物理内存，xv6 利用这种共享来维护所有 CPU 读写的数据结构。这种共享带来了一种可能性，即一个 CPU 正在读取一个数据结构，而另一个 CPU 正在中途更新它，甚至多个 CPU 同时更新同一个数据；如果没有仔细的设计，这种并行访问很可能会产生不正确的结果或损坏数据结构。即使在单处理器上，内核也可能在多个线程之间切换 CPU，导致它们的执行交错。最后，如果设备中断处理程序修改了与某些可中断代码相同的数据，那么在错误的时间发生中断可能会损坏数据。术语\indextext{并发}指的是多个指令流由于多处理器并行、线程切换或中断而交错执行的情况。

内核中充满了并发访问的数据。例如，两个 CPU 可以同时调用 {\tt kalloc}，从而并发地从空闲列表的头部弹出。内核设计者喜欢允许大量的并发，因为这可以通过并行来提高性能和响应能力。然而，因此，内核设计者必须确信尽管存在这种并发，代码仍然是正确的。有很多方法可以获得正确的代码，其中一些比其他的更容易推理。旨在在并发下保证正确性的策略以及支持它们的抽象称为\indextext{并发控制}技术。

Xv6 根据情况使用多种并发控制技术；还有更多可能的技术。本章重点介绍一种广泛使用的技术：\indextext{锁}。锁提供互斥，确保一次只有一个 CPU 可以持有锁。如果程序员为每个共享数据项关联一个锁，并且代码在使用一个项时总是持有相关的锁，那么这个项将一次只被一个 CPU 使用。在这种情况下，我们说锁保护了数据项。虽然锁是一种易于理解的并发控制机制，但锁的缺点是它们会限制性能，因为它们会序列化并发操作。

本章的其余部分将解释为什么 xv6 需要锁，xv6 如何实现它们，以及如何使用它们。

% 以下内容与无锁代码最相关：
%
% 一个关键的观察是，当你看 xv6 中的一些代码时，你必须问自己并发代码是否可能通过修改它所依赖的数据（或硬件资源）来改变代码的预期行为。
% 你必须记住，编译器可能会将一个 C 语句转换成多个机器指令，并且这些指令的执行方式可能与在其他 CPU 上执行的指令交错。
% 也就是说，你不能假设页面上的 C 代码行是原子执行的。
% 并发使得对正确性的推理变得困难。

%%
\section{竞争}
%%

\begin{figure}[t]
\center
\includegraphics[scale=0.8]{fig/smp.pdf}
\caption{简化的 SMP 架构}
\label{fig:smp}
\end{figure}

作为为什么我们需要锁的一个例子，考虑两个进程，它们的子进程已经退出，在两个不同的 CPU 上调用 {\tt wait}。{\tt wait} 会释放子进程的内存。因此，在每个 CPU 上，内核都会调用 {\tt kfree} 来释放子进程的内存页。内核分配器维护一个链表：\lstinline{kalloc()} \lineref{kernel/kalloc.c:/^kalloc/} 从一个空闲页列表中弹出一个内存页，而 \lstinline{kfree()} \lineref{kernel/kalloc.c:/^kfree/} 将一个页推入空闲列表。为了获得最佳性能，我们可能希望两个父进程的 {\tt kfree}s 能够并行执行，而无需等待对方，但考虑到 xv6 的 {\tt kfree} 实现，这是不正确的。

图~\ref{fig:smp} 更详细地说明了这种情况：空闲页的链表位于由两个 CPU 共享的内存中，它们使用加载和存储指令来操作该列表。（实际上，处理器有缓存，但从概念上讲，多处理器系统的行为就像只有一个共享内存一样。）如果没有并发请求，你可能会像下面这样实现一个列表 \lstinline{push} 操作：
\begin{lstlisting}[numbers=left,firstnumber=1]
    struct element {
      int data;
      struct element *next;
    };

    struct element *list = 0;

    void
    push(int data)
    {
      struct element *l;

      l = malloc(sizeof *l);
      l->data = data;
      l->next = list; (*@\label{line:next}@*)
      list = l;  (*@\label{line:list}@*)
   }
\end{lstlisting}

\begin{figure}[t]
\center
\includegraphics[scale=0.5]{fig/race.pdf}
\caption{竞争示例}
\label{fig:race}
\end{figure}
如果单独执行，这个实现是正确的。但是，如果多个副本并发执行，代码就不正确了。如果两个 CPU 同时执行 \lstinline{push}，它们都可能在执行第~\ref{line:list} 行之前执行第~\ref{line:next} 行，如图~\ref{fig:smp} 所示，这将导致图~\ref{fig:race} 所示的错误结果。这样就会有两个列表元素的 \lstinline{next} 被设置为 \lstinline{list} 的旧值。当两个对 \lstinline{list} 的赋值发生在第~\ref{line:list} 行时，第二个赋值会覆盖第一个；第一个赋值所涉及的元素将丢失。

第~\ref{line:list} 行的更新丢失是\indextext{竞争}的一个例子。竞争是指一个内存位置被并发访问，并且至少有一个访问是写操作的情况。竞争通常是错误的标志，要么是更新丢失（如果访问是写操作），要么是读取了未完全更新的数据结构。竞争的结果取决于编译器生成的机器代码、两个相关 CPU 的时序以及内存系统如何对它们的内存操作进行排序，这使得由竞争引起的错误难以重现和调试。例如，在调试 \lstinline{push} 时添加打印语句可能会改变执行的时序，从而使竞争消失。

避免竞争的常用方法是使用锁。锁确保\indextext{互斥}，以便一次只有一个 CPU 可以执行 \lstinline{push} 的敏感代码行；这使得上述情况不可能发生。正确加锁的上述代码版本只添加了几行（以黄色突出显示）：
\begin{lstlisting}[numbers=left,firstnumber=6]
   struct element *list = 0;
   (*@\hl{struct lock listlock;}@*)

   void
   push(int data)
   {
     struct element *l;
     l = malloc(sizeof *l); (*@\label{line:malloc}@*)
     l->data = data;

     (*@\hl{acquire(\&listlock);} @*)
     l->next = list;     (*@\label{line:next1}@*)
     list = l;           (*@\label{line:list1}@*)
     (*@\hl{release(\&listlock)}; @*)
   }
\end{lstlisting}
\lstinline{acquire} 和 \lstinline{release} 之间的指令序列通常被称为\indextext{临界区}。通常说锁在保护 \lstinline{list}。

当我们说一个锁保护数据时，我们真正的意思是锁保护了适用于该数据的一些不变量集合。不变量是数据结构在操作中保持的属性。通常，一个操作的正确行为取决于操作开始时不变量为真。操作可能会暂时违反不变量，但必须在完成前重新建立它们。例如，在链表的情况下，不变量是 \lstinline{list} 指向列表中的第一个元素，并且每个元素的 \lstinline{next} 字段指向下一个元素。 \lstinline{push} 的实现暂时违反了这个不变量：在第~\ref{line:next1} 行中， \lstinline{l} 指向下一个列表元素，但 \lstinline{list} 还没有指向 \lstinline{l} （在第~\ref{line:list1} 行重新建立）。我们上面研究的竞争发生是因为第二个 CPU 在列表不变量被（暂时）违反时执行了依赖于这些不变量的代码。正确使用锁可以确保一次只有一个 CPU 可以在临界区内操作数据结构，这样就不会有 CPU 在数据结构的不变量不成立时执行数据结构操作。

你可以把锁看作是\indextext{序列化}并发的临界区，使它们一次一个地运行，从而保持不变量（假设临界区在隔离状态下是正确的）。你也可以认为由同一个锁保护的临界区彼此之间是原子的，这样每个临界区只能看到来自早期临界区的完整变更集，而永远不会看到部分完成的更新。

虽然锁对于正确性很有用，但它们天生会限制性能。例如，如果两个进程同时调用 {\tt kfree}，锁将序列化这两个临界区，因此在不同的 CPU 上运行它们没有任何好处。我们说，如果多个进程同时想要同一个锁，它们就会发生\indextext{冲突}，或者说锁经历了\indextext{争用}。内核设计中的一个主要挑战是避免锁争用以追求并行性。Xv6 在这方面做得很少，但复杂的内核会专门组织数据结构和算法来避免锁争用。在列表的例子中，一个内核可能会为每个 CPU 维护一个单独的空闲列表，并且只有在当前 CPU 的列表为空并且必须从另一个 CPU 窃取内存时才接触另一个 CPU 的空闲列表。其他用例可能需要更复杂的设计。

锁的放置对性能也很重要。例如，在 \lstinline{push} 中将 \lstinline{acquire} 移到更早的位置，在第~\ref{line:malloc} 行之前是正确的。但这可能会降低性能，因为这样一来，对 \lstinline{malloc} 的调用就会被序列化。下面的“使用锁”一节提供了一些关于在何处插入 \lstinline{acquire} 和 \lstinline{release} 调用的指导方针。
%%
\section{代码：锁}
%%
Xv6 有两种类型的锁：自旋锁和睡眠锁。我们从自旋锁开始。Xv6 将自旋锁表示为 \indexcode{struct spinlock} \lineref{kernel/spinlock.h:/struct.spinlock/}。结构中重要的字段是 \lstinline{locked}，这是一个字，当锁可用时为零，当锁被持有时为非零。逻辑上，xv6 应该通过执行类似以下代码来获取锁：
\begin{lstlisting}[numbers=left,firstnumber=21]
   void
   acquire(struct spinlock *lk) // 无法工作！
   {
     for(;;) {
       if(lk->locked == 0) {  (*@\label{line:testlocked}@*)
         lk->locked = 1;      (*@\label{line:assign}@*)
         break;
       }
     }
   }
\end{lstlisting}
不幸的是，这个实现在多处理器上不能保证互斥。可能会发生两个 CPU 同时到达第~\ref{line:testlocked} 行，看到 \lstinline{lk->locked} 为零，然后都通过执行第~\ref{line:assign} 行来获取锁。此时，两个不同的 CPU 持有锁，这违反了互斥属性。我们需要的是一种使第 \ref{line:testlocked} 行和第 \ref{line:assign} 行作为一个\indextext{原子}（即不可分割的）步骤执行的方法。

由于锁被广泛使用，多核处理器通常提供实现第~\ref{line:testlocked} 和 \ref{line:assign} 行原子版本的指令。在 RISC-V 上，这个指令是 \lstinline{amoswap r, a}。\lstinline{amoswap} 读取内存地址 {\tt a} 的值，将寄存器 {\tt r} 的内容写入该地址，并将其读取的值放入 {\tt r} 中。也就是说，它交换了寄存器和内存地址的内容。它原子地执行这个序列，使用特殊的硬件来防止任何其他 CPU 在读和写之间使用该内存地址。

Xv6 的 \indexcode{acquire} \lineref{kernel/spinlock.c:/^acquire/} 使用了可移植的 C 库调用 \lstinline{__sync_lock_test_and_set}，它最终归结为 \lstinline{amoswap} 指令；返回值是 \lstinline{lk->locked} 的旧（交换过的）内容。 \lstinline{acquire} 函数将交换包装在一个循环中，重试（自旋）直到它获得锁。每次迭代都将一个 1 交换到 \lstinline{lk->locked} 中并检查之前的值；如果之前的值是零，那么我们就获得了锁，并且交换将把 \lstinline{lk->locked} 设置为 1。如果之前的值是 1，那么某个其他的 CPU 持有锁，并且我们原子地将 1 交换到 \lstinline{lk->locked} 中并不会改变它的值。

一旦获取了锁，\lstinline{acquire} 会为了调试而记录获取锁的 CPU。\lstinline{lk->cpu} 字段受锁保护，并且只能在持有锁时更改。

函数 \indexcode{release} \lineref{kernel/spinlock.c:/^release/} 与 \lstinline{acquire} 相反：它清除 \lstinline{lk->cpu} 字段，然后释放锁。从概念上讲，释放只需要将零赋给 \lstinline{lk->locked}。C 标准允许编译器用多个存储指令来实现一个赋值，所以一个 C 赋值对于并发代码来说可能是非原子的。相反，\lstinline{release} 使用 C 库函数 \lstinline{__sync_lock_release} 来执行原子赋值。这个函数也归结为一个 RISC-V \lstinline{amoswap} 指令。
%%
\section{代码：使用锁}
%%
Xv6 在很多地方使用锁来避免竞争。如上所述，\lstinline{kalloc} \lineref{kernel/kalloc.c:/^kalloc/} 和 \lstinline{kfree} \lineref{kernel/kalloc.c:/^kfree/} 是一个很好的例子。尝试练习 1 和 2，看看如果这些函数省略了锁会发生什么。你很可能会发现很难触发不正确的行为，这表明很难可靠地测试代码是否没有锁错误和竞争。Xv6 很可能还有尚未发现的竞争。

使用锁的一个难点是决定使用多少个锁，以及每个锁应该保护哪些数据和不变量。有几个基本原则。首先，任何时候一个变量可以被一个 CPU 写入，而同时另一个 CPU 可以读取或写入它，就应该使用一个锁来防止这两个操作重叠。其次，记住锁保护的是不变量：如果一个不变量涉及多个内存位置，通常所有这些位置都需要由一个锁来保护，以确保不变量得以维持。

上面的规则说明了什么时候需要锁，但没有说明什么时候不需要锁，为了效率，不过度加锁是很重要的，因为锁会降低并行性。如果并行性不重要，那么可以安排只有一个线程，而不用担心锁。一个简单的内核可以在多处理器上通过一个单一的锁来实现这一点，这个锁必须在进入内核时获取，在退出内核时释放（尽管阻塞系统调用如管道读取或 \lstinline{wait} 会带来问题）。许多单处理器操作系统已经使用这种方法被转换成在多处理器上运行，有时被称为“大内核锁”，但这种方法牺牲了并行性：一次只有一个 CPU 可以在内核中执行。如果内核进行任何繁重的计算，使用更大的一组更细粒度的锁会更有效率，这样内核就可以在多个 CPU 上同时执行。

作为粗粒度锁定的一个例子，xv6 的 \lstinline{kalloc.c} 分配器有一个由单个锁保护的单个空闲列表。如果不同 CPU 上的多个进程试图同时分配页面，每个进程都必须通过在 {\tt acquire} 中自旋来等待轮到自己。自旋会浪费 CPU 时间，因为它不是有用的工作。如果对锁的争用浪费了相当一部分 CPU 时间，那么也许可以通过改变分配器设计来提高性能，使其具有多个空闲列表，每个列表都有自己的锁，以允许真正的并行分配。

作为细粒度锁定的一个例子，xv6 为每个文件都有一个单独的锁，这样操作不同文件的进程通常可以继续进行而无需等待对方的锁。如果想要允许进程同时写入同一文件的不同区域，文件锁定方案可以变得更加细粒度。最终，锁的粒度决策需要由性能测量和复杂性考虑来驱动。

随着后续章节解释 xv6 的每个部分，它们将提到 xv6 使用锁来处理并发的例子。作为预览，图~\ref{fig:locktable} 列出了 xv6 中的所有锁。

\begin{figure}[t]
\center
\begin{tabular}{ll}
{\bf 锁} & {\bf 描述} \\
\midrule
bcache.lock & 保护块缓冲缓存条目的分配 \\
cons.lock & 序列化对控制台硬件的访问，避免输出混杂 \\
ftable.lock & 序列化文件表中 struct file 的分配 \\
itable.lock & 保护内存中 inode 条目的分配 \\
vdisk\_lock & 序列化对磁盘硬件和 DMA 描述符队列的访问 \\
kmem.lock & 序列化内存分配 \\
log.lock & 序列化对事务日志的操作 \\
pipe's pi->lock & 序列化对每个管道的操作 \\
pid\_lock & 序列化 next\_pid 的增量 \\
proc's p->lock & 序列化对进程状态的更改 \\
wait\_lock & 帮助 wait 避免丢失唤醒 \\
tickslock & 序列化对滴答计数器的操作 \\
inode's ip->lock & 序列化对每个 inode 及其内容的操作 \\
buf's b->lock & 序列化对每个块缓冲区的操作 \\
\end{tabular}
\caption{xv6 中的锁}
\label{fig:locktable}
\end{figure}

%%
\section{死锁和锁顺序}
%%
如果内核中的一个代码路径必须同时持有多个锁，那么所有代码路径以相同的顺序获取这些锁是很重要的。如果它们不这样做，就有\indextext{死锁}的风险。假设 xv6 中的两个代码路径需要锁 A 和 B，但代码路径 1 按 A 然后 B 的顺序获取锁，而另一个路径按 B 然后 A 的顺序获取它们。假设线程 T1 执行代码路径 1 并获取锁 A，线程 T2 执行代码路径 2 并获取锁 B。接下来 T1 将尝试获取锁 B，而 T2 将尝试获取锁 A。两个获取都将无限期地阻塞，因为在这两种情况下，另一个线程都持有需要的锁，并且在它的获取返回之前不会释放它。为了避免这种死锁，所有代码路径都必须以相同的顺序获取锁。需要一个全局锁获取顺序意味着锁实际上是每个函数规范的一部分：调用者必须以导致锁按约定顺序获取的方式调用函数。

由于 \lstinline{sleep} 的工作方式（参见第~\ref{CH:SCHED} 章），Xv6 有许多涉及每个进程锁（每个 \lstinline{struct proc} 中的锁）的长度为 2 的锁顺序链。例如，\lstinline{consoleintr} \lineref{kernel/console.c:/^consoleintr/} 是处理键入字符的中断例程。当一个新行到达时，任何等待控制台输入的进程都应该被唤醒。为此，\lstinline{consoleintr} 在调用 \indexcode{wakeup} 时持有 \lstinline{cons.lock}，\indexcode{wakeup} 会获取等待进程的锁以唤醒它。因此，全局死锁避免锁顺序包括 \lstinline{cons.lock} 必须在任何进程锁之前获取的规则。文件系统代码包含 xv6 最长的锁链。例如，创建一个文件需要同时持有一个目录的锁、一个新文件 inode 的锁、一个磁盘块缓冲区的锁、磁盘驱动程序的 \lstinline{vdisk_lock} 以及调用进程的 \lstinline{p->lock}。为了避免死锁，文件系统代码总是按前面句子中提到的顺序获取锁。

遵守全局死锁避免顺序可能出人意料地困难。有时锁顺序与逻辑程序结构冲突，例如，也许代码模块 M1 调用模块 M2，但锁顺序要求在 M1 中的锁之前获取 M2 中的锁。有时锁的身份事先不知道，也许是因为必须持有一个锁才能发现下一个要获取的锁的身份。这种情况在文件系统中查找路径名的连续组件时出现，在 {\tt wait} 和 {\tt exit} 的代码中搜索进程表以查找子进程时也出现。最后，死锁的危险通常是对锁方案可以做得多细粒度的一个约束，因为更多的锁通常意味着更多的死锁机会。避免死锁的需要通常是内核实现中的一个主要因素。

\section{可重入锁}

似乎可以通过使用\indextext{可重入锁}来避免一些死锁和锁排序挑战，可重入锁也称为\indextext{递归锁}。其思想是，如果锁由一个进程持有，并且该进程再次尝试获取该锁，那么内核可以允许这样做（因为该进程已经拥有该锁），而不是像 xv6 内核那样调用 panic。

然而，事实证明，可重入锁使得对并发的推理更加困难：可重入锁打破了锁使临界区相对于其他临界区是原子的直觉。考虑以下函数 \lstinline{f} 和 \lstinline{g}，以及一个假设的函数 \lstinline{h}：
\begin{lstlisting}
struct spinlock lock;
int data = 0; // 受 lock 保护

f() {
  acquire(&lock);
  if(data == 0){
    call_once();
    h();
    data = 1;
  }
  release(&lock);
}

g() {
  acquire(&lock);
  if(data == 0){
    call_once();
    data = 1;
  }
  release(&lock);
}

h() {
    ...
}
\end{lstlisting}

看这段代码，直觉是 \lstinline{call_once} 只会被调用一次：要么由 \lstinline{f} 调用，要么由 \lstinline{g} 调用，但不会被两者都调用。

但是如果允许可重入锁，并且 \lstinline{h} 恰好调用了 \lstinline{g}，\lstinline{call_once} 将被调用\emph{两次}。

如果不允许可重入锁，那么 \lstinline{h} 调用 \lstinline{g} 会导致死锁，这也不是很好。但是，假设调用 \lstinline{call_once} 两次是一个严重的错误，那么死锁是更可取的。内核开发人员会观察到死锁（内核 panic）并可以修复代码以避免它，而调用 \lstinline{call_once} 两次可能会悄无声息地导致一个难以追踪的错误。

因此，xv6 使用更容易理解的非可重入锁。然而，只要程序员牢记锁定规则，任何一种方法都可以工作。如果 xv6 要使用可重入锁，就需要修改 \lstinline{acquire} 来注意锁当前正被调用线程持有。还需要在 struct spinlock 中添加一个嵌套获取的计数，风格类似于接下来讨论的 \lstinline{push_off}。

\section{锁和中断处理程序}
\label{s:lockinter}

%%
一些 xv6 自旋锁保护由线程和中断处理程序共同使用的数据。例如，\lstinline{clockintr} 时钟中断处理程序可能在内核线程在 \indexcode{sys_sleep} \lineref{kernel/sysproc.c:/ticks0.=.ticks/} 中读取 \lstinline{ticks} 的大约同一时间递增 \indexcode{ticks} \lineref{kernel/trap.c:/^clockintr/}。锁 \indexcode{tickslock} 序列化了这两个访问。

自旋锁和中断的交互带来了一个潜在的危险。假设 \indexcode{sys_sleep} 持有 \indexcode{tickslock}，并且它的 CPU 被一个时钟中断打断。\lstinline{clockintr} 会尝试获取 \lstinline{tickslock}，看到它被持有，然后等待它被释放。在这种情况下，\lstinline{tickslock} 将永远不会被释放：只有 \lstinline{sys_sleep} 可以释放它，但 \lstinline{sys_sleep} 在 \lstinline{clockintr} 返回之前不会继续运行。所以 CPU 将会死锁，任何需要这两个锁的代码也会冻结。

为了避免这种情况，如果一个自旋锁被中断处理程序使用，一个 CPU 绝不能在启用中断的情况下持有该锁。Xv6 更为保守：当一个 CPU 获取任何锁时，xv6 总是禁用该 CPU 上的中断。中断仍然可能在其他 CPU 上发生，所以一个中断的 \lstinline{acquire} 可以等待一个线程释放一个自旋锁；只是不能在同一个 CPU 上。

当一个 CPU 不持有自旋锁时，Xv6 会重新启用中断；它必须做一些簿记工作来处理嵌套的临界区。 \lstinline{acquire} 调用 \indexcode{push_off} \lineref{kernel/spinlock.c:/^push_off/}， \lstinline{release} 调用 \indexcode{pop_off} \lineref{kernel/spinlock.c:/^pop_off/} 来跟踪当前 CPU 上锁的嵌套级别。当该计数达到零时，\lstinline{pop_off} 会恢复最外层临界区开始时存在的中断启用状态。\lstinline{intr_off} 和 \lstinline{intr_on} 函数执行 RISC-V 指令来分别禁用和启用中断。

\indexcode{acquire} 在设置 \lstinline{lk->locked} \lineref{kernel/spinlock.c:/sync_lock_test_and_set/} 之前严格调用 \lstinline{push_off} 是很重要的。如果这两者颠倒了，就会有一个很短的时间窗口，当锁被持有时中断是启用的，一个不幸的定时中断将会使系统死锁。类似地，\indexcode{release} 仅在释放锁 \lineref{kernel/spinlock.c:/sync_lock_release/} 之后才调用 \indexcode{pop_off} 也是很重要的。

%%
\section{指令和内存排序}
%%

很自然地会认为程序是按照源代码语句出现的顺序执行的。对于单线程代码来说，这是一个合理的心理模型，但是当多个线程通过共享内存交互时，这是不正确的~\cite{riscv:user,boehm04}。一个原因是编译器发出的加载和存储指令的顺序与源代码所暗示的顺序不同，并且可能完全省略它们（例如通过在寄存器中缓存数据）。另一个原因是 CPU 可能会为了提高性能而乱序执行指令。例如，CPU 可能会注意到在一个串行指令序列中，A 和 B 互不依赖。CPU 可能会先启动指令 B，要么是因为它的输入比 A 的输入先准备好，要么是为了重叠 A 和 B 的执行。

作为一个可能出错的例子，在 \lstinline{push} 的这段代码中，如果编译器或 CPU 将对应于第~\ref{line:next2} 行的存储移动到第~\ref{line:release} 行的 \lstinline{release} 之后，那将是一场灾难：
\begin{lstlisting}[numbers=left,firstnumber=1]
      l = malloc(sizeof *l);
      l->data = data;
      acquire(&listlock);
      l->next = list;   (*@\label{line:next2}@*)
      list = l;
      release(&listlock);  (*@\label{line:release}@*)
\end{lstlisting}
如果发生了这种重排序，将会有一个时间窗口，在此期间另一个 CPU 可以获取锁并观察到更新后的 \lstinline{list}，但会看到一个未初始化的 \lstinline{list->next}。

好消息是，编译器和 CPU 通过遵循一组称为\indextext{内存模型}的规则来帮助并发程序员，并通过提供一些原语来帮助程序员控制重排序。

为了告诉硬件和编译器不要重排序，xv6 在 \lstinline{acquire} \lineref{kernel/spinlock.c:/^acquire/} 和 \lstinline{release} \lineref{kernel/spinlock.c:/^release/} 中都使用了 \lstinline{__sync_synchronize()}。\lstinline{__sync_synchronize()} 是一个\indextext{内存屏障}：它告诉编译器和 CPU 不要在屏障的两侧重排序加载或存储。xv6 的 \lstinline{acquire} 和 \lstinline{release} 中的屏障在几乎所有重要的情况下都强制了顺序，因为 xv6 在访问共享数据时使用锁。第~\ref{CH:LOCK2} 章讨论了一些例外情况。

%%
\section{睡眠锁}
%%

% 这部分内容大量引用了 sleep、yield 等，所以也许应该放在后面，在 sched.tex 中的 sleep/wakeup 之后。
% 也许在 lock2.tex 中

有时 xv6 需要长时间持有一个锁。例如，文件系统（第~\ref{CH:FS} 章）在磁盘上读写文件内容时会保持文件锁定，而这些磁盘操作可能需要几十毫秒。如果另一个进程想要获取它，长时间持有自旋锁会导致浪费，因为获取进程在自旋时会浪费很长时间的 CPU。自旋锁的另一个缺点是进程在保留自旋锁的同时不能让出 CPU；我们希望这样做，以便其他进程可以在持有锁的进程等待磁盘时使用 CPU。在持有自旋锁时让出是非法的，因为如果第二个线程然后尝试获取自旋锁，可能会导致死锁；因为 {\tt acquire} 不会让出 CPU，第二个线程的自旋可能会阻止第一个线程运行和释放锁。
% 确实，时钟中断可能会强制从第二个线程切换回第一个线程——但前提是第二个线程没有已经持有一个或多个锁。
% 同样正确的是，这种危险在单处理器上最明显，但它也可能在多处理器上随着更长的线程链而出现。
在持有锁时让出也会违反在持有自旋锁时必须关闭中断的要求。因此，我们想要一种在等待获取时让出 CPU，并在持有锁时允许让出（和中断）的锁。

Xv6 以\indextext{睡眠锁}的形式提供了这种锁。\lstinline{acquiresleep} \lineref{kernel/sleeplock.c:/^acquiresleep/} 在等待时让出 CPU，使用的技术将在第~\ref{CH:SCHED} 章中解释。在较高的层次上，睡眠锁有一个由自旋锁保护的 \lstinline{locked} 字段，\lstinline{acquiresleep} 对 \lstinline{sleep} 的调用会原子地让出 CPU 并释放自旋锁。结果是其他线程可以在 \lstinline{acquiresleep} 等待时执行。

因为睡眠锁保持中断启用，所以它们不能在中断处理程序中使用。因为 \lstinline{acquiresleep} 可能会让出 CPU，所以睡眠锁不能在自旋锁临界区内使用（尽管自旋锁可以在睡眠锁临界区内使用）。

自旋锁最适合短的临界区，因为等待它们会浪费 CPU 时间；睡眠锁适用于冗长的操作。

%%
\section{现实世界}
%%
尽管对并发原语和并行性进行了多年的研究，但使用锁进行编程仍然具有挑战性。通常最好将锁隐藏在更高级别的构造中，如同步队列，尽管 xv6 没有这样做。如果你使用锁编程，明智的做法是使用一个试图识别竞争的工具，因为很容易忽略一个需要锁的不变量。

大多数操作系统支持 POSIX 线程（Pthreads），它允许一个用户进程有多个线程在不同的 CPU 上并发运行。Pthreads 支持用户级锁、屏障等。Pthreads 还允许程序员可选地指定一个锁应该是可重入的。

在用户级别支持 Pthreads 需要操作系统的支持。例如，如果一个 pthread 在一个系统调用中阻塞，同一进程的另一个 pthread 应该能够在该 CPU 上运行。作为另一个例子，如果一个 pthread 改变了它的进程的地址空间（例如，映射或取消映射内存），内核必须安排运行同一进程线程的其他 CPU 更新它们的硬件页表以反映地址空间的变化。

可以在没有原子指令的情况下实现锁~\cite{lamport:bakery}，但这很昂贵，而且大多数操作系统都使用原子指令。

如果许多 CPU 试图同时获取同一个锁，锁的开销可能会很大。如果一个 CPU 在其本地缓存中缓存了一个锁，而另一个 CPU 必须获取该锁，那么更新持有该锁的缓存行的原子指令必须将该行从一个 CPU 的缓存移动到另一个 CPU 的缓存，并可能使该缓存行的任何其他副本失效。从另一个 CPU 的缓存中获取一个缓存行可能比从本地缓存中获取一个行昂贵几个数量级。

为了避免与锁相关的开销，许多操作系统使用无锁数据结构和算法~\cite{herlihy:art,mckenney:rcuusage}。例如，可以实现一个像本章开头那样的链表，在列表搜索期间不需要锁，并且用一个原子指令在列表中插入一个项。然而，无锁编程比使用锁编程更复杂；例如，必须担心指令和内存重排序。使用锁编程已经很困难了，所以 xv6 避免了无锁编程的额外复杂性。

%%
\section{练习}
%%

\begin{enumerate}

\item 在 \lstinline{kalloc} \lineref{kernel/kalloc.c:/^kalloc/} 中注释掉对 \lstinline{acquire} 和 \lstinline{release} 的调用。这似乎应该会给调用 \lstinline{kalloc} 的内核代码带来问题；你期望看到什么症状？当你运行 xv6 时，你看到了这些症状吗？运行 \lstinline{usertests} 时呢？如果你没有看到问题，为什么？看看你是否可以通过在 \lstinline{kalloc} 的临界区中插入虚拟循环来引发问题。

\item 假设你转而在 \lstinline{kfree} 中注释掉了锁（在恢复 \lstinline{kalloc} 中的锁之后）。现在可能会出什么问题？\lstinline{kfree} 中缺少锁是否比 \lstinline{kalloc} 中危害小？

\item 如果两个 CPU 同时调用 \lstinline{kalloc}，其中一个将不得不等待另一个，这对性能不利。修改 \lstinline{kalloc.c} 以具有更多的并行性，以便来自不同 CPU 的对 \lstinline{kalloc} 的同时调用可以继续进行而无需等待对方。

\item 使用 POSIX 线程编写一个并行程序，大多数操作系统都支持它。例如，实现一个并行哈希表并测量 puts/gets 的数量是否随着 CPU 数量的增加而扩展。

\item 在 xv6 中实现 Pthreads 的一个子集。也就是说，实现一个用户级线程库，以便一个用户进程可以有多个线程，并安排这些线程可以在不同的 CPU 上并行运行。提出一个设计，正确处理一个线程进行阻塞系统调用和改变其共享地址空间的情况。

\end{enumerate}

% LocalWords:  CPUs uniprocessor interruptible allocator kalloc kfree
% LocalWords:  struct malloc sizeof listlock invariants spinlock lk
% LocalWords:  amoswap cpu bcache ftable itable inode vdisk DMA kmem
% LocalWords:  pid proc's tickslock inode's ip buf's proc SCHED sys
% LocalWords:  consoleintr wakeup clockintr intr acquiresleep POSIX
% LocalWords:  Pthreads pthread unmaps TLB IPIs