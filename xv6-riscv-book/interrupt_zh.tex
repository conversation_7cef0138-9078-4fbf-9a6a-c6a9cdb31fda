%    关于 panic 的旁注：
% 	panic 是内核的最后手段：发生了不可能发生的事情，
% 	内核不知道如何继续。在 xv6 中，panic 会做...
\chapter{中断和设备驱动程序}
\label{CH:INTERRUPT}

\indextext{驱动程序}(driver)是操作系统中管理特定设备的代码：它配置设备硬件，告诉设备执行操作，处理由此产生的中断，并与可能正在等待设备 I/O 的进程进行交互。驱动程序代码可能很棘手，因为驱动程序与其管理的设备同时执行。此外，驱动程序必须了解设备的硬件接口，而这些接口可能很复杂且文档记录不完善。

需要操作系统关注的设备通常可以配置为产生中断，中断是陷阱的一种类型。内核陷阱处理代码识别设备何时引发中断，并调用驱动程序的中断处理程序；在 xv6 中，此分派发生在 {\tt devintr} \lineref{kernel/trap.c:/^devintr/} 中。

许多设备驱动程序在两个上下文中执行代码：一个在进程的内核线程中运行的\indextext{上半部分}(top half)，以及一个在中断时执行的\indextext{下半部分}(bottom half)。上半部分通过诸如 {\tt read} 和 {\tt write} 等希望设备执行 I/O 的系统调用来调用。此代码可能会要求硬件启动一个操作（例如，要求磁盘读取一个块）；然后代码等待操作完成。最终设备完成操作并引发中断。驱动程序的中断处理程序作为下半部分，确定已完成的操作，在适当时唤醒等待的进程，并告诉硬件开始处理任何等待的下一个操作。

\section{代码：控制台输入}

控制台驱动程序 \fileref{kernel/console.c} 是驱动程序结构的简单示例。控制台驱动程序通过连接到 RISC-V 的 \indextext{UART} 串行端口硬件接受由人键入的字符。控制台驱动程序一次累积一行输入，处理诸如退格和 control-u 之类的特殊输入字符。用户进程（例如 shell）使用 {\tt read} 系统调用从控制台获取输入行。当您在 QEMU 中向 xv6 输入时，您的击键通过 QEMU 的模拟 UART 硬件传递给 xv6。

驱动程序与之通信的 UART 硬件是 QEMU 模拟的 16550 芯片~\cite{ns16550a}。在真实的计算机上，16550 将管理连接到终端或其他计算机的 RS232 串行链路。在运行 QEMU 时，它连接到您的键盘和显示器。

UART 硬件以一组\indextext{内存映射}(memory-mapped)控制寄存器的形式向软件显示。也就是说，有一些物理地址，RISC-V 硬件将其连接到 UART 设备，因此加载和存储与设备硬件而不是 RAM 交互。UART 的内存映射地址从 0x10000000 开始，即 {\tt UART0} \lineref{kernel/memlayout.h:/UART0.0x/}。有少数几个 UART 控制寄存器，每个寄存器的宽度为一个字节。它们相对于 {\tt UART0} 的偏移量在 \lineref{kernel/uart.c:/define.RHR/} 中定义。例如，{\tt LSR} 寄存器包含指示是否有输入字符等待软件读取的位。这些字符（如果有）可以从 {\tt RHR} 寄存器中读取。每读取一个字符，UART 硬件就会将其从内部的等待字符 FIFO 中删除，并在 FIFO 为空时清除 {\tt LSR} 中的“就绪”位。UART 发送硬件在很大程度上独立于接收硬件；如果软件向 {\tt THR} 写入一个字节，UART 就会发送该字节。

Xv6 的 {\tt main} 调用 {\tt consoleinit} \lineref{kernel/console.c:/^consoleinit/} 来初始化 UART 硬件。此代码配置 UART，以便在 UART 每接收一个输入字节时产生一个接收中断，并在 UART 每次完成发送一个输出字节时产生一个\indextext{发送完成}(transmit complete)中断 \lineref{kernel/uart.c:/^uartinit/}。

xv6 shell 通过由 {\tt init.c} \lineref{user/init.c:/open..console/} 打开的文件描述符从控制台读取。对 {\tt read} 系统调用的调用会通过内核到达 {\tt consoleread} \lineref{kernel/console.c:/^consoleread/}。{\tt consoleread} 等待输入到达（通过中断）并被缓冲在 {\tt cons.buf} 中，将输入复制到用户空间，并且（在整行到达后）返回到用户进程。如果用户还没有输入完整的一行，任何读取进程都将在 {\tt sleep} 调用 \lineref{kernel/console.c:/sleep..cons/} 中等待（第~\ref{CH:SCHED}章解释了 {\tt sleep} 的细节）。

当用户键入一个字符时，UART 硬件请求 RISC-V 引发一个中断，从而激活 xv6 的陷阱处理程序。陷阱处理程序调用 {\tt devintr} \lineref{kernel/trap.c:/^devintr/}，它查看 RISC-V 的 {\tt scause} 寄存器以发现中断来自外部设备。然后它请求一个名为 PLIC \cite{riscv:priv} 的硬件单元告诉它哪个设备中断了 \lineref{kernel/trap.c:/plic.claim/}。如果是 UART，{\tt devintr} 会调用 {\tt uartintr}。

{\tt uartintr} \lineref{kernel/uart.c:/^uartintr/} 从 UART 硬件读取任何等待的输入字符，并将它们交给 {\tt consoleintr} \lineref{kernel/console.c:/^consoleintr/}；它不等待字符，因为未来的输入将引发新的中断。{\tt consoleintr} 的工作是在 {\tt cons.buf} 中累积输入字符，直到一整行到达。{\tt consoleintr} 特殊处理退格键和一些其他字符。当换行符到达时，{\tt consoleintr} 会唤醒一个等待的 {\tt consoleread}（如果有的话）。

一旦被唤醒，{\tt consoleread} 将在 {\tt cons.buf} 中观察到完整的一行，将其复制到用户空间，并通过系统调用机制返回到用户空间。

\section{代码：控制台输出}

对连接到控制台的文件描述符的 {\tt write} 系统调用最终会到达 {\tt uartputc} \lineref{kernel/uart.c:/^uartputc/}。设备驱动程序维护一个输出缓冲区（{\tt uart\_tx\_buf}），以便写入进程不必等待 UART 完成发送；相反，{\tt uartputc} 将每个字符附加到缓冲区，调用 {\tt uartstart} 来启动设备传输（如果尚未启动），然后返回。{\tt uartputc} 等待的唯一情况是缓冲区已满。

每当 UART 完成发送一个字节时，它就会产生一个中断。{\tt uartintr} 调用 {\tt uartstart}，后者检查设备是否真的完成了发送，并将下一个缓冲的输出字符交给设备。因此，如果一个进程向控制台写入多个字节，通常第一个字节将由 {\tt uartputc} 对 {\tt uartstart} 的调用发送，而剩余的缓冲字节将在发送完成中断到达时由来自 {\tt uartintr} 的 {\tt uartstart} 调用发送。

需要注意的一个通用模式是通过缓冲和中断将设备活动与进程活动解耦。控制台驱动程序即使在没有进程等待读取输入时也可以处理输入；后续的读取将看到该输入。类似地，进程可以发送输出而不必等待设备。这种解耦可以通过允许进程与设备 I/O 并发执行来提高性能，并且在设备缓慢（如 UART）或需要立即关注（如回显键入的字符）时尤其重要。这个想法有时被称为\indextext{I/O 并发性}(I/O concurrency)。

\section{驱动程序中的并发}

您可能已经注意到在 {\tt consoleread} 和 {\tt consoleintr} 中有对 {\tt acquire} 的调用。这些调用获取一个锁，以保护控制台驱动程序的数据结构免受并发访问。这里有三个并发危险：两个不同 CPU 上的进程可能同时调用 {\tt consoleread}；硬件可能会请求一个 CPU 在该 CPU 已经在 {\tt consoleread} 内部执行时传递一个控制台（实际上是 UART）中断；以及硬件可能会在一个不同的 CPU 上传递一个控制台中断，而 {\tt consoleread} 正在执行。第~\ref{CH:LOCK}章解释了如何使用锁来确保这些危险不会导致不正确的结果。

并发在驱动程序中需要小心的另一种方式是，一个进程可能正在等待来自设备的输入，但表示输入到达的中断可能在另一个进程（或根本没有进程）正在运行时到达。因此，中断处理程序不允许考虑它们已中断的进程或代码。例如，中断处理程序不能安全地使用当前进程的页表调用 {\tt copyout}。中断处理程序通常做相对较少的工作（例如，只是将输入数据复制到缓冲区），并唤醒上半部分代码来完成其余的工作。

\section{定时器中断}

Xv6 使用定时器中断来维护其当前时间的概念，并在计算密集型进程之间进行切换。定时器中断来自连接到每个 RISC-V CPU 的时钟硬件。Xv6 对每个 CPU 的时钟硬件进行编程，以使其定期中断 CPU。

{\tt start.c} \lineref{kernel/start.c:/^timerinit/} 中的代码设置了一些控制位，允许在 supervisor 模式下访问定时器控制寄存器，然后请求第一个定时器中断。{\tt time} 控制寄存器包含一个硬件以稳定速率递增的计数；这作为当前时间的概念。{\tt stimecmp} 寄存器包含 CPU 将引发定时器中断的时间；将 {\tt stimecmp} 设置为 {\tt time} 的当前值加上 {\it x} 将在未来 {\it x} 个时间单位后安排一个中断。对于 {\tt qemu} 的 RISC-V 仿真，1000000 个时间单位大约是十分之一秒。

定时器中断像其他设备中断一样，通过 {\tt usertrap} 或 {\tt kerneltrap} 以及 {\tt devintr} 到达。定时器中断到达时，{\tt scause} 的低位设置为 5；{\tt trap.c} 中的 {\tt devintr} 检测到这种情况并调用 {\tt clockintr} \lineref{kernel/trap.c:/clockintr/}。后一个函数递增 {\tt ticks}，允许内核跟踪时间的流逝。递增只在一个 CPU 上发生，以避免在有多个 CPU 时时间过得更快。{\tt clockintr} 唤醒任何在 {\tt sleep} 系统调用中等待的进程，并通过写入 {\tt stimecmp} 来安排下一个定时器中断。

{\tt devintr} 对定时器中断返回 2，以向 {\tt kerneltrap} 或 {\tt usertrap} 指示它们应该调用 {\tt yield}，以便 CPU 可以在可运行的进程之间进行多路复用。

内核代码可能被定时器中断中断，该中断通过 {\tt yield} 强制进行上下文切换，这是 {\tt usertrap} 中的早期代码在启用中断之前小心保存诸如 {\tt sepc} 之类的状态的部分原因。这些上下文切换也意味着内核代码的编写必须意识到它可能会在没有警告的情况下从一个 CPU 移动到另一个 CPU。

\section{现实世界}

与许多操作系统一样，Xv6 允许在内核中执行时发生中断甚至上下文切换（通过 {\tt yield}）。这样做的原因是在运行时间较长的复杂系统调用期间保持快速的响应时间。然而，如上所述，允许在内核中中断是一些复杂性的来源；因此，一些操作系统只允许在执行用户代码时中断。

要完全支持典型计算机上的所有设备是一项繁重的工作，因为设备众多，设备功能繁多，而且设备和驱动程序之间的协议可能复杂且文档记录不佳。在许多操作系统中，驱动程序占用的代码比核心内核还多。

UART 驱动程序通过读取 UART 控制寄存器一次一个字节地检索数据；这种模式称为\indextext{编程 I/O}(programmed I/O)，因为软件正在驱动数据移动。编程 I/O 很简单，但速度太慢，无法用于高数据速率。需要高速移动大量数据的设备通常使用\indextext{直接内存访问 (DMA)}(direct memory access)。DMA 设备硬件直接将传入数据写入 RAM，并从 RAM 读取传出数据。现代磁盘和网络设备使用 DMA。DMA 设备的驱动程序会在 RAM 中准备数据，然后使用对控制寄存器的单次写入来告诉设备处理准备好的数据。

当设备在不可预测的时间需要关注，并且频率不高时，中断是有意义的。但中断的 CPU 开销很高。因此，高速设备，如网络和磁盘控制器，使用技巧来减少对中断的需求。一种技巧是为一批传入或传出的请求引发单个中断。另一种技巧是让驱动程序完全禁用中断，并定期检查设备以查看是否需要关注。这种技术称为\indextext{轮询}(polling)。如果设备以高速率执行操作，轮询是有意义的，但如果设备大部分时间处于空闲状态，则会浪费 CPU 时间。一些驱动程序根据当前的设备负载动态地在轮询和中断之间切换。

UART 驱动程序首先将传入数据复制到内核中的缓冲区，然后再复制到用户空间。这在低数据速率下是有意义的，但对于非常快速地生成或消耗数据的设备，这种双重复制会显著降低性能。一些操作系统能够直接在用户空间缓冲区和设备硬件之间移动数据，通常使用 DMA。

如第~\ref{CH:UNIX}章所述，控制台对应用程序显示为常规文件，应用程序使用 \lstinline{read} 和 \lstinline{write} 系统调用来读取输入和写入输出。应用程序可能希望控制无法通过标准文件系统调用表达的设备方面（例如，在控制台驱动程序中启用/禁用行缓冲）。Unix 操作系统为此类情况支持 \lstinline{ioctl} 系统调用。

计算机的某些用法要求系统必须在有限的时间内做出响应。例如，在安全关键系统中，错过最后期限可能导致灾难。Xv6 不适用于硬实时设置。用于硬实时的操作系统往往是与应用程序链接的库，以便能够进行分析以确定最坏情况的响应时间。Xv6 也不适用于软实时应用程序，在这种应用程序中，偶尔错过最后期限是可以接受的，因为 xv6 的调度程序过于简单，并且其内核代码路径中中断被长时间禁用。

\section{练习}

\begin{enumerate}

\item 修改 {\tt uart.c} 以完全不使用中断。您可能还需要修改 {\tt console.c}。

\item 为以太网卡添加一个驱动程序。

\end{enumerate}