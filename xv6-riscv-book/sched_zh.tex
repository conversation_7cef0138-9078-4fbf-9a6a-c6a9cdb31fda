% cox and mullender, semaphores.
% 
% process has one thread with two stacks
%  
% pike et al, sleep and wakeup
\chapter{调度}
\label{CH:SCHED}

任何操作系统都可能需要运行比计算机CPU数量更多的进程，因此需要一个计划来在进程之间分时共享CPU。理想情况下，这种共享对用户进程是透明的。一种常见的方法是，通过在硬件CPU上\indextext{多路复用}（multiplexing）进程，为每个进程提供其拥有虚拟CPU的假象。本章将解释xv6如何实现这种多路复用。
%% 
\section{多路复用}
%% 

Xv6通过在两种情况下将每个CPU从一个进程切换到另一个进程来实现多路复用。首先，当一个进程进行阻塞（必须等待一个事件）的系统调用时，xv6的\lstinline{sleep}和\lstinline{wakeup}机制会进行切换，这通常发生在\lstinline{read}、\lstinline{wait}或\lstinline{sleep}中。其次，xv6会周期性地强制切换，以处理那些长时间计算而不阻塞的进程。前者是自愿切换；后者被称为非自愿切换。这种多路复用创造了每个进程都拥有自己CPU的假象。

实现多路复用带来了一些挑战。首先，如何从一个进程切换到另一个进程？基本思想是保存和恢复CPU寄存器，但C语言无法直接表达这一点，使其变得棘手。其次，如何以对用户进程透明的方式强制切换？Xv6使用了标准技术，即硬件定时器中断驱动上下文切换。第三，所有的CPU都在同一组进程之间切换，因此需要一个锁方案来避免竞争。第四，当一个进程退出时，必须释放其内存和其他资源，但它无法自己完成所有这些工作，因为（例如）它不能在使用自己的内核栈时释放它。第五，多核机器的每个CPU都必须记住它正在执行哪个进程，以便系统调用影响正确进程的内核状态。最后，\lstinline{sleep}和\lstinline{wakeup}允许一个进程放弃CPU并等待被另一个进程或中断唤醒。需要小心避免导致唤醒通知丢失的竞争。

%% 
\section{代码：上下文切换}
%% 

\begin{figure}[t]
\center
\includegraphics[scale=0.5]{fig/switch.pdf}
\caption{从一个用户进程切换到另一个用户进程。在此示例中，xv6使用一个CPU（因此只有一个调度器线程）运行。}
\label{fig:switch}
\end{figure}

图~\ref{fig:switch}概述了从一个用户进程切换到另一个用户进程所涉及的步骤：从用户空间到旧进程内核线程的陷阱（系统调用或中断），上下文切换到当前CPU的调度器线程，上下文切换到新进程的内核线程，以及陷阱返回到用户级进程。Xv6有独立的线程（保存的寄存器和栈）来执行调度器，因为让调度器在任何进程的内核栈上执行都是不安全的：其他CPU可能会唤醒该进程并运行它，在两个不同的CPU上使用同一个栈将是一场灾难。每个CPU都有一个独立的调度器线程，以应对多个CPU正在运行希望放弃CPU的进程的情况。
%
% I think it may never be safe. if the old process is a ZOMBIE, the
% parent may mark it UNUSED and another core may allocate it in
% fork(). if the old process is SLEEPING or RUNNABLE, another core may
% wake it up and run it.
%
% what has changed is that, if we deleted wait(), it would 
% be safe for an exit()ing process to set its state to UNUSED
% directly (no ZOMBIE, no need for parent cleanup).
%
在本节中，我们将研究内核线程和调度器线程之间切换的机制。

从一个线程切换到另一个线程涉及保存旧线程的CPU寄存器，并恢复新线程先前保存的寄存器；栈指针和程序计数器被保存和恢复的事实意味着CPU将切换栈和正在执行的代码。

函数\indexcode{swtch}为内核线程切换保存和恢复寄存器。\lstinline{swtch}不直接了解线程；它只是保存和恢复一组RISC-V寄存器，称为\indextext{上下文}（contexts）。当一个进程需要放弃CPU时，该进程的内核线程调用\lstinline{swtch}来保存自己的上下文并恢复调度器的上下文。每个上下文都包含在一个\lstinline{struct context}\lineref{kernel/proc.h:/^struct.context/}中，它本身包含在一个进程的\lstinline{struct proc}或一个CPU的\lstinline{struct cpu}中。\lstinline{swtch}接受两个参数：\indexcode{struct context}\lstinline{*old}和\lstinline{struct context}\lstinline{*new}。它将当前寄存器保存在\lstinline{old}中，从\lstinline{new}加载寄存器，然后返回。

让我们跟随一个进程通过\lstinline{swtch}进入调度器。我们在第~\ref{CH:TRAP}章看到，中断结束时的一种可能性是\indexcode{usertrap}调用\indexcode{yield}。\lstinline{yield}转而调用\indexcode{sched}，后者调用\indexcode{swtch}将当前上下文保存在\lstinline{p->context}中，并切换到先前保存在\indexcode{cpu->context}\lineref{kernel/proc.c:/swtch..p/}中的调度器上下文。

\lstinline{swtch}\lineref{kernel/swtch.S:/swtch/}只保存被调用者保存的寄存器；C编译器在调用者中生成代码以在栈上保存调用者保存的寄存器。\lstinline{swtch}知道\lstinline{struct context}中每个寄存器字段的偏移量。它不保存程序计数器。相反，\lstinline{swtch}保存\lstinline{ra}寄存器，该寄存器保存了调用\lstinline{swtch}的返回地址。现在\lstinline{swtch}从新上下文中恢复寄存器，新上下文保存了先前\lstinline{swtch}保存的寄存器值。当\lstinline{swtch}返回时，它返回到恢复的\lstinline{ra}寄存器所指向的指令，即新线程先前调用\lstinline{swtch}的指令。此外，它在新线程的栈上返回，因为恢复的\lstinline{sp}指向那里。

在我们的例子中，\indexcode{sched}调用\indexcode{swtch}切换到\indexcode{cpu->context}，即每个CPU的调度器上下文。该上下文是在过去某个时刻\lstinline{scheduler}调用\lstinline{swtch}\lineref{kernel/proc.c:/swtch.&c/}切换到当前放弃CPU的进程时保存的。当我们一直在跟踪的\indexcode{swtch}返回时，它不会返回到\lstinline{sched}，而是返回到\indexcode{scheduler}，栈指针位于当前CPU的调度器栈中。
%% 
\section{代码：调度}
%% 

上一节我们研究了\indexcode{swtch}的底层细节；现在让我们将\lstinline{swtch}视为一个给定的功能，并研究从一个进程的内核线程通过调度器切换到另一个进程的过程。调度器以每个CPU一个特殊线程的形式存在，每个线程运行\lstinline{scheduler}函数。该函数负责选择下一个要运行的进程。希望放弃CPU的进程必须获取自己的进程锁\indexcode{p->lock}，释放它持有的任何其他锁，更新自己的状态（\lstinline{p->state}），然后调用\indexcode{sched}。你可以在\lstinline{yield}\lineref{kernel/proc.c:/^yield/}、\texttt{sleep}和\texttt{exit}中看到这个序列。\lstinline{sched}会仔细检查其中一些要求\linerefs{kernel/proc.c:/if..holding/,/running/}，然后检查一个推论：由于持有一个锁，中断应该被禁用。最后，\indexcode{sched}调用\indexcode{swtch}将当前上下文保存在\lstinline{p->context}中，并切换到\indexcode{cpu->context}中的调度器上下文。\lstinline{swtch}在调度器的栈上返回，就好像\indexcode{scheduler}的\lstinline{swtch}返回了一样\lineref{kernel/proc.c:/swtch.*contex.*contex/}。调度器继续其\lstinline{for}循环，找到一个要运行的进程，切换到它，然后循环重复。

我们刚刚看到xv6在调用\lstinline{swtch}期间持有\indexcode{p->lock}：\indexcode{swtch}的调用者必须已经持有该锁，并且锁的控制权传递给被切换到的代码。这种安排不寻常：更常见的是获取锁的线程也释放它。Xv6的上下文切换必须打破这个惯例，因为\indexcode{p->lock}保护进程的\lstinline{state}和\lstinline{context}字段的不变性，这些不变性在\lstinline{swtch}中执行时是不成立的。例如，如果在\indexcode{swtch}期间没有持有\lstinline{p->lock}，另一个CPU可能会在\indexcode{yield}将其状态设置为\lstinline{RUNNABLE}之后，但在\lstinline{swtch}使其停止使用自己的内核栈之前，决定运行该进程。结果将是两个CPU在同一个栈上运行，这会引起混乱。一旦\lstinline{yield}开始修改一个正在运行的进程的状态以使其变为\lstinline{RUNNABLE}，\lstinline{p->lock}必须保持持有直到不变性恢复：最早的正确释放点是在\lstinline{scheduler}（在其自己的栈上运行）清除\lstinline{c->proc}之后。类似地，一旦\lstinline{scheduler}开始将一个\lstinline{RUNNABLE}的进程转换为\lstinline{RUNNING}，锁就不能被释放，直到进程的内核线程完全运行（在\lstinline{swtch}之后，例如在\lstinline{yield}中）。

内核线程放弃其CPU的唯一地方是在\lstinline{sched}中，并且它总是切换到\lstinline{scheduler}中的相同位置，而\lstinline{scheduler}（几乎）总是切换到某个先前调用了\lstinline{sched}的内核线程。因此，如果有人打印出xv6切换线程的行号，他会观察到以下简单的模式：\lineref{kernel/proc.c:/swtch..c/}，\lineref{kernel/proc.c:/swtch..p/}，\lineref{kernel/proc.c:/swtch..c/}，\lineref{kernel/proc.c:/swtch..p/}，等等。通过线程切换有意地将控制权相互转移的过程有时被称为\indextext{协程}（coroutines）；在这个例子中，\indexcode{sched}和\indexcode{scheduler}是彼此的协程。

有一种情况是调度器的\indexcode{swtch}调用不会在\indexcode{sched}中结束。\lstinline{allocproc}将新进程的上下文\lstinline{ra}寄存器设置为\indexcode{forkret}\lineref{kernel/proc.c:/^forkret/}，以便其第一次\lstinline{swtch}“返回”到该函数的开头。\lstinline{forkret}的存在是为了释放\indexcode{p->lock}；否则，由于新进程需要像从\lstinline{fork}返回一样返回到用户空间，它可以在\lstinline{usertrapret}处开始。

\lstinline{scheduler}\lineref{kernel/proc.c:/^scheduler/}运行一个循环：找到一个要运行的进程，运行它直到它让出，重复。调度器遍历进程表寻找一个可运行的进程，即\lstinline{p->state} \lstinline{==} \lstinline{RUNNABLE}的进程。一旦找到一个进程，它就会设置每个CPU的当前进程变量\lstinline{c->proc}，将进程标记为\lstinline{RUNNING}，然后调用\indexcode{swtch}开始运行它\linerefs{kernel/proc.c:/Switch.to/,/swtch/}。


%% 
\section{代码: mycpu 和 myproc}
%% 

Xv6经常需要一个指向当前进程的\lstinline{proc}结构的指针。在单处理器上，可以有一个全局变量指向当前的\lstinline{proc}。这在多核机器上行不通，因为每个CPU执行不同的进程。解决这个问题的方法是利用每个CPU都有自己的一套寄存器这一事实。

当一个给定的CPU在内核中执行时，xv6确保CPU的\lstinline{tp}寄存器始终保存CPU的hartid。RISC-V对其CPU进行编号，给每个CPU一个唯一的\indextext{hartid}。\indexcode{mycpu}\lineref{kernel/proc.c:/^mycpu/}使用\lstinline{tp}来索引一个\lstinline{cpu}结构数组，并返回当前CPU的结构。一个\indexcode{struct cpu}\lineref{kernel/proc.h:/^struct.cpu/}持有一个指向当前在该CPU上运行的进程（如果有的话）的\lstinline{proc}结构的指针，为CPU的调度器线程保存的寄存器，以及管理中断禁用所需的嵌套自旋锁计数。

确保CPU的\lstinline{tp}持有CPU的hartid有点复杂，因为用户代码可以自由修改\lstinline{tp}。\lstinline{start}在CPU启动序列的早期，仍在机器模式下时设置\lstinline{tp}寄存器\lineref{kernel/start.c:/w_tp/}。\lstinline{usertrapret}在蹦床页中保存\lstinline{tp}，以防用户代码修改它。最后，\lstinline{uservec}在从用户空间进入内核时恢复保存的\lstinline{tp}\lineref{kernel/trampoline.S:/make tp hold/}。编译器保证在内核代码中永远不会修改\lstinline{tp}。如果xv6可以在需要时向RISC-V硬件请求当前的hartid会更方便，但RISC-V只允许在机器模式下这样做，而不是在监督者模式下。

\lstinline{cpuid}和\lstinline{mycpu}的返回值是脆弱的：如果定时器中断并导致线程让出并在稍后在不同的CPU上恢复执行，先前返回的值将不再正确。为了避免这个问题，xv6要求调用者禁用中断，并且只有在他们完成使用返回的\lstinline{struct cpu}后才启用它们。

函数\indexcode{myproc}\lineref{kernel/proc.c:/^myproc/}返回当前CPU上运行的进程的\lstinline{struct proc}指针。\lstinline{myproc}禁用中断，调用\lstinline{mycpu}，从\lstinline{struct cpu}中获取当前进程指针（\lstinline{c->proc}），然后启用中断。\lstinline{myproc}的返回值即使在启用中断的情况下也是安全的：如果定时器中断将调用进程移动到不同的CPU，它的\lstinline{struct proc}指针将保持不变。
%% 
\section{休眠与唤醒}
\label{sec:sleep}
%% 

调度和锁有助于隐藏一个线程对另一个线程的操作，但我们还需要有助于线程有意交互的抽象。例如，xv6中管道的读取者可能需要等待一个写入进程产生数据；父进程对\lstinline{wait}的调用可能需要等待一个子进程退出；以及一个读取磁盘的进程需要等待磁盘硬件完成读取。xv6内核在这些情况（以及许多其他情况）下使用一种称为休眠和唤醒的机制。休眠允许一个内核线程等待一个特定事件；另一个线程可以调用唤醒来指示等待指定事件的线程应该恢复。休眠和唤醒通常被称为\indextext{序列协调}（sequence coordination）或\indextext{条件同步}（conditional synchronization）机制。

休眠和唤醒提供了一个相对底层的同步接口。为了激发它们在xv6中的工作方式，我们将用它们来构建一个更高级别的同步机制，称为\indextext{信号量}（semaphore）~\cite{dijkstra65}，用于协调生产者和消费者（xv6不使用信号量）。信号量维护一个计数并提供两个操作。“V”操作（对于生产者）增加计数。“P”操作（对于消费者）等到计数非零，然后递减它并返回。如果只有一个生产者线程和一个消费者线程，并且它们在不同的CPU上执行，并且编译器没有进行过于激进的优化，那么这个实现将是正确的：
\begin{lstlisting}[numbers=left,firstnumber=100]
  struct semaphore {
    struct spinlock lock;
    int count;
  };

  void
  V(struct semaphore *s)
  {
     acquire(&s->lock);
     s->count += 1;
     release(&s->lock);
  }

  void
  P(struct semaphore *s)
  {
     while(s->count == 0)
       ;
     acquire(&s->lock);
     s->count -= 1;
     release(&s->lock);
  }
\end{lstlisting}

上面的实现是昂贵的。如果生产者很少活动，消费者将花费大部分时间在\lstinline{while}循环中旋转，希望计数非零。消费者的CPU可以通过重复\indextext{轮询}（polling）\lstinline{s->count}来找到比\indextext{忙等待}（busy waiting）更有效率的工作。避免忙等待需要一种让消费者让出CPU并仅在\lstinline{V}增加计数后才恢复的方法。

这是一个朝着这个方向迈出的一步，虽然正如我们将看到的，这还不够。让我们想象一对调用，\indexcode{sleep}和\indexcode{wakeup}，它们的工作方式如下。\lstinline{sleep(chan)}等待由\indexcode{chan}的值指定的事件，称为\indextext{等待通道}（wait channel）。\lstinline{sleep}将调用进程置于休眠状态，释放CPU用于其他工作。\lstinline{wakeup(chan)}唤醒所有正在调用具有相同\lstinline{chan}的\lstinline{sleep}的进程（如果有的话），导致它们的\lstinline{sleep}调用返回。如果没有进程在\lstinline{chan}上等待，\lstinline{wakeup}什么也不做。我们可以改变信号量实现以使用\lstinline{sleep}和\lstinline{wakeup}（更改以黄色突出显示）：
\begin{lstlisting}[numbers=left,firstnumber=200]
  void
  V(struct semaphore *s)
  {
     acquire(&s->lock);
     s->count += 1;
     (*@\hl{wakeup(s);}@*)
     release(&s->lock);
  }
  
  void
  P(struct semaphore *s)
  {
    while(s->count == 0)    (*@\label{line:test}@*)
      (*@\hl{sleep(s);}@*)  (*@\label{line:sleep}@*)
    acquire(&s->lock);
    s->count -= 1;
    release(&s->lock);
  }
\end{lstlisting}

% \begin{figure}[t]
% \center
% \includegraphics[scale=0.5]{fig/deadlock.pdf}
% \caption{Example lost wakeup problem}
% \label{fig:deadlock}
% \end{figure}

\lstinline{P}现在放弃CPU而不是自旋，这很好。然而，事实证明，使用这种接口设计\lstinline{sleep}和\lstinline{wakeup}而不遭受所谓的\indextext{丢失唤醒}（lost wake-up）问题并不简单。
% (see 
% Figure~\ref{fig:deadlock}).
假设\lstinline{P}在第~\ref{line:test}行发现\lstinline{s->count} \lstinline{==} \lstinline{0}。当\lstinline{P}在第~\ref{line:test}行和第~\ref{line:sleep}行之间时，\lstinline{V}在另一个CPU上运行：它将\lstinline{s->count}更改为非零并调用\lstinline{wakeup}，它发现没有进程在休眠，因此什么也不做。现在\lstinline{P}在第~\ref{line:sleep}行继续执行：它调用\lstinline{sleep}并进入休眠状态。这导致一个问题：\lstinline{P}正在休眠等待一个已经发生过的\lstinline{V}调用。除非我们幸运地生产者再次调用\lstinline{V}，否则即使计数非零，消费者也将永远等待。

这个问题的根源在于，\lstinline{P}仅在\lstinline{s->count} \lstinline{==} \lstinline{0}时休眠的不变性被\lstinline{V}在恰好错误的时刻运行所破坏。一个不正确的保护不变性的方法是在\lstinline{P}中移动锁的获取（下面以黄色突出显示），以便其对计数的检查和对\lstinline{sleep}的调用是原子的：
\begin{lstlisting}[numbers=left,firstnumber=300]
  void
  V(struct semaphore *s)
  {
    acquire(&s->lock);
    s->count += 1;
    wakeup(s);
    release(&s->lock);
  }
  
  void
  P(struct semaphore *s)
  {
    (*@\hl{acquire(\&s->lock);}@*)
    while(s->count == 0)    (*@\label{line:test1}@*)
      sleep(s);             (*@\label{line:sleep1}@*)
    s->count -= 1;
    release(&s->lock);
  }
\end{lstlisting}
人们可能希望这个版本的\lstinline{P}会避免丢失唤醒，因为锁阻止了\lstinline{V}在第~\ref{line:test1}行和第~\ref{line:sleep1}行之间执行。它确实做到了这一点，但它也导致了死锁：\lstinline{P}在休眠时持有锁，所以\lstinline{V}将永远阻塞等待锁。

我们将通过改变\lstinline{sleep}的接口来修复前面的方案：调用者必须将\indextext{条件锁}（condition lock）传递给\lstinline{sleep}，以便它可以在调用进程被标记为休眠并等待在休眠通道上之后释放锁。该锁将强制一个并发的\lstinline{V}等到\lstinline{P}完成将自己置于休眠状态，以便\lstinline{wakeup}将找到休眠的消费者并唤醒它。一旦消费者再次被唤醒，\indexcode{sleep}会在返回之前重新获取锁。我们新的正确的休眠/唤醒方案可按如下方式使用（更改以黄色突出显示）：
\begin{lstlisting}[numbers=left,firstnumber=400]
  void
  V(struct semaphore *s)
  {
    acquire(&s->lock);
    s->count += 1;
    wakeup(s);
    release(&s->lock);
  }

  void
  P(struct semaphore *s)
  {
    acquire(&s->lock);
    while(s->count == 0)
       (*@\hl{sleep(s, \&s->lock);}@*)
    s->count -= 1;
    release(&s->lock);
  }
\end{lstlisting}

\lstinline{P}持有\lstinline{s->lock}的事实阻止了\lstinline{V}在\lstinline{P}检查\lstinline{s->count}和其调用\lstinline{sleep}之间尝试唤醒它。然而，\lstinline{sleep}必须释放\lstinline{s->lock}并将消费进程置于休眠状态，从\lstinline{wakeup}的角度来看，这必须是原子的，以避免丢失唤醒。

%% 
\section{代码: 休眠与唤醒}
%% 

Xv6的\indexcode{sleep}\lineref{kernel/proc.c:/^sleep/}和\indexcode{wakeup}\lineref{kernel/proc.c:/^wakeup/}提供了上一个例子中使用的接口。基本思想是让\lstinline{sleep}将当前进程标记为\indexcode{SLEEPING}，然后调用\indexcode{sched}以释放CPU；\lstinline{wakeup}寻找在给定等待通道上休眠的进程，并将其标记为\indexcode{RUNNABLE}。\lstinline{sleep}和\lstinline{wakeup}的调用者可以使用任何相互方便的数字作为通道。Xv6通常使用与等待相关的内核数据结构的地址。

\lstinline{sleep}获取\indexcode{p->lock}\lineref{kernel/proc.c:/DOC: sleeplock1/}，然后*才*释放\lstinline{lk}。正如我们将看到的，\lstinline{sleep}在任何时候都持有这两个锁中的一个或另一个，这阻止了并发的\lstinline{wakeup}（它必须获取并持有这两个锁）采取行动。现在\lstinline{sleep}只持有\lstinline{p->lock}，它可以通过记录休眠通道、将进程状态更改为\texttt{SLEEPING}并调用\lstinline{sched}\linerefs{kernel/proc.c:/chan.=.chan/,/sched/}来使进程休眠。稍后就会清楚，为什么在进程被标记为\texttt{SLEEPING}之后，\lstinline{p->lock}不被（由\lstinline{scheduler}）释放是至关重要的。

在某个时刻，一个进程将获取条件锁，设置休眠者正在等待的条件，并调用\lstinline{wakeup(chan)}。重要的是\lstinline{wakeup}在持有条件锁的同时被调用\footnote{%
%
严格来说，如果\lstinline{wakeup}只是跟在\lstinline{acquire}之后就足够了（也就是说，可以在\lstinline{release}之后调用\lstinline{wakeup}）。%
%
}。\lstinline{wakeup}遍历进程表\lineref{kernel/proc.c:/^wakeup\(/}。它获取它检查的每个进程的\lstinline{p->lock}。当\lstinline{wakeup}发现一个处于\indexcode{SLEEPING}状态且具有匹配\indexcode{chan}的进程时，它将该进程的状态更改为\indexcode{RUNNABLE}。下一次\lstinline{scheduler}运行时，它将看到该进程已准备好运行。

为什么\lstinline{sleep}和\lstinline{wakeup}的锁定规则能确保一个即将进入休眠的进程不会错过一个并发的唤醒？即将进入休眠的进程从*检查条件之前*到*被标记为\texttt{SLEEPING}之后*，都持有条件锁或它自己的\lstinline{p->lock}，或两者兼有。调用\texttt{wakeup}的进程在\texttt{wakeup}的循环中持有*两个*锁。因此，唤醒者要么在消费线程检查条件之前使条件为真；要么唤醒者的\lstinline{wakeup}严格地在休眠线程被标记为\texttt{SLEEPING}之后检查它。然后\lstinline{wakeup}将看到休眠的进程并唤醒它（除非有其他东西先唤醒它）。

有时多个进程在同一个通道上休眠；例如，多个进程从一个管道读取。一个对\lstinline{wakeup}的调用将唤醒所有这些进程。其中一个将首先运行并获取\lstinline{sleep}被调用时所持有的锁，并（在管道的情况下）读取任何等待的数据。其他进程会发现，尽管被唤醒，但没有数据可读。从它们的角度来看，唤醒是“虚假的”，它们必须再次休眠。因此，\lstinline{sleep}总是在一个检查条件的循环内调用。

如果两个sleep/wakeup的使用意外地选择了同一个通道，也不会造成伤害：它们会看到虚假的唤醒，但如上所述的循环将容忍这个问题。sleep/wakeup的魅力很大程度上在于它既是轻量级的（不需要创建特殊的数据结构来充当休眠通道），又提供了一层间接性（调用者不需要知道它们正在与哪个特定的进程交互）。
%% 
\section{代码: 管道}
%% 
一个更复杂的例子是xv6的管道实现，它使用\lstinline{sleep}和\lstinline{wakeup}来同步生产者和消费者。我们在第~\ref{CH:UNIX}章中看到了管道的接口：写入管道一端的字节被复制到内核缓冲区，然后可以从管道的另一端读取。未来的章节将探讨围绕管道的文件描述符支持，但现在让我们看看\indexcode{pipewrite}和\indexcode{piperead}的实现。

每个管道由一个\indexcode{struct pipe}表示，其中包含一个\lstinline{lock}和一个\lstinline{data}缓冲区。字段\lstinline{nread}和\lstinline{nwrite}计算从缓冲区读取和写入的总字节数。缓冲区是环形的：在\lstinline{buf[PIPESIZE-1]}之后写入的下一个字节是\lstinline{buf[0]}。计数器不会回绕。这个约定让实现能够区分一个满的缓冲区（\lstinline{nwrite} \lstinline{==} \lstinline{nread+PIPESIZE}）和一个空的缓冲区（\lstinline{nwrite} \lstinline{==} \lstinline{nread}），但这意味着索引缓冲区必须使用\lstinline{buf[nread} \lstinline{%} \lstinline{PIPESIZE]}而不是仅仅\lstinline{buf[nread]}（对于\lstinline{nwrite}也是如此）。

假设\lstinline{piperead}和\lstinline{pipewrite}的调用同时在两个不同的CPU上发生。\lstinline{pipewrite}\lineref{kernel/pipe.c:/^pipewrite/}开始时获取管道的锁，该锁保护计数、数据及其相关的不变性。\lstinline{piperead}\lineref{kernel/pipe.c:/^piperead/}然后也尝试获取锁，但无法获取。它在\lstinline{acquire}\lineref{kernel/spinlock.c:/^acquire/}中自旋等待锁。当\lstinline{piperead}等待时，\lstinline{pipewrite}循环遍历正在写入的字节（\lstinline{addr[0..n-1]}），依次将每个字节添加到管道中\lineref{kernel/pipe.c:/nwrite\+\+/}. 在这个循环中，可能会发生缓冲区已满的情况\lineref{kernel/pipe.c:/DOC: pipewrite-full/}。在这种情况下，\lstinline{pipewrite}调用\lstinline{wakeup}来提醒任何休眠的读取者缓冲区中有数据在等待，然后在\lstinline{&pi->nwrite}上休眠，等待读取者从缓冲区中取出一些字节。\lstinline{sleep}在使\lstinline{pipewrite}的进程进入休眠状态时释放管道的锁。

现在\lstinline{piperead}获取了管道的锁并进入其临界区：它发现\lstinline{pi->nread} \lstinline{!=} \lstinline{pi->nwrite}\lineref{kernel/pipe.c:/DOC: pipe-empty/}（\lstinline{pipewrite}进入休眠是因为\lstinline{pi->nwrite} \lstinline{==} \lstinline{pi->nread} \lstinline{+} \lstinline{PIPESIZE}\lineref{kernel/pipe.c:/pipewrite-full/}），所以它进入\lstinline{for}循环，从管道中复制数据\lineref{kernel/pipe.c:/DOC: piperead-copy/}，并将在复制的字节数上增加\lstinline{nread}。现在有这么多字节可供写入，所以\lstinline{piperead}在返回之前调用\lstinline{wakeup}\lineref{kernel/pipe.c:/DOC: piperead-wakeup/}来唤醒任何休眠的写入者。\lstinline{wakeup}找到一个在\lstinline{&pi->nwrite}上休眠的进程，即之前运行\lstinline{pipewrite}但在缓冲区满时停止的进程。它将该进程标记为\indexcode{RUNNABLE}。

管道代码为读者和写者使用不同的休眠通道（\lstinline{pi->nread}和\lstinline{pi->nwrite}）；在有大量读者和写者等待同一个管道的不太可能的情况下，这可能会使系统更有效率。管道代码在一个检查休眠条件的循环内休眠；如果有多个读者或写者，除了第一个被唤醒的进程外，所有进程都会看到条件仍然为假并再次休眠。
%% 
\section{代码: Wait, exit, 和 kill}
%% 
\lstinline{sleep}和\lstinline{wakeup}可以用于多种等待。一个有趣的例子，在第~\ref{CH:UNIX}章中介绍过，是子进程的\indexcode{exit}和其父进程的\indexcode{wait}之间的交互。在子进程死亡时，父进程可能已经在{\tt wait}中休眠，或者可能在做其他事情；在后一种情况下，后续对{\tt wait}的调用必须观察到子进程的死亡，可能是在它调用{\tt exit}很久之后。xv6记录子进程死亡直到{\tt wait}观察到它的方式是让{\tt exit}将调用者置于\indexcode{ZOMBIE}状态，它一直保持在该状态，直到父进程的{\tt wait}注意到它，将子进程的状态更改为{\tt UNUSED}，复制子进程的退出状态，并将子进程的进程ID返回给父进程。如果父进程在子进程之前退出，父进程会将子进程交给\lstinline{init}进程，该进程会永久调用{\tt wait}；因此每个子进程都有一个父进程来为其清理。一个挑战是避免同时发生的父子进程的\lstinline{wait}和\lstinline{exit}以及同时发生的\lstinline{exit}和\lstinline{exit}之间的竞争和死锁。

\lstinline{wait}开始时获取\lstinline{wait_lock}\lineref{kernel/proc.c:/^wait/}，它充当条件锁，帮助确保\lstinline{wait}不会错过来自退出子进程的\lstinline{wakeup}。然后\lstinline{wait}扫描进程表。如果它找到一个处于\texttt{ZOMBIE}状态的子进程，它会释放该子进程的资源及其\lstinline{proc}结构，将子进程的退出状态复制到提供给\lstinline{wait}的地址（如果不是0），并返回子进程的进程ID。如果\lstinline{wait}找到子进程但没有一个退出，它会调用\lstinline{sleep}来等待它们中的任何一个退出\lineref{kernel/proc.c:/DOC: wait-sleep/}，然后再次扫描。\lstinline{wait}通常持有两个锁，\lstinline{wait_lock}和某个进程的\lstinline{pp->lock}；避免死锁的顺序是先\lstinline{wait_lock}然后是\lstinline{pp->lock}。

\lstinline{exit} \lineref{kernel/proc.c:/^exit/} 记录退出状态，释放一些资源，调用\lstinline{reparent}将其子进程交给\lstinline{init}进程，唤醒父进程以防它在\lstinline{wait}中，将调用者标记为僵尸，并永久让出CPU。\lstinline{exit}在此序列中同时持有\lstinline{wait_lock}和\lstinline{p->lock}。\lstinline{exit}持有\lstinline{wait_lock}是因为它是\lstinline{wakeup(p->parent)}的条件锁，防止在\lstinline{wait}中的父进程丢失唤醒。\lstinline{exit}也必须持有\lstinline{p->lock}，以防止在\lstinline{wait}中的父进程在子进程最终调用\lstinline{swtch}之前看到子进程处于\lstinline{ZOMBIE}状态。\lstinline{exit}以与\lstinline{wait}相同的顺序获取这些锁以避免死锁。

\lstinline{exit}在将其状态设置为\lstinline{ZOMBIE}之前唤醒父进程可能看起来不正确，但这是安全的：虽然\lstinline{wakeup}可能会导致父进程运行，但\lstinline{wait}中的循环在子进程的\lstinline{p->lock}被{\tt scheduler}释放之前无法检查子进程，所以\lstinline{wait}在\lstinline{exit}将其状态设置为\lstinline{ZOMBIE}\lineref{kernel/proc.c:/state.=.ZOMBIE/}很久之后才能看到退出的进程。

虽然\lstinline{exit}允许一个进程自己终止，但\lstinline{kill}\lineref{kernel/proc.c:/^kill/}允许一个进程请求另一个进程终止。让\lstinline{kill}直接销毁受害者进程会过于复杂，因为受害者可能正在另一个CPU上执行，可能正在对内核数据结构进行敏感的更新序列。因此\lstinline{kill}做得很少：它只是设置受害者的\indexcode{p->killed}，如果它正在休眠，就唤醒它。最终受害者将进入或离开内核，此时\lstinline{usertrap}中的代码如果\lstinline{p->killed}被设置就会调用\lstinline{exit}（它通过调用\lstinline{killed}\lineref{kernel/proc.c:/^killed/}来检查）。如果受害者在用户空间运行，它很快就会通过进行系统调用或因为定时器（或其他设备）中断而进入内核。

如果受害者进程在\lstinline{sleep}中，\lstinline{kill}对\lstinline{wakeup}的调用将导致受害者从\lstinline{sleep}返回。这可能有潜在的危险，因为正在等待的条件可能不为真。然而，xv6对\lstinline{sleep}的调用总是被包装在一个\lstinline{while}循环中，该循环在\lstinline{sleep}返回后重新测试条件。一些对\lstinline{sleep}的调用也在循环中测试\lstinline{p->killed}，并在设置时放弃当前活动。这只有在放弃是正确的情况下才会这样做。例如，管道读写代码\lineref{kernel/pipe.c:/killed.pr/}在killed标志被设置时返回；最终代码将返回到trap，trap将再次检查\lstinline{p->killed}并退出。

一些xv6的\lstinline{sleep}循环不检查\lstinline{p->killed}，因为代码正处于一个应该原子化的多步系统调用的中间。virtio驱动程序\lineref{kernel/virtio\_disk.c:/sleep.b/}是一个例子：它不检查\lstinline{p->killed}，因为一个磁盘操作可能是一组写操作中的一个，而所有这些写操作都是为了让文件系统处于一个正确的状态所必需的。一个在等待磁盘I/O时被杀死的进程在完成当前系统调用并且\lstinline{usertrap}看到killed标志之前不会退出。

\section{进程锁定}

与每个进程关联的锁（\lstinline{p->lock}）是xv6中最复杂的锁。一种简单的思考\lstinline{p->lock}的方式是，在读取或写入以下任何\lstinline{struct proc}字段时必须持有它：\lstinline{p->state}、\lstinline{p->chan}、\lstinline{p->killed}、\lstinline{p->xstate}和\lstinline{p->pid}。这些字段可以被其他进程或其他CPU上的调度器线程使用，所以它们必须由一个锁来保护是很自然的。

然而，\lstinline{p->lock}的大多数用途是保护xv6进程数据结构和算法的更高级别的方面。以下是\lstinline{p->lock}所做的全部事情：

% it's hard to know how to phrase these things in a uniform way.
% should the discussion be about invariants? protecting data? avoiding
% races? ensuring atomicity? avoiding a specific danger?

% the "atomic" phrasing often doesn't really say what the danger is.

\begin{itemize}

\item 与\lstinline{p->state}一起，它防止了为新进程分配\lstinline{proc[]}槽位时的竞争。
% makes check for UNUSED atomic with allocation (or something).

\item 它在进程创建或销毁时将其隐藏起来。
% makes all the steps of allocation, and destruction, atomic.

\item 它防止父进程的\lstinline{wait}收集一个已经将其状态设置为\lstinline{ZOMBIE}但尚未让出CPU的进程。
% it atomicizes the setting of the state to ZOMIE and the yielding
% of the CPU.

\item 它防止另一个CPU的调度器在让出进程将其状态设置为\lstinline{RUNNABLE}之后但在完成\lstinline{swtch}之前决定运行它。
% it atomizes the setting of p->state and swtch().

\item 它确保只有一个CPU的调度器决定运行一个\lstinline{RUNNABLE}的进程。
% it atomicizes a scheduler's check for RUNNABLE and actually
% running the process. or setting state to RUNNING.

\item 它防止定时器中断导致进程在\lstinline{swtch}中让出。
% it makes swtch (or really the whole sequence) atomic w.r.t. timer interrupts

\item 与条件锁一起，它有助于防止\lstinline{wakeup}忽略一个正在调用\lstinline{sleep}但尚未完成让出CPU的进程。
% avoids a race between conditionCheck+sleep and wakeup?
% makes condition check atomic with yield?

\item 它防止\lstinline{kill}的受害者进程在\lstinline{kill}检查\lstinline{p->pid}和设置\lstinline{p->killed}之间退出并可能被重新分配。
% it makes the pid check atomic with setting killed.

\item 它使\lstinline{kill}对\lstinline{p->state}的检查和写入成为原子操作。

\end{itemize}


\lstinline{p->parent}字段由全局锁\lstinline{wait_lock}而不是\lstinline{p->lock}保护。只有一个进程的父进程会修改\lstinline{p->parent}，尽管该字段由进程本身和其他正在寻找其子进程的进程读取。\lstinline{wait_lock}的目的是当\lstinline{wait}休眠等待任何子进程退出时充当条件锁。一个正在退出的子进程持有\lstinline{wait_lock}或\lstinline{p->lock}直到它将其状态设置为\lstinline{ZOMBIE}，唤醒其父进程，并让出CPU。\lstinline{wait_lock}还序列化了父进程和子进程并发的\lstinline{exit}，以便\lstinline{init}进程（继承了子进程）保证从其\lstinline{wait}中被唤醒。\lstinline{wait_lock}是一个全局锁而不是每个父进程的锁，因为，在一个进程获取它之前，它无法知道它的父进程是谁。

%% 
\section{现实世界}
%% 

xv6调度器实现了一个简单的调度策略，即轮流运行每个进程。这个策略被称为\indextext{轮询调度}（round robin）。真正的操作系统实现了更复杂的策略，例如，允许进程有优先级。其思想是，一个可运行的高优先级进程将被调度器优先于一个可运行的低优先级进程。这些策略可能很快变得复杂，因为通常存在相互竞争的目标：例如，操作系统可能还希望保证公平性和高吞吐量。此外，复杂的策略可能导致意想不到的交互，例如\indextext{优先级反转}（priority inversion）和\indextext{护航}（convoys）。当一个低优先级和一个高优先级的进程都使用一个特定的锁时，可能会发生优先级反转，当低优先级进程获取该锁时，可能会阻止高优先级进程取得进展。当许多高优先级进程等待一个获取了共享锁的低优先级进程时，可能会形成一个长的等待进程护航；一旦护航形成，它可能会持续很长时间。为了避免这些问题，在复杂的调度器中需要额外的机制。

\lstinline{sleep}和\lstinline{wakeup}是一个简单而有效的同步方法，但还有许多其他方法。所有这些方法的第一个挑战是避免我们在本章开头看到的“丢失唤醒”问题。最初的Unix内核的\lstinline{sleep}只是禁用中断，这在Unix在单CPU系统上运行时就足够了。因为xv6在多处理器上运行，它向\lstinline{sleep}添加了一个显式锁。FreeBSD的\lstinline{msleep}采取了同样的方法。Plan 9的\lstinline{sleep}使用一个回调函数，该函数在进入休眠前持有调度锁运行；该函数作为对休眠条件的最后检查，以避免丢失唤醒。Linux内核的\lstinline{sleep}使用一个显式的进程队列，称为等待队列，而不是一个等待通道；该队列有自己的内部锁。

在\lstinline{wakeup}中扫描整个进程集是低效的。一个更好的解决方案是在\lstinline{sleep}和\lstinline{wakeup}中用一个保存了在该结构上休眠的进程列表的数据结构替换\lstinline{chan}，例如Linux的等待队列。Plan 9的\lstinline{sleep}和\lstinline{wakeup}称该结构为会合点。许多线程库将相同的结构称为条件变量；在这种情况下，操作\lstinline{sleep}和\lstinline{wakeup}被称为\lstinline{wait}和\lstinline{signal}。所有这些机制都有相同的特点：休眠条件由某种在休眠期间原子地释放的锁保护。

\lstinline{wakeup}的实现唤醒了所有在特定通道上等待的进程，并且可能有很多进程在等待该特定通道。操作系统将调度所有这些进程，它们将竞争检查休眠条件。以这种方式行为的进程有时被称为\indextext{惊群效应}（thundering herd），最好避免。大多数条件变量有两个用于\lstinline{wakeup}的原语：\lstinline{signal}，唤醒一个进程，和\lstinline{broadcast}，唤醒所有等待的进程。

信号量通常用于同步。计数通常对应于管道缓冲区中可用的字节数或进程拥有的僵尸子进程数。使用显式计数作为抽象的一部分可以避免“丢失唤醒”问题：有一个关于已发生唤醒次数的显式计数。该计数还避免了虚假唤醒和惊群效应问题。

终止进程并清理它们在xv6中引入了许多复杂性。在大多数操作系统中，它甚至更复杂，因为，例如，受害者进程可能在内核深处休眠，展开其栈需要小心，因为调用栈上的每个函数可能需要进行一些清理工作。一些语言通过提供异常机制来提供帮助，但C语言没有。此外，还有其他事件可能导致休眠的进程被唤醒，即使它正在等待的事件尚未发生。例如，当一个Unix进程正在休眠时，另一个进程可能会向它发送一个\indexcode{signal}。在这种情况下，进程将从被中断的系统调用返回，返回值为-1，错误代码设置为EINTR。应用程序可以检查这些值并决定做什么。Xv6不支持信号，因此不会出现这种复杂性。

Xv6对\lstinline{kill}的支持不完全令人满意：有一些休眠循环可能应该检查\lstinline{p->killed}。一个相关的问题是，即使对于检查\lstinline{p->killed}的\lstinline{sleep}循环，\lstinline{sleep}和\lstinline{kill}之间也存在竞争；后者可能会在受害者的休眠循环检查\lstinline{p->killed}之后但在其调用\lstinline{sleep}之前设置\lstinline{p->killed}并试图唤醒受害者。如果发生此问题，受害者在它等待的条件发生之前不会注意到\lstinline{p->killed}。这可能会晚很多，甚至永远不会（例如，如果受害者正在等待来自控制台的输入，但用户不输入任何内容）。

一个真正的操作系统会用一个显式的空闲列表在常数时间内找到空闲的\lstinline{proc}结构，而不是像\lstinline{allocproc}那样进行线性时间搜索；xv6为了简单起见使用了线性扫描。
%% 
\section{练习}
%% 

\begin{enumerate}

\item 在xv6中实现信号量，不使用\lstinline{sleep}和\lstinline{wakeup}（但可以使用自旋锁）。选择xv6中几个使用sleep和wakeup的地方，并用信号量替换它们。评价结果。

\item 修复上面提到的\lstinline{kill}和\lstinline{sleep}之间的竞争，以便在受害者的休眠循环检查\lstinline{p->killed}之后但在调用\lstinline{sleep}之前发生的\lstinline{kill}会导致受害者放弃当前的系统调用。
% Answer: a solution is to check in sleep if p->killed is set before setting
% the processes's state to sleep. 

\item 设计一个计划，以便每个休眠循环都检查\lstinline{p->killed}，这样，例如，在virtio驱动程序中的进程如果被另一个进程杀死，可以从while循环中快速返回。
% Answer: this is difficult.  Moderns Unixes do this with setjmp and longjmp and very carefully programming to clean any partial state that the interrupted systems call may have built up.

\item 修改xv6，在从一个进程的内核线程切换到另一个进程时只使用一次上下文切换，而不是通过调度器线程切换。让出的线程需要自己选择下一个线程并调用\texttt{swtch}。挑战将是防止多个CPU意外执行同一个线程；正确处理锁定；以及避免死锁。

\item 修改xv6的\lstinline{scheduler}，在没有可运行进程时使用RISC-V的\lstinline{WFI}（等待中断）指令。尝试确保，在任何时候有可运行的进程等待运行时，没有CPU在\texttt{WFI}中暂停。

\end{enumerate}