@book{riscv,
 author = {<PERSON>, <PERSON> and <PERSON>, <PERSON>},
 title = {The {RISC-V} Reader: an open architecture Atlas},
 year = {2017},
 isbn = {099924910X, 9780999249109},
 publisher = {Strawberry Canyon},
}

@book{lions,
  author = {<PERSON>},
  title = {Commentary on UNIX 6th Edition},
  year = 2000,
  publisher =  {Peer to Peer Communications},
  isbn = {1-57398-013-7},
}

@article{unix,
 author = {<PERSON>, <PERSON> and <PERSON>, <PERSON>},
 title = {The {UNIX} Time-sharing System},
 journal = {Commun. ACM},
 issue_date = {July 1974},
 volume = {17},
 number = {7},
 month = jul,
 year = {1974},
 pages = {365--375},
 numpages = {11},
 url = {http://doi.acm.org/10.1145/361011.361061},
 doi = {10.1145/361011.361061},
 publisher = {ACM},
} 

@book{knuth,
 author = {<PERSON><PERSON><PERSON>, <PERSON>},
 title = {Fundamental Algorithms. The Art of Computer Programming. (Second ed.)},
 year = 1997,
 volume = 1,
 publisher =  <PERSON><PERSON>,
 isbn = {0-201-89683-4},
}

@document{riscv:priv,
  title = {The {RISC-V} instruction set manual {Volume II}: privileged specification},
  editor = {Andrew Waterman and Krste Asanovic and <PERSON> Ha<PERSON>},
  year = 2024,
  howpublished = {\url{https://drive.google.com/file/d/1uviu1nH-tScFfgrovvFCrj7Omv8tFtkp/view?usp=drive_link}},
}

@document{riscv:user,
  title = {The {RISC-V} instruction set manual {Volume I}: unprivileged specification {ISA}},
  editor = {Andrew Waterman and Krste Asanovic},
  year = 2024,
  howpublished={\url{https://drive.google.com/file/d/17GeetSnT5wW3xNuAHI95-SI1gPGd5sJ_/view?usp=drive_link}},
}

@book{kernighan,
 author = {Kernighan, Brian W.},
 editor = {Ritchie, Dennis M.},
 title = {The  C Programming Language},
 year = {1988},
 isbn = {0131103709},
 edition = {2nd},
 publisher = {Prentice Hall Professional Technical Reference},
} 

@document{u54,
   author = {SiFive},
   title = {SiFive {FU540-C000} manual},
   howpublished={\url{https://sifive.cdn.prismic.io/sifive%2F590bbcb6-598e-4ed8-b5d3-88c2c7458ebf_u54-core-complex-manual-v19.05.pdf}},
   year = "2018",
}

@document{virtio,
  author = {{OASIS} Open},
  title = {Virtual {I/O} Device ({VIRTIO}) Version 1.0},
  year = "2016",
  month = "March",
  howpublished={\url{http://docs.oasis-open.org/virtio/virtio/v1.0/virtio-v1.0.html}},
}


@document{dijkstra65,
  author = {Edsger Dijkstra},
  title = {Cooperating Sequential Processes},
  year = "1965",
  howpublished={\url{https://www.cs.utexas.edu/users/EWD/transcriptions/EWD01xx/EWD123.html}},
}
                  
@document{ns16550a,
  author = {Martin Michael and Daniel Durich},
  year = "1987",
  title = {The {NS16550A}: {UART} Design and Application Considerations},
  howpublished = {\url{http://bitsavers.trailing-edge.com/components/national/_appNotes/AN-0491.pdf}},
}

@article{boehm04,
 author = {Boehm, Hans-J},
 title = {Threads cannot be implemented as a library},
 journal = {ACM PLDI Conference},
 year = {2005},
} 


@article{lamport:bakery,
  author = {Lamport, L},
  title = {A New Solution of Dijkstra's Concurrent Programming Problem},
  journal = {Communications of the ACM},
  year = {1974},
}


@MISC{mckenney:rcuusage,
    author = {Paul E. Mckenney and Silas Boyd-wickizer and Jonathan Walpole},
    title = {{RCU} Usage In the Linux Kernel: One Decade Later},
    year = {2013}
}


@book{herlihy:art,
author = {Herlihy, Maurice and Shavit, Nir},
title = {The Art of Multiprocessor Programming, Revised Reprint},
year = {2012},
}

@INPROCEEDINGS{Presotto91plan9,
    author = {Dave Presotto and Rob Pike and Ken Thompson and Howard Trickey},
    title = {Plan 9, A Distributed System},
    booktitle = {In Proceedings of the Spring 1991 EurOpen Conference},
    year = {1991},
    pages = {43--50}
}

@inproceedings{sel4,
author = {Klein, Gerwin and Elphinstone, Kevin and Heiser, Gernot and Andronick, June and Cock, David and Derrin, Philip and Elkaduwe, Dhammika and Engelhardt, Kai and Kolanski, Rafal and Norrish, Michael and Sewell, Thomas and Tuch, Harvey and Winwood, Simon},
title = {SeL4: Formal Verification of an {OS} Kernel},
year = {2009},
booktitle = {Proceedings of the ACM SIGOPS 22nd Symposium on Operating Systems Principles},
pages = {207–220},
}

@MISC{aleph:smashing,
  author="Aleph One",
  title={Smashing The Stack For Fun And Profit},
  howpublished={\url{http://phrack.org/issues/49/14.html#article}},
}

@MISC{mitre:cves,
  title = {Linux Common Vulnerabilities and Exposures ({CVEs})},
  howpublished={\url{https://cve.mitre.org/cgi-bin/cvekey.cgi?keyword=linux}},
}
