\chapter{文件系统}
\label{CH:FS}
%         是进程还是内核线程？
%
%         日志记录文本（以及一些缓冲区文本）假设读者
%         对 inode 和目录的工作方式有相当的了解，
%         但这些内容将在后面介绍。
%
% 	必须决定使用 processor 还是 CPU。
% 	
% 	确保说的是 buffer，而不是 block 
% 
% 	挂载

文件系统的目的是组织和存储数据。文件系统
通常支持用户和应用程序之间的数据共享，以及
\indextext{持久性}，
以便数据在重启后仍然可用。

xv6 文件系统提供类 Unix 的文件、目录和路径名
（参见第 \ref{CH:UNIX} 章），并将其数据存储在 virtio 磁盘上以实现
持久性。该文件系统解决了
几个挑战：
\begin{itemize}

\item 文件系统需要磁盘上的数据结构来表示
命名的目录和文件树，记录每个文件内容所在的
块的标识，并记录磁盘的哪些区域
是空闲的。

\item 文件系统必须支持
\indextext{崩溃恢复}。
也就是说，如果发生崩溃（例如，电源故障），文件系统必须
在重启后仍能正常工作。风险在于崩溃可能会
中断一系列更新，并留下不一致的磁盘
数据结构（例如，一个块既在文件中使用又被标记为空闲）。

\item 不同的进程可能同时操作文件系统，
因此文件系统代码必须协调以维护不变量。

\item 访问磁盘比访问
内存慢几个数量级，因此文件系统必须维护一个内存中的
常用块缓存。

\end{itemize}

本章的其余部分将解释 xv6 如何应对这些挑战。
%% 
%%  -------------------------------------------
%% 
\section{概述}

xv6 文件系统的实现
分为七层，如图 \ref{fig:fslayer} 所示。
磁盘层在 virtio 硬盘上读取和写入块。
缓冲区缓存层缓存磁盘块并同步对它们的访问，
确保一次只有一个内核进程可以修改
存储在任何特定块中的数据。日志层允许更高
层将对多个块的更新包装在
\indextext{事务}中，
并确保在发生崩溃时，这些块以原子方式更新
（即，要么全部更新，要么都不更新）。
inode 层提供单个文件，每个文件表示为一个
\indextext{inode}，
具有唯一的 i-number
和一些保存文件数据的块。目录
层将每个目录实现为一种特殊的
inode，其内容是一系列目录条目，每个条目包含
文件名和 i-number。
路径名层提供
像
\lstinline{/usr/rtm/xv6/fs.c} 这样的分层路径名，
并通过递归查找来解析它们。
文件描述符层使用文件系统接口抽象了许多 Unix 资源（例如，管道、设备、
文件等），简化了
应用程序员的生活。

\begin{figure}[t]
\center
\includegraphics[scale=0.5]{fig/fslayer.pdf}
\caption{xv6 文件系统的层次结构。}
\label{fig:fslayer}
\end{figure}

传统上，磁盘硬件将磁盘上的数据
呈现为一系列编号的 512 字节
\textit{块}
\index{块}
（也称为
\textit{扇区}）：
\index{扇区}
扇区 0 是前 512 字节，扇区 1 是下一个，依此类推。操作系统
为其文件系统使用的块大小可能与
磁盘使用的扇区大小不同，但通常块大小是
扇区大小的倍数。Xv6 将其已读入内存的块的副本
保存在
\lstinline{struct buf}
\lineref{kernel/buf.h:/^struct.buf/} 类型的对象中。
此结构中存储的数据有时与磁盘不同步：它可能
尚未从磁盘读入（磁盘正在处理但尚未返回
扇区的内容），或者它可能已被软件更新
但尚未写入磁盘。

文件系统必须有一个计划，用于在磁盘上存储 inode 和
内容块。
为此，xv6 将磁盘划分为几个
部分，如图 \ref{fig:fslayout} 所示。
文件系统不使用
块 0（它包含引导扇区）。块 1 被称为
\indextext{超级块}；
它包含有关文件系统的元数据（文件系统大小（以块为单位）、
数据块数、inode 数以及
日志中的块数）。从 2 开始的块保存日志。日志之后是 inode，每个块有多个 inode。
之后是位图块，跟踪哪些数据块正在使用。
其余块是数据块；每个块要么在位图块中标记为
空闲，要么保存文件或目录的内容。
超级块由一个名为
\indexcode{mkfs} 的独立程序填充，
该程序构建初始文件系统。

本章的其余部分将讨论每一层，从
缓冲区缓存开始。
请注意那些在较低层精心选择的抽象
简化了较高层设计的情况。
%% 
%%  -------------------------------------------
%% 
\section{缓冲区缓存层}
\label{s:bcache}

缓冲区缓存有两个工作：（1）同步对磁盘块的访问，以确保
内存中只有一个块的副本，并且一次只有一个内核线程
使用该副本；（2）缓存常用块，这样就不需要从
慢速磁盘重新读取它们。代码在
\lstinline{bio.c} 中。

缓冲区缓存导出的主要接口包括
\indexcode{bread}
和
\indexcode{bwrite}；
前者获取一个
\indextext{buf}，
其中包含一个可以在内存中读取或修改的块的副本，而
后者将修改后的缓冲区写入磁盘上的相应块。
内核线程在完成缓冲区操作后必须通过调用
\indexcode{brelse}
来释放它。
缓冲区缓存使用每个缓冲区的休眠锁来确保
一次只有一个线程使用每个缓冲区
（从而使用每个磁盘块）；
\lstinline{bread}
返回一个锁定的缓冲区，而
\lstinline{brelse}
释放该锁。

让我们回到缓冲区缓存。
缓冲区缓存具有固定数量的缓冲区来保存磁盘块，
这意味着如果文件系统请求的块
不在缓存中，缓冲区缓存必须回收当前
保存其他块的缓冲区。缓冲区缓存回收
最近最少使用的缓冲区用于新块。假设是
最近最少使用的缓冲区是最近最可能
再次使用的。

\begin{figure}[t]
\center
\includegraphics[scale=0.5]{fig/fslayout.pdf}
\caption{xv6 文件系统的结构。}
\label{fig:fslayout}
\end{figure}
%% 
%%  -------------------------------------------
%% 
\section{代码：缓冲区缓存}

缓冲区缓存是一个双向链表的缓冲区。
函数
\indexcode{binit}，
由
\indexcode{main}
\lineref{kernel/main.c:/binit/} 调用，
用静态数组
\lstinline{buf}
\linerefs{kernel/bio.c:/Create.linked.list/,/^..}/} 中的
\indexcode{NBUF}
个缓冲区初始化列表。
对缓冲区缓存的所有其他访问都通过
\indexcode{bcache.head}
引用链表，而不是
\lstinline{buf}
数组。

一个缓冲区有两个与之关联的状态字段。
字段
\indexcode{valid}
表示缓冲区包含该块的副本。
字段 \indexcode{disk}
表示缓冲区内容已交给
磁盘，磁盘可能会更改缓冲区（例如，将
数据从磁盘写入 \lstinline{data}）。

\lstinline{bread}
\lineref{kernel/bio.c:/^bread/}
调用
\indexcode{bget}
来获取给定扇区的缓冲区
\lineref{kernel/bio.c:/b.=.bget/}。
如果需要从磁盘读取缓冲区，
\lstinline{bread}
调用
\indexcode{virtio_disk_rw}
来执行此操作，然后返回缓冲区。

\lstinline{bget}
\lineref{kernel/bio.c:/^bget/}
扫描缓冲区列表，查找具有给定设备和扇区号的缓冲区
\linerefs{kernel/bio.c:/Is.the.block.already/,/^..}/}。
如果存在这样的缓冲区，
\indexcode{bget}
获取该缓冲区的休眠锁。
\lstinline{bget}
然后返回锁定的缓冲区。

如果没有给定扇区的缓存缓冲区，
\indexcode{bget}
必须创建一个，可能会重用一个保存
不同扇区的缓冲区。
它第二次扫描缓冲区列表，寻找一个未被使用的缓冲区（\lstinline{b->refcnt = 0}）；
任何这样的缓冲区都可以使用。
\lstinline{bget}
编辑缓冲区元数据以记录新的设备和扇区号
并获取其休眠锁。
请注意，赋值
\lstinline{b->valid = 0}
确保
\lstinline{bread}
将从磁盘读取块数据，
而不是错误地使用缓冲区的先前内容。

每个磁盘扇区最多只有一个缓存缓冲区，这一点很重要，
以确保读者看到写入，并且因为
文件系统使用缓冲区上的锁进行同步。
\lstinline{bget}
通过持有
\lstinline{bache.lock}
\lstinline{bcache.lock}
从第一个循环检查块是否被缓存
到第二个循环声明
块现在被缓存（通过设置
\lstinline{dev}、
\lstinline{blockno}
和
\lstinline{refcnt}）来确保此不变量。
这使得检查块是否存在和（如果不存在）
指定一个缓冲区来保存该块
是原子的。

\lstinline{bget}
在
\lstinline{bcache.lock}
临界区之外获取缓冲区的休眠锁是安全的，
因为非零的
\lstinline{b->refcnt}
阻止了缓冲区被重用于不同的磁盘块。
休眠锁保护对块的缓冲内容的读取
和写入，而
\lstinline{bcache.lock}
保护有关哪些块被缓存的信息。

如果所有缓冲区都繁忙，则说明有太多进程
同时执行文件系统调用；
\lstinline{bget}
会 panic。
更优雅的响应可能是休眠直到有缓冲区变为空闲，
尽管那样可能会出现死锁。

一旦
\indexcode{bread}
读取了磁盘（如果需要）并返回
缓冲区给其调用者，调用者就
独占使用该缓冲区，可以读取或写入数据字节。
如果调用者确实修改了缓冲区，它必须在释放缓冲区之前调用
\indexcode{bwrite}
将更改的数据写入磁盘。
\lstinline{bwrite}
\lineref{kernel/bio.c:/^bwrite/}
调用
\indexcode{virtio_disk_rw}
与磁盘硬件通信。

当调用者完成一个缓冲区时，
它必须调用
\indexcode{brelse}
来释放它。
（名称
\lstinline{brelse}，
是
b-release 的缩写，
虽然晦涩但值得学习：
它起源于 Unix，也在 BSD、Linux 和 Solaris 中使用。）
\lstinline{brelse}
\lineref{kernel/bio.c:/^brelse/}
释放休眠锁并将
缓冲区移动
到链表的前端
\linerefs{kernel/bio.c:/b->next->prev.=.b->prev/,/bcache.head.next.=.b/}。
移动缓冲区会导致
列表按缓冲区最近使用的顺序排序（意味着释放）：
列表中的第一个缓冲区是最近使用的，
最后一个是最近最少使用的。
\lstinline{bget}
中的两个循环利用了这一点：
扫描现有缓冲区最坏情况下需要处理整个列表，
但首先检查最近使用的缓冲区
（从
\lstinline{bcache.head}
开始并跟随
\lstinline{next}
指针）将在有良好引用局部性时减少扫描时间。
选择重用缓冲区的扫描通过向后扫描
（跟随
\lstinline{prev}
指针）来选择最近最少使用的缓冲区。
%% 
%%  -------------------------------------------
%% 
\section{日志层}

文件系统设计中最有趣的问题之一是崩溃
恢复。这个问题之所以出现，是因为许多文件系统操作
涉及对磁盘的多次写入，而在部分写入后发生崩溃
可能会使磁盘上的文件系统处于不一致状态。例如，
假设在文件截断期间发生崩溃（将文件
长度设置为零并释放其内容块）。
根据磁盘写入的顺序，崩溃
可能会留下一个引用
已释放内容块的 inode，
或者可能会留下一个已分配但未被引用的内容块。

后者相对良性，但引用已释放块的 inode
在重启后很可能会导致严重问题。重启后，
内核可能会将该块分配给另一个文件，现在我们就有两个不同的
文件无意中指向同一个块。如果 xv6 支持
多用户，这种情况可能是一个安全问题，因为
旧文件的所有者将能够读写
属于不同用户的新文件中的块。

Xv6 通过一种简单的日志记录形式解决了文件系统操作期间崩溃的问题。
xv6 系统调用不直接写入
磁盘上的文件系统数据结构。相反，它将
它希望进行的所有磁盘写入的描述放在磁盘上的一个
\indextext{日志}
中。一旦系统调用记录了其所有写入，它就会向磁盘写入一个
特殊的
\indextext{提交}
记录，指示日志包含
一个完整的操作。此时，系统调用将写入
复制到磁盘上的文件系统数据结构。在这些写入
完成后，系统调用会擦除磁盘上的日志。

如果系统崩溃并重启，文件系统代码在运行任何进程之前
会如下恢复：如果日志
被标记为包含一个完整的操作，那么恢复代码
会将写入复制到它们在磁盘文件系统中的所属位置。如果
日志未被标记为包含一个完整的操作，恢复
代码会忽略该日志。恢复代码最后会擦除
日志。

为什么 xv6 的日志能解决文件系统操作期间崩溃的问题？
如果崩溃发生在操作提交之前，那么
磁盘上的日志将不会被标记为完成，恢复代码将
忽略它，磁盘的状态将如同操作
从未开始一样。如果崩溃发生在操作提交之后，
那么恢复将重放操作的所有写入，如果操作
已开始将它们写入
磁盘上的数据结构，则可能会重复它们。在任何一种情况下，日志都使操作
相对于崩溃是原子的：恢复后，操作的所有写入
要么全部出现在磁盘上，要么全都不出现。
%% 
%% 
%% 
\section{日志设计}

日志位于一个已知的固定位置，在超级块中指定。
它由一个头部块和一系列
更新的块副本（“日志块”）组成。
头部块包含一个扇区
号数组，每个日志块对应一个，以及
日志块的数量。
磁盘上头部块中的计数要么是
零，表示日志中没有事务，
要么是非零，表示日志包含一个完整的已提交
事务，其中包含指定数量的日志块。
Xv6 在事务提交时写入头部
块，但在此之前不写入，并在将日志块复制到文件系统后将
计数设置为零。
因此，在事务中途崩溃将导致
日志头部块中的计数为零；提交后崩溃
将导致非零计数。

每个系统调用的代码都指明了必须相对于崩溃是原子的
写入序列的开始和结束。
为了允许不同进程
并发执行文件系统操作，
日志系统可以将多个系统调用的写入
累积到一个事务中。
因此，单个提交可能涉及多个
完整系统调用的写入。
为了避免跨事务拆分系统调用，日志系统
仅在没有文件系统系统调用正在进行时才提交。

将多个事务一起提交的想法被称为
\indextext{组提交}。
组提交减少了磁盘操作的数量，
因为它将一次提交的固定成本分摊到多个
操作上。
组提交还同时向磁盘系统提交了更多的并发写入，
也许允许磁盘在一次磁盘旋转期间
写入所有这些写入。
Xv6 的 virtio 驱动程序不支持这种
\indextext{批处理}，
但 xv6 的文件系统设计允许这样做。

Xv6 在磁盘上专门分配了固定数量的空间来保存日志。
一个事务中系统调用写入的块总数
必须能容纳在该空间中。
这有两个后果。
任何单个系统调用
都不允许写入比日志空间中
更多的不同块。
这对于大多数系统调用来说不是问题，但其中有两个
可能会写入许多块：
\indexcode{write}
和
\indexcode{unlink}。
一个大的文件写入可能会写入许多数据块和许多位图块
以及一个 inode 块；取消链接一个大文件可能会写入许多
位图块和一个 inode。
Xv6 的写入系统调用将大的写入分解为多个较小的写入，
这些写入适合日志，
而
\lstinline{unlink}
不会引起问题，因为实际上 xv6 文件系统
只使用一个位图块。
有限日志空间的另一个后果
是日志系统不能允许系统调用开始，
除非它确定该系统调用的写入将
适合日志中剩余的空间。
%% 
%% 
%% 
\section{代码：日志记录}

在系统调用中典型使用日志的方式如下：
\begin{lstlisting}[]
  begin_op();
  ...
  bp = bread(...);
  bp->data[...] = ...;
  log_write(bp);
  ...
  end_op();
\end{lstlisting}

\indexcode{begin_op}
\lineref{kernel/log.c:/^begin.op/}
等待直到
日志系统当前没有在提交，并且有
足够的未保留日志空间来容纳
此调用的写入。
\lstinline{log.outstanding}
计算已保留日志
空间的系统调用数量；总保留空间是
\lstinline{log.outstanding}
乘以
\lstinline{MAXOPBLOCKS}。
增加
\lstinline{log.outstanding}
既保留了空间，又阻止了在此系统调用期间
发生提交。
代码保守地假设每个系统调用最多可能写入
\lstinline{MAXOPBLOCKS}
个不同的块。

\indexcode{log_write}
\lineref{kernel/log.c:/^log.write/}
充当
\indexcode{bwrite}
的代理。
它在内存中记录块的扇区号，
在磁盘上的日志中为其保留一个槽位，
并将缓冲区固定在块缓存中
以防止块缓存将其逐出。
块必须在提交之前一直留在缓存中：
在此之前，缓存的副本是
修改的唯一记录；在提交之后才能将其写入
其在磁盘上的位置；
并且同一事务中的其他读取必须
看到修改。
\lstinline{log_write}
注意到一个块在单个事务中被多次写入时，
会为该块在日志中分配相同的槽位。
这种优化通常被称为
\indextext{吸收}。
例如，包含多个文件的 inode 的磁盘块
在一个事务中被多次写入是很常见的。通过将
多个磁盘写入吸收为一个，文件系统可以节省日志空间并
可以实现更好的性能，因为只需要将磁盘块的一个副本
写入磁盘。

\indexcode{end_op}
\lineref{kernel/log.c:/^end.op/}
首先减少未完成的系统调用计数。
如果计数现在为零，它通过调用
\lstinline{commit()}
来提交当前事务。
此过程有四个阶段。
\lstinline{write_log()}
\lineref{kernel/log.c:/^write.log/}
将事务中修改的每个块从缓冲区
缓存复制到其在磁盘上日志中的槽位。
\lstinline{write_head()}
\lineref{kernel/log.c:/^write.head/}
将头部块写入磁盘：这是
提交点，写入后发生崩溃将
导致恢复从日志中重放事务的写入。
\indexcode{install_trans}
\lineref{kernel/log.c:/^install_trans/}
从日志中读取每个块并将其写入文件系统中的
适当位置。
最后
\lstinline{end_op}
用零计数写入日志头部；
这必须在下一个事务开始写入
日志块之前发生，这样崩溃就不会导致恢复
使用一个事务的头部和后续事务的
日志块。

\indexcode{recover_from_log}
\lineref{kernel/log.c:/^recover_from_log/}
从
\indexcode{initlog}
\lineref{kernel/log.c:/^initlog/}
调用，
而后者又从 \indexcode{fsinit}\lineref{kernel/fs.c:/^fsinit/} 在引导期间第一个用户进程运行之前调用
\lineref{kernel/proc.c:/fsinit/}。
它读取日志头部，如果头部指示日志包含已提交的事务，
则模仿
\lstinline{end_op}
的操作。

\indexcode{filewrite}
\lineref{kernel/file.c:/^filewrite/}
中出现了一个使用日志的例子。
事务看起来像这样：
\begin{lstlisting}[]
      begin_op();
      ilock(f->ip);
      r = writei(f->ip, ...);
      iunlock(f->ip);
      end_op();
\end{lstlisting}
此代码被包装在一个循环中，该循环将大的写入分解为
一次只有几个扇区的单个事务，以避免溢出
日志。对
\indexcode{writei}
的调用作为此
事务的一部分写入许多块：文件的 inode、一个或多个位图块以及一些数据
块。
%% 
%% 
%% 
\section{代码：块分配器}

文件和目录内容存储在磁盘块中，
这些块必须从空闲池中分配。
Xv6 的块分配器
在磁盘上维护一个空闲位图，每个块一位。
零位表示相应块是空闲的；
一位表示它正在使用中。
程序
\lstinline{mkfs}
设置与引导扇区、超级块、日志块、inode
块和位图块相对应的位。

块分配器提供两个函数：
\indexcode{balloc}
分配一个新的磁盘块，而
\indexcode{bfree}
释放一个块。
\lstinline{balloc}
\lstinline{balloc}
中的循环在
\lineref{kernel/fs.c:/^..for.b.=.0/}
处考虑每个块，从块 0 到
\lstinline{sb.size}，
即文件系统中的块数。
它寻找一个位图位为零的块，
表示它是空闲的。
如果
\lstinline{balloc}
找到这样的块，它会更新位图
并返回该块。
为提高效率，循环分为两
部分。
外层循环读取每个位图块。
内层循环检查单个位图块中的所有
Bits-Per-Block（\lstinline{BPB}）
位。
如果两个进程试图同时分配
一个块，可能会发生竞争，这种情况由
缓冲区缓存只允许一个进程一次使用任何一个位图块的事实来防止。

\lstinline{bfree}
\lineref{kernel/fs.c:/^bfree/}
找到正确的位图块并清除正确的位。
同样，由
\lstinline{bread}
和
\lstinline{brelse}
隐含的独占使用避免了显式锁定的需要。

与本章其余部分描述的大部分代码一样，
\lstinline{balloc}
和
\lstinline{bfree}
必须在事务内部调用。
%% 
%%  -------------------------------------------
%% 
\section{Inode 层}

术语
\indextext{inode}
可以有两个相关的含义。
它可以指包含文件大小和数据块编号列表的磁盘上数据结构。
或者“inode”可以指内存中的 inode，它包含
磁盘上 inode 的副本以及内核中所需的额外信息。

磁盘上的 inode
被打包在一个称为 inode 块的连续磁盘区域中。
每个 inode 大小相同，因此给定一个
数字 n，很容易在磁盘上找到第 n 个 inode。
实际上，这个数字 n，称为 inode 号或 i-number，
是实现中标识 inode 的方式。

磁盘上的 inode 由一个
\indexcode{struct dinode}
\lineref{kernel/fs.h:/^struct.dinode/} 定义。
\lstinline{type}
字段区分文件、目录和特殊
文件（设备）。
类型为零表示磁盘上的 inode 是空闲的。
\lstinline{nlink}
字段计算引用此 inode 的目录条目数，
以便识别何时应释放磁盘上的
inode 及其数据块。
\lstinline{size}
字段记录文件内容的字节数。
\lstinline{addrs}
数组记录保存文件内容的磁盘块的块号。

内核将活动 inode 集保存在内存中
一个名为 \indexcode{itable} 的表中；
\indexcode{struct inode}
\lineref{kernel/file.h:/^struct.inode/}
是磁盘上
\lstinline{struct}
\lstinline{dinode}
的内存副本。
内核仅在有 C 指针引用该 inode 时才将其存储在内存中。
\lstinline{ref}
字段计算引用内存中
inode 的 C 指针数，如果引用计数降至零，内核会从内存中丢弃该 inode。
\indexcode{iget}
和
\indexcode{iput}
函数获取和释放指向 inode 的指针，
修改引用计数。
指向 inode 的指针可以来自文件描述符、
当前工作目录和临时内核代码，
例如
\lstinline{exec}。

xv6 的 inode 代码中有四种锁或类似锁的机制。
\lstinline{itable.lock}
保护了 inode 在 inode 表中最多出现一次的不变量，以及内存中 inode 的
\lstinline{ref}
字段计算指向该 inode 的内存指针数的不变量。
每个内存中的 inode 都有一个
\lstinline{lock}
字段，其中包含一个
休眠锁，确保对 inode 字段（如文件长度）以及
inode 的文件或目录内容块的独占访问。
inode 的
\lstinline{ref}，
如果大于零，会导致系统在表中维护
该 inode，并且不重用该表条目用于
不同的 inode。
最后，每个 inode 包含一个
\lstinline{nlink}
字段（在磁盘上，如果在内存中则复制在内存中），
计算引用文件的目录条目数；
如果其链接计数大于零，xv6 不会释放 inode。

由
\lstinline{iget()}
返回的
\lstinline{struct}
\lstinline{inode}
指针保证在相应的
\lstinline{iput()}
调用之前是有效的；
inode 不会被删除，并且指针引用的内存
不会被重用于不同的 inode。
\lstinline{iget()}
提供对 inode 的非独占访问，因此
可以有许多指向同一 inode 的指针。
文件系统代码的许多部分都依赖于
\lstinline{iget()}
的这种行为，
既用于持有对 inode 的长期引用（如打开的文件
和当前目录），又用于在避免
操作多个 inode 的代码中死锁的同时防止竞争（如
路径名查找）。

\lstinline{iget}
返回的
\lstinline{struct}
\lstinline{inode}
可能没有任何有用的内容。
为了确保它持有磁盘上 inode 的副本，
代码必须调用
\indexcode{ilock}。
这将锁定 inode（以便没有其他进程可以
\lstinline{ilock}
它）并从磁盘读取 inode，
如果尚未读取的话。
\lstinline{iunlock}
释放 inode 上的锁。
将 inode 指针的获取与锁定分开
在某些情况下有助于避免死锁，例如在
目录查找期间。
多个进程可以持有由
\lstinline{iget}
返回的指向 inode 的 C 指针，
但一次只有一个进程可以锁定该 inode。

inode 表只存储那些内核代码
或数据结构持有 C 指针的 inode。
它的主要工作是同步多个进程的访问。
inode 表也恰好缓存了频繁使用的 inode，但
缓存是次要的；如果一个 inode 被频繁使用，缓冲区缓存很可能
会将其保存在内存中。
修改内存中 inode 的代码会使用
\lstinline{iupdate}
将其写入磁盘。

%% 
%%  -------------------------------------------
%% 
\section{代码：Inode}

要分配一个新的 inode（例如，在创建文件时），
xv6 调用
\indexcode{ialloc}
\lineref{kernel/fs.c:/^ialloc/}。
\lstinline{ialloc}
类似于
\indexcode{balloc}：
它一次一个块地遍历磁盘上的 inode 结构，
寻找一个被标记为空闲的 inode。
当它找到一个时，它通过向磁盘写入新的
\lstinline{type}
来声明它，然后通过对
\indexcode{iget}
\lineref{kernel/fs.c:/return.iget\(dev..inum\)/}
的尾调用返回一个 inode 表条目。
\lstinline{ialloc}
的正确操作
依赖于这样一个事实：一次只有一个进程
可以持有对
\lstinline{bp}
的引用：
\lstinline{ialloc}
可以确保没有其他进程
同时看到该 inode 可用
并试图声明它。

\lstinline{iget}
\lineref{kernel/fs.c:/^iget/}
在 inode 表中查找具有所需设备和 inode 号的活动条目（\lstinline{ip->ref}
\lstinline{>}
\lstinline{0}）。
如果找到一个，它会返回对该 inode 的新引用
\linerefs{kernel/fs.c:/^....if.ip->ref.>.0/,/^....}/}。
当
\indexcode{iget}
扫描时，它记录第一个空槽的位置
\linerefs{kernel/fs.c:/^....if.empty.==.0/,/empty.=.ip/}，
如果需要分配表条目，它会使用该位置。

代码必须在使用
\indexcode{ilock}
锁定 inode 之后才能读取或写入其元数据或内容。
\lstinline{ilock}
\lineref{kernel/fs.c:/^ilock/}
为此使用休眠锁。
一旦
\indexcode{ilock}
对 inode 具有独占访问权，它会根据需要从磁盘（更可能是缓冲区缓存）读取 inode。
函数
\indexcode{iunlock}
\lineref{kernel/fs.c:/^iunlock/}
释放休眠锁，
这可能会唤醒任何正在休眠的进程。

\lstinline{iput}
\lineref{kernel/fs.c:/^iput/}
通过递减引用计数
\lineref{kernel/fs.c:/^..ip->ref--/}
来释放指向 inode 的 C 指针。
如果这是最后一个引用，则 inode
在 inode 表中的槽位现在是空闲的，可以被重用于
不同的 inode。

如果
\indexcode{iput}
看到一个 inode 没有 C 指针引用，
并且该 inode 没有链接到它（即没有出现在任何
目录中），那么该 inode 及其数据块必须
被释放。
\lstinline{iput}
调用
\indexcode{itrunc}
将文件截断为零字节，释放数据块；
将 inode 类型设置为 0（未分配）；
并将 inode 写入磁盘
\lineref{kernel/fs.c:/inode.has.no.links.and/}。

在
\indexcode{iput}
释放 inode 的情况下，其锁定协议值得仔细研究。
一个危险是，一个并发线程可能正在
\lstinline{ilock}
中等待使用此 inode（例如，读取文件或列出目录），
并且没有准备好发现该 inode 不再
被分配。这不会发生，因为如果一个内存中的 inode
没有链接并且
\lstinline{ip->ref}
为 1，则系统调用无法获取指向它的指针。这一个引用是调用
\lstinline{iput}
的线程所拥有的引用。
另一个主要危险是，并发调用
\lstinline{ialloc}
可能会选择
\lstinline{iput}
正在释放的同一个 inode。
这只有在
\lstinline{iupdate}
写入磁盘，使 inode 类型为零之后才会发生。
这种竞争是良性的；分配线程会礼貌地等待
获取 inode 的休眠锁，然后再读取或写入
该 inode，此时
\lstinline{iput}
已经完成了对它的操作。

\lstinline{iput()}
可以写入磁盘。这意味着任何使用文件
系统的系统调用都可能写入磁盘，因为该系统调用可能是最后一个
持有文件引用的系统调用。即使像
\lstinline{read()}
这样看起来是只读的调用，也可能最终调用
\lstinline{iput()}。
这反过来意味着，即使是只读的系统调用，
如果它们使用文件系统，也必须包装在事务中。

\lstinline{iput()}
和崩溃之间存在一个具有挑战性的交互。
当文件的链接计数降至零时，
\lstinline{iput()}
不会立即截断文件，因为某个进程可能仍然持有对该 inode 的
内存引用：一个进程可能仍在读写该文件，因为它
成功地打开了它。但是，如果在最后一个进程关闭
该文件的文件描述符之前发生崩溃，那么该文件将在磁盘上被标记为已分配，
但没有目录条目指向它。

文件系统通过以下两种方式之一来处理这种情况。简单的解决方案是，在
恢复时，重启后，文件系统扫描整个文件系统，查找那些
被标记为已分配但没有目录条目指向它们的文件。如果存在任何
这样的文件，那么它可以释放这些文件。

第二种解决方案不需要扫描文件系统。在这种解决方案中，
文件系统在磁盘上记录（例如，在超级块中）
链接计数降至零但引用计数不为零的文件的 inode 号。如果
文件系统在引用计数达到 0 时删除该文件，那么它会
通过从列表中删除该 inode 来更新磁盘上的列表。在恢复时，
文件系统会释放列表中的任何文件。

Xv6 两种解决方案都没有实现，这意味着 inode 可能会在磁盘上被标记为已分配，
即使它们已不再使用。这意味着随着时间的推移，xv6
可能会耗尽磁盘空间。
%% 
%% 
%% 
\section{代码：Inode 内容}

\begin{figure}[t]
\center
\includegraphics[scale=0.5]{fig/inode.pdf}
\caption{磁盘上文件的表示。}
\label{fig:inode}
\end{figure}

磁盘上的 inode 结构，
\indexcode{struct dinode}，
包含一个大小和一个块号数组（见
图 \ref{fig:inode}）。
inode 数据位于
\lstinline{dinode}
的
\lstinline{addrs}
数组中列出的块中。
前
\indexcode{NDIRECT}
个数据块列在数组的前
\lstinline{NDIRECT}
个条目中；这些块被称为
\indextext{直接块}。
接下来的
\indexcode{NINDIRECT}
个数据块不是列在 inode 中，
而是列在一个称为
\indextext{间接块}
的数据块中。
\lstinline{addrs}
数组中的最后一个条目给出了间接块的地址。
因此，文件的前 12 kB（
\lstinline{NDIRECT}
\lstinline{x}
\indexcode{BSIZE}）
字节可以从 inode 中列出的块加载，
而接下来的
\lstinline{256} kB（
\lstinline{NINDIRECT}
\lstinline{x}
\lstinline{BSIZE}）
字节只有在查询间接块后才能加载。
这是一个很好的磁盘表示，但对客户端来说
很复杂。
函数
\indexcode{bmap}
管理这种表示，以便更高级别的例程，如
\indexcode{readi}
和
\indexcode{writei}，
我们稍后会看到，不需要管理这种复杂性。
\lstinline{bmap}
返回 inode
\lstinline{ip}
的第
\lstinline{bn}
个数据块的磁盘块号。
如果
\lstinline{ip}
还没有这样的块，
\lstinline{bmap}
会分配一个。

函数
\indexcode{bmap}
\lineref{kernel/fs.c:/^bmap/}
首先处理简单情况：前
\indexcode{NDIRECT}
个块列在 inode 本身中
\linerefs{kernel/fs.c:/^..if.bn.<.NDIRECT/,/^..}/}。
接下来的
\indexcode{NINDIRECT}
个块列在
\lstinline{ip->addrs[NDIRECT]}
的间接块中。
\lstinline{bmap}
读取间接块
\lineref{kernel/fs.c:/bp.=.bread.ip->dev..addr/}
然后从块内的正确
位置读取一个块号
\lineref{kernel/fs.c:/a.=..uint\*.bp->data/}。
如果块号超过
\lstinline{NDIRECT+NINDIRECT}，
\lstinline{bmap}
会 panic；
\lstinline{writei}
包含了防止这种情况发生的检查
\lineref{kernel/fs.c:/off...n...MAXFILE.BSIZE/}。

\lstinline{bmap}
根据需要分配块。
\lstinline{ip->addrs[]}
或间接
条目为零表示没有分配块。
当
\lstinline{bmap}
遇到零时，它会用新块的编号替换它们，
这些块是按需分配的
\linerefs{kernel/fs.c:/^....if..addr.=.*==.0/,/./}
\linerefs{kernel/fs.c:/^....if..addr.*NDIRECT.*==.0/,/./}。

\indexcode{itrunc}
释放文件的块，将 inode 的大小重置为零。
\lstinline{itrunc}
\lineref{kernel/fs.c:/^itrunc/}
首先释放直接块
\linerefs{kernel/fs.c:/^..for.i.=.0.*NDIRECT/,/^..}/}，
然后是间接块中列出的块
\linerefs{kernel/fs.c:/^....for.j.=.0.*NINDIRECT/,/^....}/}，
最后是间接块本身
\linerefs{kernel/fs.c:/^....bfree.*NDIRECT/,/./}。

\lstinline{bmap}
使得
\indexcode{readi}
和
\indexcode{writei}
很容易访问 inode 的数据。
\lstinline{readi}
\lineref{kernel/fs.c:/^readi/}
首先
确保偏移量和计数不会
超出文件末尾。
从文件末尾之后开始的读取会返回错误
\linerefs{kernel/fs.c:/^..if.off.>.ip->size/,/./}，
而从文件末尾开始或跨越文件末尾的读取
会返回比请求的少的字节
\linerefs{kernel/fs.c:/^..if.off.\+.n.>.ip->size/,/./}。
主循环处理文件的每个块，
将数据从缓冲区复制到
\lstinline{dst}
\linerefs{kernel/fs.c:/^..for.tot=0/,/^..}/}。
%% 注意：为 writei 编写行引用非常困难
%% 因为很多行与 readi 中的行相同。
%% 幸运的是，相同的行可能
%% 不需要注释。
\indexcode{writei}
\lineref{kernel/fs.c:/^writei/}
与
\indexcode{readi}
相同，
有三个例外：
从文件末尾开始或跨越文件末尾的写入
会增长文件，直到最大文件大小
\linerefs{kernel/fs.c:/^..if.off.\+.n.>.MAXFILE/,/./}；
循环将数据复制到缓冲区而不是从中复制出来
\lineref{kernel/fs.c:/either.copyin.*bp->data/}；
并且如果写入扩展了文件，
\indexcode{writei}
必须更新其大小
\linerefs{kernel/fs.c:/^..if.off.>.ip->size\)/,/./}。

%% Both
%% \indexcode{readi}
%% and
%% \indexcode{writei}
%% begin by checking for
%% \lstinline{ip->type}
%% \lstinline{==}
%% \indexcode{T_DEV}.
%% This case handles special devices whose data does not
%% live in the file system; we will return to this case in the file descriptor layer.

函数
\indexcode{stati}
\lineref{kernel/fs.c:/^stati\(/}
将 inode 元数据复制到
\lstinline{stat}
结构中，该结构通过
\indexcode{stat}
系统调用暴露给用户程序。
%% 
%% 
%% 
\section{代码：目录层}

目录在内部的实现方式与文件非常相似。
它的 inode 类型为
\indexcode{T_DIR}，
其数据是一系列目录条目。
每个条目都是一个
\indexcode{struct dirent}
\lineref{kernel/fs.h:/^struct.dirent/}，
其中包含一个名称和一个 inode 号。
名称最多为
\indexcode{DIRSIZ}
（14）个字符；
如果更短，则以 NULL (0) 字节结尾。
inode 号为零的目录条目是空闲的。

函数
\indexcode{dirlookup}
\lineref{kernel/fs.c:/^dirlookup/}
在目录中搜索具有给定名称的条目。
如果找到一个，它返回相应 inode 的指针，未锁定，
并设置
\lstinline{*poff}
为条目在目录中的字节偏移量，
以备调用者希望编辑它。
如果
\lstinline{dirlookup}
找到具有正确名称的条目，
它会更新
\lstinline{*poff}
并返回一个通过
\indexcode{iget}
获取的未锁定 inode。
\lstinline{dirlookup}
是
\lstinline{iget}
返回未锁定 inode 的原因。
调用者已锁定
\lstinline{dp}，
因此如果查找的是
\indexcode{.}，
即当前目录的别名，
在返回之前尝试锁定 inode 会试图重新锁定
\lstinline{dp}
并导致死锁。
（存在涉及多个进程和
\indexcode{..}，
即父目录的别名的更复杂的死锁场景；
\lstinline{.}
不是唯一的问题。）
调用者可以解锁
\lstinline{dp}
然后锁定
\lstinline{ip}，
确保一次只持有一个锁。

函数
\indexcode{dirlink}
\lineref{kernel/fs.c:/^dirlink/}
将一个具有给定名称和 inode 号的新目录条目写入
目录
\lstinline{dp}。
如果名称已存在，
\lstinline{dirlink}
返回一个错误
\linerefs{kernel/fs.c:/Check.that.name.is.not.present/,/^..}/}。
主循环读取目录条目，寻找一个未分配的条目。
当找到一个时，它会提前停止循环
\linerefs{kernel/fs.c:/Look for an empty dirent/,/.*break/}，
并将
\lstinline{off}
设置为可用条目的偏移量。
否则，循环结束时
\lstinline{off}
被设置为
\lstinline{dp->size}。
无论哪种方式，
\lstinline{dirlink}
然后通过在偏移量
\lstinline{off}
处写入来向目录添加一个新条目
\linerefs{kernel/fs.c:/if.writei/,/return -1/}。
%% 
%% 
%% 
\section{代码：路径名}

路径名查找涉及一系列对
\indexcode{dirlookup}
的调用，
每个路径组件一次。
\lstinline{namei}
\lineref{kernel/fs.c:/^namei/}
评估
\lstinline{path}
并返回相应的
\lstinline{inode}。
函数
\indexcode{nameiparent}
是一个变体：它在最后一个元素之前停止，返回
父目录的 inode 并将最后一个元素复制到
\lstinline{name} 中。
两者都调用通用函数
\indexcode{namex}
来完成实际工作。

\lstinline{namex}
\lineref{kernel/fs.c:/^namex/}
首先决定路径评估从哪里开始。
如果路径以斜杠开头，评估从根目录开始；
否则，从当前目录开始
\linerefs{kernel/fs.c:/..if.\*path.==....\)/,/idup/}。
然后它使用
\indexcode{skipelem}
依次考虑路径的每个元素
\lineref{kernel/fs.c:/while.*skipelem/}。
循环的每次迭代都必须在当前 inode
\lstinline{ip}
中查找
\lstinline{name}。
迭代开始时锁定
\lstinline{ip}
并检查它是否是一个目录。
如果不是，查找失败
\linerefs{kernel/fs.c:/^....ilock.ip/,/^....}/}。
（锁定
\lstinline{ip}
是必要的，不是因为
\lstinline{ip->type}
会发生变化——它不会——而是因为
直到
\indexcode{ilock}
运行，
\lstinline{ip->type}
不保证已从磁盘加载。）
如果调用是
\indexcode{nameiparent}
并且这是最后一个路径元素，则循环提前停止，
根据
\lstinline{nameiparent}
的定义；
最后一个路径元素已经复制
到
\lstinline{name} 中，
所以
\indexcode{namex}
只需要
返回未锁定的
\lstinline{ip}
\linerefs{kernel/fs.c:/^....if.nameiparent/,/^....}/}。
最后，循环使用
\indexcode{dirlookup}
查找路径元素
并设置
\lstinline{ip = next}
为下一次迭代做准备
\linerefs{kernel/fs.c:/^....if..next.*dirlookup/,/^....ip.=.next/}。
当循环用完路径元素时，它返回
\lstinline{ip}。

过程
\lstinline{namex}
可能需要很长时间才能完成：它可能涉及多次磁盘操作来
读取路径中遍历的目录的 inode 和目录块
（如果它们不在缓冲区缓存中）。Xv6 经过精心设计，以便如果一个内核线程的
\lstinline{namex}
调用被磁盘 I/O 阻塞，另一个查找
不同路径名的内核线程可以并发进行。
\lstinline{namex}
分别锁定路径中的每个目录，以便在不同
目录中的查找可以并行进行。

这种并发性带来了一些挑战。例如，当一个内核
线程正在查找一个路径名时，另一个内核线程可能正在通过取消链接一个目录来更改
目录树。一个潜在的风险是，查找
可能正在搜索一个已被另一个内核线程删除的目录，
并且其块已被重用于另一个目录或文件。

Xv6 避免了这种竞争。例如，在
\lstinline{namex}
中执行
\lstinline{dirlookup}
时，
查找线程持有目录的锁，并且
\lstinline{dirlookup}
返回一个使用
\lstinline{iget}
获得的 inode。
\lstinline{iget}
增加了 inode 的引用计数。只有在从
\lstinline{dirlookup}
接收到 inode 之后，
\lstinline{namex}
才会释放目录上的锁。现在另一个线程可能会从
目录中取消链接该 inode，但 xv6 不会删除该 inode，因为
该 inode 的引用计数仍然大于零。

另一个风险是死锁。例如，当查找“.”时，
\lstinline{next}
指向与
\lstinline{ip}
相同的 inode。
在释放
\lstinline{ip}
上的锁之前锁定
\lstinline{next}
将导致死锁。
为了避免这种死锁，
\lstinline{namex}
在获取
\lstinline{next}
上的锁之前解锁目录。
这里我们再次看到为什么
\lstinline{iget}
和
\lstinline{ilock}
之间的分离很重要。
%% 
%% 
%% 
\section{文件描述符层}

Unix 接口的一个很酷的方面是，Unix 中的大多数资源
都表示为文件，包括设备（如控制台）、管道，当然还有
真正的文件。文件描述符层就是实现这种
统一性的层。

正如我们在
第 \ref{CH:UNIX} 章中看到的，Xv6 为每个进程提供了自己的打开文件表，或
文件描述符。
每个打开的文件都由一个
\indexcode{struct file}
\lineref{kernel/file.h:/^struct.file/} 表示，
它是一个 inode 或管道的包装器，
外加一个 I/O 偏移量。
每次调用
\indexcode{open}
都会创建一个新的打开文件（一个新的
\lstinline{struct}
\lstinline{file}）：
如果多个进程独立打开同一个文件，
不同的实例将有不同的 I/O 偏移量。
另一方面，一个单独的打开文件
（同一个
\lstinline{struct}
\lstinline{file}）
可以
多次出现在一个进程的文件表中，
也可以出现在多个进程的文件表中。
如果一个进程使用
\lstinline{open}
打开文件，然后使用
\indexcode{dup}
创建别名，
或使用
\indexcode{fork}
与子进程共享，就会发生这种情况。
引用计数跟踪对
特定打开文件的引用次数。
一个文件可以以只读、只写或读写方式打开。
\lstinline{readable}
和
\lstinline{writable}
字段跟踪这一点。

系统中所有打开的文件都保存在一个全局文件表中，
即
\indexcode{ftable}。
文件表
有分配文件的函数
（\indexcode{filealloc}）、
创建重复引用的函数
（\indexcode{filedup}）、
释放引用的函数
（\indexcode{fileclose}），
以及读写数据的函数
（\indexcode{fileread}
和
\indexcode{filewrite}）。

前三个遵循了现在熟悉的形式。
\lstinline{filealloc}
\lineref{kernel/file.c:/^filealloc/}
扫描文件表，查找一个未被引用的文件
（\lstinline{f->ref}
\lstinline{==}
\lstinline{0}）
并返回一个新的引用；
\indexcode{filedup}
\lineref{kernel/file.c:/^filedup/}
增加引用计数；
而
\indexcode{fileclose}
\lineref{kernel/file.c:/^fileclose/}
减少它。
当一个文件的引用计数达到零时，
\lstinline{fileclose}
根据类型释放底层的管道或 inode。

函数
\indexcode{filestat}、
\indexcode{fileread}
和
\indexcode{filewrite}
实现了对文件的
\indexcode{stat}、
\indexcode{read}
和
\indexcode{write}
操作。
\lstinline{filestat}
\lineref{kernel/file.c:/^filestat/}
只允许在 inode 上调用，并调用
\indexcode{stati}。
\lstinline{fileread}
和
\lstinline{filewrite}
检查操作是否被
打开模式允许，然后
将调用传递给
管道或 inode 的实现。
如果文件代表一个 inode，
\lstinline{fileread}
和
\lstinline{filewrite}
使用 I/O 偏移量作为操作的偏移量，
然后将其推进
\linerefs{kernel/file.c:/readi/,/./}
\linerefs{kernel/file.c:/writei/,/./}。
管道没有偏移量的概念。
回想一下，inode 函数要求调用者
处理锁定
\linerefs{kernel/file.c:/stati/-1,/iunlock/}
\linerefs{kernel/file.c:/readi/-1,/iunlock/}
\linerefs{kernel/file.c:/writei\(f/-1,/iunlock/}。
inode 锁定的一个方便的副作用是
读写偏移量是原子更新的，因此
多个进程同时写入同一个文件
不会覆盖彼此的数据，尽管它们的写入最终可能会交错。
%% 
%% 
%% 
\section{代码：系统调用}

有了底层提供的函数，大多数
系统调用的实现都是微不足道的
（见
\fileref{kernel/sysfile.c}）。
有几个调用
值得仔细研究。

函数
\indexcode{sys_link}
和
\indexcode{sys_unlink}
编辑目录，创建或删除对 inode 的引用。
它们是使用
事务强大功能的另一个好例子。
\lstinline{sys_link}
\lineref{kernel/sysfile.c:/^sys_link/}
首先获取其参数，两个字符串
\lstinline{old}
和
\lstinline{new}
\lineref{kernel/sysfile.c:/argstr.*old.*new/}。
假设
\lstinline{old}
存在且不是目录
\linerefs{kernel/sysfile.c:/namei.old/,/^..}/}，
\lstinline{sys_link}
增加其
\lstinline{ip->nlink}
计数。
然后
\lstinline{sys_link}
调用
\indexcode{nameiparent}
来查找
\lstinline{new}
的父目录和最后一个路径元素
\lineref{kernel/sysfile.c:/nameiparent.new/}
并创建一个指向
\lstinline{old}
的 inode 的新目录条目
\lineref{kernel/sysfile.c:/\|\| dirlink/}。
新的父目录必须存在并且
与现有 inode 在同一设备上：
inode 号仅在单个磁盘上有唯一含义。
如果发生此类错误，
\indexcode{sys_link}
必须返回并减少
\lstinline{ip->nlink}。

事务简化了实现，因为它需要更新多个
磁盘块，但我们不必担心我们执行它们的顺序。
它们要么全部成功，要么全部失败。
例如，没有事务，在创建链接之前更新
\lstinline{ip->nlink}，
会使文件系统暂时处于不安全状态，
而中间的崩溃可能会导致混乱。
有了事务，我们就不必担心这个问题了。

\lstinline{sys_link}
为现有 inode 创建一个新名称。
函数
\indexcode{create}
\lineref{kernel/sysfile.c:/^create/}
为新 inode 创建一个新名称。
它是三个文件创建
系统调用的泛化：
带有
\indexcode{O_CREATE}
标志的
\indexcode{open}
创建一个新的普通文件，
\indexcode{mkdir}
创建一个新目录，
而
\indexcode{mkdev}
创建一个新的设备文件。
像
\indexcode{sys_link}
一样，
\indexcode{create}
首先调用
\indexcode{nameiparent}
来获取父目录的 inode。
然后它调用
\indexcode{dirlookup}
来检查名称是否已存在
\lineref{kernel/sysfile.c:/dirlookup.*[^=]=.0/}。
如果名称确实存在，
\lstinline{create}
的行为取决于它被用于哪个系统调用：
\lstinline{open}
的语义与
\indexcode{mkdir}
和
\indexcode{mkdev}
不同。
如果
\lstinline{create}
代表
\lstinline{open}
被使用
（\lstinline{type}
\lstinline{==}
\indexcode{T_FILE}）
并且存在的名称本身
是一个常规文件，
那么
\lstinline{open}
将其视为成功，
所以
\lstinline{create}
也这样做
\lineref{kernel/sysfile.c:/^......return.ip/}。
否则，这是一个错误
\linerefs{kernel/sysfile.c:/^......return.ip/+1,/return.0/}。
如果名称尚不存在，
\lstinline{create}
现在用
\indexcode{ialloc}
分配一个新的 inode
\lineref{kernel/sysfile.c:/ialloc/}。
如果新的 inode 是一个目录，
\lstinline{create}
用
\indexcode{.}
和
\indexcode{..}
条目初始化它。
最后，既然数据已正确初始化，
\indexcode{create}
可以将其链接到父目录中
\lineref{kernel/sysfile.c:/if.dirlink/}。
\lstinline{create}，
像
\indexcode{sys_link}
一样，
同时持有两个 inode 锁：
\lstinline{ip}
和
\lstinline{dp}。
不存在死锁的可能性，因为
inode
\lstinline{ip}
是新分配的：系统中的没有其他进程
会持有
\lstinline{ip}
的锁，然后尝试锁定
\lstinline{dp}。

使用
\lstinline{create}，
很容易实现
\indexcode{sys_open}、
\indexcode{sys_mkdir}
和
\indexcode{sys_mknod}。
\lstinline{sys_open}
\lineref{kernel/sysfile.c:/^sys_open/}
是最复杂的，因为创建一个新文件只是
它能做的一小部分。
如果
\indexcode{open}
被传递
\indexcode{O_CREATE}
标志，它会调用
\lstinline{create}
\lineref{kernel/sysfile.c:/create.*T_FILE/}。
否则，它会调用
\indexcode{namei}
\lineref{kernel/sysfile.c:/if..ip.=.namei.path/}。
\lstinline{create}
返回一个锁定的 inode，但
\lstinline{namei}
不返回，所以
\indexcode{sys_open}
必须自己锁定 inode。
这提供了一个方便的地方来检查目录
是否只为读取而打开，而不是写入。
假设 inode 以某种方式获得，
\lstinline{sys_open}
分配一个文件和一个文件描述符
\lineref{kernel/sysfile.c:/filealloc.*fdalloc/}
然后填充该文件
\linerefs{kernel/sysfile.c:/type.=.FD_INODE/,/writable/}。
请注意，没有其他进程可以访问部分初始化的文件，因为它只
在当前进程的表中。

第 \ref{CH:SCHED} 章在我们甚至还没有文件系统之前就研究了管道的实现。
函数
\indexcode{sys_pipe}
通过提供一种创建管道对的方法，将该实现与文件系统
连接起来。
它的参数是一个指向两个整数空间的指针，
它将在那里记录两个新的文件描述符。
然后它分配管道并安装文件描述符。
%% 
%%  -------------------------------------------
%% 
\section{现实世界}

现实世界操作系统中的缓冲区缓存明显
比 xv6 的复杂，但它服务于相同的两个目的：
缓存和同步对磁盘的访问。
xv6 的缓冲区缓存，像 V6 的一样，使用简单的最近最少使用 (LRU)
驱逐策略；还有许多更复杂的
策略可以实现，每种策略对某些
工作负载有利，而对其他工作负载则不那么有利。
一个更高效的 LRU 缓存将消除链表，
而是使用哈希表进行查找，使用堆进行 LRU 驱逐。
现代缓冲区缓存通常与
虚拟内存系统集成，以支持内存映射文件。

Xv6 的日志系统效率低下。
提交不能与文件系统系统调用并发进行。
系统记录整个块，即使
块中只有几个字节被更改。它执行同步
日志写入，一次一个块，每次写入都可能需要
整个磁盘旋转时间。真正的日志系统解决了所有这些
问题。

日志记录不是提供崩溃恢复的唯一方法。早期的文件系统
在重启期间使用清道夫（例如，UNIX
\indexcode{fsck}
程序）来检查每个文件和目录以及块和 inode
空闲列表，查找并解决不一致性。对于大型文件系统，清道夫可能需要
数小时，并且在某些情况下，不可能
以使原始
系统调用具有原子性的方式解决不一致性。从日志恢复
要快得多，并使系统调用在
崩溃面前具有原子性。

Xv6 使用与早期 UNIX 相同的基本磁盘布局的 inode 和目录；
这种方案多年来一直非常持久。
BSD 的 UFS/FFS 和 Linux 的 ext2/ext3 使用基本相同的数据结构。
文件系统布局中效率最低的部分是目录，
每次查找都需要对所有磁盘块进行线性扫描。
当目录只有几个磁盘块时，这是合理的，
但对于包含许多文件的目录来说，这是昂贵的。
微软 Windows 的 NTFS、macOS 的 HFS 和 Solaris 的 ZFS，仅举几例，
将目录实现为磁盘上块的平衡树。
这很复杂，但保证了对数时间的目录查找。

Xv6 对磁盘故障很天真：如果磁盘
操作失败，xv6 会 panic。
这是否合理取决于硬件：
如果操作系统位于使用
冗余来掩盖磁盘故障的特殊硬件之上，也许操作系统
很少看到故障，以至于 panic 是可以接受的。
另一方面，使用普通磁盘的操作系统
应该预料到故障并更优雅地处理它们，
以便一个文件中块的丢失不会影响
文件系统其余部分的使用。

Xv6 要求文件系统
适合一个磁盘设备并且大小不变。
随着大型数据库和多媒体文件对存储
需求的不断提高，操作系统正在开发
消除“每个文件系统一个磁盘”瓶颈的方法。
基本方法是将许多磁盘组合成一个
逻辑磁盘。像 RAID 这样的硬件解决方案仍然是
最流行的，但目前的趋势是尽可能多地在软件中实现
这种逻辑。
这些软件实现通常
允许丰富的功能，如通过动态添加或删除磁盘来增长或缩小逻辑
设备。
当然，一个可以动态增长或缩小的存储层
需要一个可以做同样事情的文件系统：xv6 使用的固定大小的 inode 块数组
在这样的环境中效果不佳。
将磁盘管理与文件系统分开可能是
最清晰的设计，但两者之间复杂的接口
导致一些系统，如 Sun 的 ZFS，将它们结合起来。

Xv6 的文件系统缺少现代文件系统的许多其他功能；例如，
它缺少对快照和增量备份的支持。

现代 Unix 系统允许使用与磁盘存储相同的系统调用访问多种资源：
命名管道、网络连接、
远程访问的网络文件系统，以及监控和控制
接口，如
\lstinline{/proc}。
而不是 xv6 在
\indexcode{fileread}
和
\indexcode{filewrite}
中的
\lstinline{if}
语句，
这些系统通常为每个打开的文件提供一个函数指针表，
每个操作一个，
并调用函数指针来调用该 inode 对该调用的
实现。
网络文件系统和用户级文件系统
提供将这些调用转换为网络 RPC
并在返回前等待响应的函数。
%% 
%%  -------------------------------------------
%% 
\section{练习}

\begin{enumerate}

\item 为什么在
\lstinline{balloc}
中 panic？
xv6 能恢复吗？

\item 为什么在
\lstinline{ialloc}
中 panic？
xv6 能恢复吗？

\item 为什么
\lstinline{filealloc}
在文件用完时不会 panic？
为什么这种情况更常见，因此值得处理？

\item 假设与
\lstinline{ip}
对应的文件在
\lstinline{sys_link}
调用
\lstinline{iunlock(ip)}
和
\lstinline{dirlink}
之间被另一个进程取消链接。
链接会正确创建吗？
为什么或为什么不？

\item
\lstinline{create}
进行了四个函数调用（一个到
\lstinline{ialloc}
和三个到
\lstinline{dirlink}），
它要求这些调用必须成功。
如果任何一个不成功，
\lstinline{create}
会调用
\lstinline{panic}。
为什么这是可以接受的？
为什么这四个调用都不会失败？

\item
\lstinline{sys_chdir}
在
\lstinline{iput(cp->cwd)}
之前调用
\lstinline{iunlock(ip)}，
而
\lstinline{iput(cp->cwd)}
可能会尝试锁定
\lstinline{cp->cwd}，
然而推迟
\lstinline{iunlock(ip)}
到
\lstinline{iput}
之后不会导致死锁。
为什么不呢？

\item 实现
\lstinline{lseek}
系统调用。支持
\lstinline{lseek}
还需要你修改
\lstinline{filewrite}
以在
\lstinline{lseek}
将
\lstinline{off}
设置到
\lstinline{f->ip->size}
之外时用零填充文件中的空洞。

\item 向
\lstinline{open}
添加
\lstinline{O_TRUNC}
和
\lstinline{O_APPEND}，
以便
\lstinline{>}
和
\lstinline{>>}
运算符在 shell 中工作。

\item 修改文件系统以支持符号链接。

\item 修改文件系统以支持命名管道。

\item 修改文件和虚拟机系统以支持内存映射文件。

\end{enumerate}