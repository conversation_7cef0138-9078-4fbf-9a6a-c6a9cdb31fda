\chapter*{前言与致谢}

这是一份用于操作系统课程的教材草稿。它通过研究一个名为 xv6 的示例内核来解释操作系统的主要概念。Xv6 仿照 <PERSON> 和 Ken <PERSON> 的 Unix 第 6 版 (v6)~\cite{unix} 建模。Xv6 松散地遵循了 v6 的结构和风格，但是使用 ANSI C~\cite{k<PERSON><PERSON><PERSON>} 为多核 RISC-V~\cite{riscv} 实现。

本文本应与 xv6 的源代码一起阅读，这种方法受到了 John Lions 的《UNIX 第 6 版评注》~\cite{lions} 的启发；文本中包含了指向 \url{https://github.com/mit-pdos/xv6-riscv} 源代码的超链接。有关 v6 和 xv6 的在线资源，包括几个使用 xv6 的实验作业，请参见 \url{https://pdos.csail.mit.edu/6.1810}。

我们在 MIT 的操作系统课程 6.828 和 6.1810 中使用了这本教材。我们感谢这些课程的教职员工、助教和学生，他们都直接或间接地为 xv6 做出了贡献。我们尤其要感谢 <PERSON>、Austin Clements 和 <PERSON><PERSON><PERSON>。最后，我们要感谢那些通过电子邮件向我们报告文本中的错误或提出改进建议的人们：Abutalib Aghayev, Sebastian Boehm, brandb97, Anton Burtsev, Raphael Carvalho, Tej Chajed,Brendan Davidson, Rasit Eskicioglu, Color Fuzzy, Wojciech Gac, <PERSON>, Tao Guo, Haibo Hao, Naoki Hayama, <PERSON> Henderson, Robert Hilderman, Eden Hochbaum, Wolfgang Keller, Paweł Kraszewski, Henry Laih, Jin Li, Austin Liew, <EMAIL>, Pavan Maddamsetti, Jacek Masiulaniec, Michael McConville, m3hm00d, miguelgvieira, Mark Morrissey, Muhammed Mourad, Harry Pan, Harry Porter, Siyuan Qian, Zhefeng Qiao, Askar Safin, Salman Shah, Huang Sha, Vikram Shenoy, Adeodato Simó, Ruslan Savchenko, Pawel Szczurko, Warren Toomey, tyfkda, tzerbib, Vanush Vaswani, Xi Wang, and Zou Chang Wei, Sam Whitlock, Qiongsi Wu, LucyShawYang, <EMAIL>, and Meng Zhou

如果您发现错误或有改进建议，请发送电子邮件至 Frans Kaashoek 和 Robert Morris (kaashoek,<EMAIL>)。