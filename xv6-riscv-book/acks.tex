\chapter*{Foreword and acknowledgments}


This is a draft text intended for a class on operating systems. It
explains the main concepts of operating systems by studying an example
kernel, named xv6.  Xv6 is modeled on <PERSON>'s and
<PERSON>'s Unix Version 6 (v6)~\cite{unix}.  Xv6 loosely follows the structure
and style of v6, but is implemented in ANSI C~\cite{<PERSON><PERSON><PERSON><PERSON>} for 
a multi-core RISC-V~\cite{riscv}.

This text should be read along with the source code for xv6, an
approach inspired by <PERSON>' Commentary on UNIX 6th
Edition~\cite{lions}; the text has hyperlinks to the source code at
\url{https://github.com/mit-pdos/xv6-riscv}. See
\url{https://pdos.csail.mit.edu/6.1810} for additional pointers to
on-line resources for v6 and xv6, including several lab assignments
using xv6.

We have used this text in 6.828 and 6.1810, the operating system
classes at MIT.  We thank the faculty, teaching assistants, and
students of those classes who have all directly or indirectly
contributed to xv6.  In particular, we would like to thank <PERSON>,
<PERSON>, and <PERSON><PERSON><PERSON>.  Finally, we would like to
thank people who emailed us bugs in the text or suggestions for
improvements: <PERSON>, <PERSON>, <PERSON>b9<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>,<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>,
<PERSON>, <PERSON>, <PERSON> <PERSON>, <PERSON> <PERSON>,
<PERSON>, <PERSON> <PERSON>, <PERSON> <PERSON><PERSON>, <PERSON> <PERSON>, <PERSON> <PERSON><PERSON>,
l<PERSON>@g<PERSON>.com, <PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON> <PERSON><PERSON>, <PERSON>
<PERSON><PERSON><PERSON><PERSON><PERSON>, m<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Muhammed Mourad,
Harry Pan, Harry Porter, Siyuan Qian, Zhefeng Qiao, Askar Safin,
Salman Shah, Huang Sha, Vikram Shenoy, Adeodato Simó, Ruslan
Savchenko, Pawel Szczurko, Warren Toomey, tyfkda, tzerbib, Vanush
Vaswani, Xi Wang, and Zou Chang Wei, Sam Whitlock, Qiongsi Wu,
LucyShawYang, <EMAIL>, and Meng Zhou

If you spot errors or have suggestions for improvement, please send email to
Frans Kaashoek and Robert Morris (kaashoek,<EMAIL>).
