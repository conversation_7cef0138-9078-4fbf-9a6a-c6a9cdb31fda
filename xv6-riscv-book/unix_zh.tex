\chapter{操作系统接口}
\label{CH:UNIX}

操作系统的任务是在多个程序之间共享一台计算机，并提供一组比硬件本身支持的更有用的服务。
操作系统管理和抽象底层硬件，因此，例如，文字处理器无需关心正在使用哪种类型的磁盘硬件。
操作系统在多个程序之间共享硬件，以便它们可以同时运行（或看起来是同时运行）。
最后，操作系统为程序之间的交互提供了受控的方式，以便它们可以共享数据或协同工作。

操作系统通过一个接口向用户程序提供服务。
\index{interface design}
设计一个好的接口是困难的。一方面，我们希望接口简单而精炼，因为这样更容易确保实现的正确性。另一方面，我们可能想为应用程序提供许多复杂的功能。
解决这种矛盾的诀窍是设计依赖于少数机制的接口，这些机制可以组合起来提供很大的通用性。

本书使用一个单一的操作系统作为具体例子来说明操作系统的概念。那个操作系统，
xv6，提供了由Ken Thompson和Dennis Ritchie的Unix操作系统\cite{unix}引入的基本接口，并模仿了Unix的内部设计。Unix提供了一个精炼的接口，其机制结合得很好，提供了惊人的通用性。这个接口非常成功，以至于现代操作系统——BSD、Linux、macOS、Solaris，甚至在较小程度上，Microsoft Windows——都有类似Unix的接口。
理解xv6是理解这些系统以及许多其他系统的一个良好开端。

如图\ref{fig:os}所示，xv6采用了传统的\indextext{内核}形式，它是一个为正在运行的程序提供服务的特殊程序。
每个正在运行的程序，称为\indextext{进程}，都有一块内存，其中包含指令、数据和一个栈。指令实现了程序的计算。数据是计算作用于的变量。栈组织程序的过程序调用。
一台给定的计算机通常有许多进程，但只有一个内核。

当一个进程需要调用内核服务时，它会调用一个\indextext{系统调用}，这是操作系统接口中的一个调用。
系统调用进入内核；内核执行服务并返回。
因此，一个进程在\indextext{用户空间}和\indextext{内核空间}之间交替执行。

如后续章节所详述，内核使用CPU\footnote{
本文档通常使用术语\indextext{CPU}（中央处理单元的缩写）来指代执行计算的硬件元件。其他文档（例如，RISC-V规范）也使用处理器、核心和hart等词来代替CPU。
}提供的硬件保护机制来确保每个在用户空间执行的进程只能访问自己的内存。
内核以实现这些保护所需的硬件特权执行；用户程序在没有这些特权的情况下执行。
当用户程序调用系统调用时，硬件会提升特权级别，并开始执行内核中预先安排好的函数。

\begin{figure}[t]
\center
\includegraphics[scale=0.5]{fig/os.pdf}
\caption{一个内核和两个用户进程。}
\label{fig:os}
\end{figure}

内核提供的系统调用集合是用户程序看到的接口。
xv6内核提供了Unix内核传统上提供的一部分服务和系统调用。
图\ref{fig:api}列出了xv6的所有系统调用。

本章的其余部分概述了xv6的服务——进程、内存、文件描述符、管道和文件系统——并通过代码片段和对\indextext{shell}（Unix的命令行用户界面）如何使用它们的讨论来说明它们。shell对系统调用的使用说明了它们是如何被精心设计的。

shell是一个普通的程序，它从用户那里读取命令并执行它们。
shell是一个用户程序，而不是内核的一部分，这一事实说明了系统调用接口的强大之处：shell没有什么特别之处。
这也意味着shell很容易被替换；因此，现代Unix系统有各种各样的shell可供选择，每种都有自己的用户界面和脚本功能。
xv6 shell是Unix Bourne shell精髓的一个简单实现。其实现可以在\lineref{user/sh.c:1}找到。
%%
%%      进程和内存
%%
\section{进程和内存}

一个xv6进程由用户空间内存（指令、数据和栈）和内核私有的每个进程的状态组成。
Xv6\indextext{分时}处理进程：它透明地在等待执行的进程集合中切换可用的CPU。
当一个进程不执行时，xv6会保存该进程的CPU寄存器，并在下次运行该进程时恢复它们。
内核为每个进程关联一个进程标识符，或\indexcode{PID}。

\begin{figure}[t]
\center
\begin{tabular}{ll}
{\bf 系统调用} & {\bf 描述} \\
\midrule
int fork() & 创建一个进程，返回子进程的PID。 \\
int exit(int status) & 终止当前进程；状态报告给wait()。无返回。 \\
int wait(int *status) & 等待一个子进程退出；退出状态在*status中；返回子进程PID。 \\
int kill(int pid) & 终止进程PID。成功返回0，错误返回-1。 \\
int getpid() & 返回当前进程的PID。 \\
int sleep(int n) & 暂停n个时钟周期。 \\
int exec(char *file, char *argv[]) & 加载一个文件并带参数执行它；仅在出错时返回。 \\
char *sbrk(int n) & 将进程的内存增长n个零字节。返回新内存的起始地址。 \\
int open(char *file, int flags) & 打开一个文件；flags指示读/写；返回一个fd（文件描述符）。 \\
int write(int fd, char *buf, int n) & 从buf向文件描述符fd写入n个字节；返回n。 \\
int read(int fd, char *buf, int n) & 读入n个字节到buf；返回读取的字节数；文件末尾返回0。 \\
int close(int fd) & 释放打开的文件fd。 \\
int dup(int fd) & 返回一个新的文件描述符，引用与fd相同的文件。\\
int pipe(int p[]) & 创建一个管道，将读/写文件描述符放入p[0]和p[1]。 \\
int chdir(char *dir) & 更改当前目录。 \\
int mkdir(char *dir) & 创建一个新目录。 \\
int mknod(char *file, int, int) & 创建一个设备文件。 \\
int fstat(int fd, struct stat *st) & 将有关打开文件的信息放入*st。 \\
int link(char *file1, char *file2) & 为文件file1创建另一个名称(file2)。 \\
int unlink(char *file) & 删除一个文件。 \\
\end{tabular}
\caption{Xv6系统调用。如无特别说明，这些调用成功时返回0，出错时返回-1。}
\label{fig:api}
\end{figure}

一个进程可以使用\indexcode{fork}系统调用创建一个新进程。
\lstinline{fork}为新进程提供调用进程内存的精确副本：它将调用进程的指令、数据和栈复制到新进程的内存中。
\lstinline{fork}在原始进程和新进程中都会返回。
在原始进程中，\lstinline{fork}返回新进程的PID。
在新进程中，\lstinline{fork}返回零。
原始进程和新进程通常被称为\indextext{父进程}和\indextext{子进程}。

例如，考虑以下用C编程语言\cite{kernighan}编写的程序片段：
\begin{lstlisting}[]
int pid = fork();
if(pid > 0){
  printf("parent: child=%d\n", pid);
  pid = wait((int *) 0);
  printf("child %d is done\n", pid);
} else if(pid == 0){
  printf("child: exiting\n");
  exit(0);
} else {
  printf("fork error\n");
}
\end{lstlisting}
\indexcode{exit}系统调用使调用进程停止执行并释放资源，如内存和打开的文件。
Exit接受一个整数状态参数，通常0表示成功，1表示失败。
\indexcode{wait}系统调用返回当前进程的一个已退出（或被杀死）的子进程的PID，并将子进程的退出状态复制到传递给wait的地址；如果调用者的子进程都没有退出，\indexcode{wait}会等待其中一个退出。
如果调用者没有子进程，\lstinline{wait}立即返回-1。
如果父进程不关心子进程的退出状态，它可以向\lstinline{wait}传递一个0地址。

在示例中，输出行
\begin{lstlisting}[]
parent: child=1234
child: exiting
\end{lstlisting}
可能会以任何顺序出现（甚至混合在一起），这取决于父进程或子进程哪个先到达其\indexcode{printf}调用。
子进程退出后，父进程的\indexcode{wait}返回，导致父进程打印
\begin{lstlisting}[]
parent: child 1234 is done
\end{lstlisting}
尽管子进程最初具有与父进程相同的内存内容，但父进程和子进程使用独立的内存和独立的寄存器执行：
在一个进程中更改一个变量不会影响另一个进程。例如，当\lstinline{wait}的返回值存储到父进程的\lstinline{pid}中时，它不会改变子进程中的变量\lstinline{pid}。子进程中的\lstinline{pid}值仍将为零。

\indexcode{exec}系统调用用从文件系统中加载的新内存映像替换调用进程的内存。
该文件必须具有特定的格式，该格式指定文件的哪一部分包含指令，哪一部分是数据，从哪个指令开始执行等。Xv6使用ELF格式，第\ref{CH:MEM}章将对此进行更详细的讨论。
通常，该文件是编译程序源代码的结果。
当\indexcode{exec}成功时，它不会返回到调用程序；相反，从文件加载的指令在ELF头中声明的入口点开始执行。
\lstinline{exec}接受两个参数：包含可执行文件的文件名和一个字符串参数数组。
例如：
\begin{lstlisting}[]
char *argv[3];
argv[0] = "echo";
argv[1] = "hello";
argv[2] = 0;
exec("/bin/echo", argv);
printf("exec error\n");
\end{lstlisting}
该片段将调用程序替换为以参数列表\lstinline{echo hello}运行的程序\lstinline{/bin/echo}的实例。
大多数程序忽略参数数组的第一个元素，该元素通常是程序的名称。

xv6 shell使用上述调用代表用户运行程序。shell的主要结构很简单；请参见\lstinline{main} \lineref{user/sh.c:/main/}。
主循环使用\indexcode{getcmd}从用户读取一行输入。
然后它调用\lstinline{fork}，创建一个shell进程的副本。父进程调用\lstinline{wait}，而子进程运行命令。例如，如果用户向shell键入``\lstinline{echo hello}''，\lstinline{runcmd}将被调用，参数为``\lstinline{echo hello}''。
\lstinline{runcmd} \lineref{user/sh.c:/runcmd/}运行实际的命令。对于``\lstinline{echo hello}''，它会调用\lstinline{exec} \lineref{user/sh.c:/exec.ecmd/}。
如果\lstinline{exec}成功，则子进程将执行来自\lstinline{echo}的指令，而不是\lstinline{runcmd}。
在某个时候\lstinline{echo}会调用\lstinline{exit}，这将导致父进程从\lstinline{main} \lineref{user/sh.c:/main/}中的\lstinline{wait}返回。

您可能想知道为什么\indexcode{fork}和\indexcode{exec}没有合并成一个调用；我们稍后会看到，shell在其I/O重定向的实现中利用了这种分离。
为了避免创建重复进程然后立即替换它（使用\lstinline{exec}）的浪费，操作系统内核通过使用虚拟内存技术（如写时复制（见第\ref{sec:pagefaults}节））来优化\lstinline{fork}在这种用例下的实现。

Xv6隐式地分配大多数用户空间内存：\indexcode{fork}分配子进程复制父进程内存所需的内存，而\indexcode{exec}分配足以容纳可执行文件的内存。
在运行时需要更多内存的进程（例如，对于\indexcode{malloc}）可以调用\lstinline{sbrk(n)}来将其数据内存增长n个零字节；\indexcode{sbrk}返回新内存的位置。

%%
%%      I/O和文件描述符
%%
\section{I/O和文件描述符}

\indextext{文件描述符}是一个小整数，代表一个内核管理的对象，进程可以从中读取或向其写入。
进程可以通过打开文件、目录或设备，或者通过创建管道，或者通过复制现有描述符来获取文件描述符。
为简单起见，我们通常将文件描述符引用的对象称为“文件”；文件描述符接口抽象了文件、管道和设备之间的差异，使它们都看起来像字节流。
我们将输入和输出称为\indextext{I/O}。

在内部，xv6内核使用文件描述符作为每个进程表的索引，因此每个进程都有一个从零开始的私有文件描述符空间。
按照惯例，进程从文件描述符0（标准输入）读取，将输出写入文件描述符1（标准输出），并将错误消息写入文件描述符2（标准错误）。
正如我们将看到的，shell利用这个惯例来实现I/O重定向和管道。shell确保它始终有三个打开的文件描述符\lineref{user/sh.c:/open..console/}，默认情况下是控制台的文件描述符。

\lstinline{read}和\lstinline{write}系统调用从由文件描述符命名的打开文件中读取字节和写入字节。
调用\lstinline{read(fd, buf, n)}最多从文件描述符\lstinline{fd}读取\lstinline{n}个字节，将它们复制到\lstinline{buf}中，并返回读取的字节数。
每个引用文件的文件描述符都有一个与之关联的偏移量。
\lstinline{read}从当前文件偏移量读取数据，然后将该偏移量增加读取的字节数：
随后的\lstinline{read}将返回第一个\lstinline{read}返回的字节之后的字节。
当没有更多字节可读时，\lstinline{read}返回零以指示文件结尾。

调用\lstinline{write(fd, buf, n)}将\lstinline{n}个字节从\lstinline{buf}写入文件描述符\lstinline{fd}并返回写入的字节数。
只有在发生错误时才会写入少于\lstinline{n}个字节。
像\lstinline{read}一样，\lstinline{write}在当前文件偏移量处写入数据，然后将该偏移量增加写入的字节数：
每个\lstinline{write}都从前一个停止的地方继续。

以下程序片段（构成程序\lstinline{cat}的精髓）将其标准输入的数据复制到其标准输出。如果发生错误，它会向标准错误写入一条消息。
\begin{lstlisting}[]
char buf[512];
int n;
for(;;){
  n = read(0, buf, sizeof buf);
  if(n == 0)
    break;
  if(n < 0){
    fprintf(2, "read error\n");
    exit(1);
  }
  if(write(1, buf, n) != n){
    fprintf(2, "write error\n");
    exit(1);
  }
}
\end{lstlisting}
代码片段中需要注意的重要一点是，\lstinline{cat}不知道它是在从文件、控制台还是管道读取。
同样，\lstinline{cat}也不知道它是在向控制台、文件还是其他任何地方打印。
文件描述符的使用以及文件描述符0是输入、文件描述符1是输出的惯例，使得\lstinline{cat}的实现很简单。

\lstinline{close}系统调用释放一个文件描述符，使其可以被未来的\lstinline{open}、\lstinline{pipe}或\lstinline{dup}系统调用（见下文）重用。
新分配的文件描述符始终是当前进程的最低编号的未使用描述符。

文件描述符和\indexcode{fork}的交互使得I/O重定向易于实现。
\lstinline{fork}复制父进程的文件描述符表及其内存，因此子进程以与父进程完全相同的打开文件开始。
系统调用\indexcode{exec}替换调用进程的内存，但保留其文件表。
这种行为允许shell通过forking，在子进程中重新打开选定的文件描述符，然后调用\lstinline{exec}来运行新程序，从而实现\indextext{I/O重定向}。
这是一个shell为命令\lstinline{cat < input.txt}运行的代码的简化版本：
\begin{lstlisting}[]
char *argv[2];
argv[0] = "cat";
argv[1] = 0;
if(fork() == 0) {
  close(0);
  open("input.txt", O_RDONLY);
  exec("cat", argv);
}
\end{lstlisting}
子进程关闭文件描述符0后，\lstinline{open}保证为新打开的\lstinline{input.txt}使用该文件描述符：0将是最小的可用文件描述符。
然后\lstinline{cat}执行，其文件描述符0（标准输入）引用\lstinline{input.txt}。
父进程的文件描述符不受此序列的影响，因为它只修改子进程的描述符。

xv6 shell中I/O重定向的代码正是以这种方式工作的\lineref{user/sh.c:/case.REDIR/}。
回想一下，在代码的这一点上，shell已经fork了子shell，并且\lstinline{runcmd}将调用\lstinline{exec}来加载新程序。

\lstinline{open}的第二个参数由一组标志组成，以位表示，控制\lstinline{open}的功能。可能的值在文件控制(fcntl)头文件\linerefs{kernel/fcntl.h:/RDONLY/,/TRUNC/}中定义：
\lstinline{O_RDONLY}、\lstinline{O_WRONLY}、\lstinline{O_RDWR}、\lstinline{O_CREATE}和\lstinline{O_TRUNC}，它们指示\lstinline{open}
以只读方式打开文件，
或以只写方式，
或以读写方式，
如果文件不存在则创建文件，
并将文件截断为零长度。

现在应该清楚为什么\lstinline{fork}和\lstinline{exec}是分开的调用很有帮助：在这两者之间，shell有机会重定向子进程的I/O，而不会干扰主shell的I/O设置。
人们可以想象一个假设的组合\lstinline{forkexec}系统调用，但使用这种调用进行I/O重定向的选项似乎很笨拙。
shell可以在调用\lstinline{forkexec}之前修改自己的I/O设置（然后撤消这些修改）；或者\lstinline{forkexec}可以将I/O重定向的指令作为参数；或者（最不吸引人的）每个像\lstinline{cat}这样的程序都可以被教导自己进行I/O重定向。

尽管\lstinline{fork}复制了文件描述符表，但每个底层的文件偏移量在父子进程之间是共享的。
考虑这个例子：
\begin{lstlisting}[]
if(fork() == 0) {
  write(1, "hello ", 6);
  exit(0);
} else {
  wait(0);
  write(1, "world\n", 6);
}
\end{lstlisting}
在这个片段的末尾，附加到文件描述符1的文件将包含数据\lstinline{hello world}。
父进程中的\lstinline{write}（由于\lstinline{wait}，仅在子进程完成后运行）从子进程的\lstinline{write}停止的地方继续。
这种行为有助于从shell命令序列中产生顺序输出，例如\lstinline{(echo hello; echo world) >output.txt}。

\lstinline{dup}系统调用复制一个现有的文件描述符，返回一个新的文件描述符，它引用相同的底层I/O对象。
两个文件描述符共享一个偏移量，就像\lstinline{fork}复制的文件描述符一样。
这是另一种将\lstinline{hello world}写入文件的方式：
\begin{lstlisting}[]
fd = dup(1);
write(1, "hello ", 6);
write(fd, "world\n", 6);
\end{lstlisting}

如果两个文件描述符是通过一系列\lstinline{fork}和\lstinline{dup}调用从同一个原始文件描述符派生出来的，那么它们共享一个偏移量。
否则，文件描述符不共享偏移量，即使它们是由对同一文件的\lstinline{open}调用产生的。
\lstinline{dup}允许shell实现像这样的命令：
\lstinline{ls existing-file non-existing-file > tmp1 2>&1}。
\lstinline{2>&1}告诉shell给命令一个文件描述符2，它是描述符1的副本。
现有文件的名称和不存在文件的错误消息都将显示在文件\lstinline{tmp1}中。
xv6 shell不支持错误文件描述符的I/O重定向，但现在您知道如何实现它了。

文件描述符是一个强大的抽象，因为它们隐藏了它们所连接的细节：
一个向文件描述符1写入的进程可能是在向文件、像控制台这样的设备或管道写入。
%%
%%      管道
%%
\section{管道}

\indextext{管道}是一个小的内核缓冲区，作为一对文件描述符暴露给进程，一个用于读取，一个用于写入。
向管道的一端写入数据，使得这些数据可以从管道的另一端读取。
管道为进程间通信提供了一种方式。

以下示例代码运行程序\lstinline{wc}，其标准输入连接到管道的读取端。
\begin{lstlisting}[]
int p[2];
char *argv[2];
argv[0] = "wc";
argv[1] = 0;
pipe(p);
if(fork() == 0) {
  close(0);
  dup(p[0]);
  close(p[0]);
  close(p[1]);
  exec("/bin/wc", argv);
} else {
  close(p[0]);
  write(p[1], "hello world\n", 12);
  close(p[1]);
}
\end{lstlisting}
程序调用\lstinline{pipe}，它创建一个新管道，并将读写文件描述符记录在数组\lstinline{p}中。
\lstinline{fork}之后，父子进程都有引用该管道的文件描述符。
子进程调用\lstinline{close}和\lstinline{dup}使文件描述符零引用管道的读取端，
关闭\lstinline{p}中的文件描述符，并调用\lstinline{exec}来运行\lstinline{wc}。
当\lstinline{wc}从其标准输入读取时，它从管道中读取。
父进程关闭管道的读取端，向管道写入，然后关闭写入端。

如果没有数据可用，对管道的\lstinline{read}会等待数据被写入或所有引用写入端的文件描述符被关闭；在后一种情况下，\lstinline{read}将返回0，就像到达数据文件的末尾一样。
\lstinline{read}会阻塞直到不可能有新数据到达，这是为什么在上面的\lstinline{wc}执行之前，子进程关闭管道的写入端很重要的原因之一：如果\lstinline{wc}的一个文件描述符引用了管道的写入端，\lstinline{wc}将永远看不到文件结束符。

xv6 shell以类似于上述代码的方式实现诸如\lstinline{grep fork sh.c | wc -l}之类的管道\lineref{user/sh.c:/case.PIPE/}。
子进程创建一个管道以连接管道的左端和右端。然后它为管道的左端调用\lstinline{fork}和\lstinline{runcmd}，为管道的右端调用\lstinline{fork}和\lstinline{runcmd}，并等待两者完成。
管道的右端可能是一个本身包含管道的命令（例如，\lstinline{a | b | c}），它本身会派生两个新的子进程（一个用于\lstinline{b}，一个用于\lstinline{c}）。
因此，shell可能会创建一个进程树。该树的叶子是命令，内部节点是等待左右子节点完成的进程。

% 原则上，可以让内部节点运行管道的左端，但这样做会使实现复杂化。考虑只做以下修改：
% 更改\lstinline{sh.c}以不为\lstinline{p->left}派生，并在内部进程中运行\lstinline{runcmd(p->left)}。然后，例如，\lstinline{echo hi | wc}不会产生输出，因为当\lstinline{echo hi}在\lstinline{runcmd}中退出时，内部进程退出，并且永远不会调用fork来运行管道的右端。这个不正确的行为可以通过在\lstinline{runcmd}中不为内部进程调用\lstinline{exit}来修复，但这个修复使代码复杂化：现在\lstinline{runcmd}需要知道它是否在内部进程中。当不为\lstinline{runcmd(p->right)}派生时也会出现复杂情况。例如，仅做此修改，\lstinline{sleep 10 | echo hi}将立即打印“hi”和一个新提示，而不是在10秒后；这是因为\lstinline{echo}立即运行并退出，不等待\lstinline{sleep}完成。
% 由于\lstinline{sh.c}的目标是尽可能简单，它不试图避免创建内部进程。

管道似乎并不比临时文件更强大：
管道\begin{lstlisting}[]
echo hello world | wc
\end{lstlisting}
可以不用管道实现为
\begin{lstlisting}[]
echo hello world >/tmp/xyz; wc </tmp/xyz
\end{lstlisting}
在这种情况下，管道至少比临时文件有三个优势。
首先，管道会自动清理自己；
使用文件重定向，shell必须小心在完成后删除\lstinline{/tmp/xyz}。
其次，管道可以传递任意长的数据流，而文件重定向需要在磁盘上有足够的可用空间来存储所有数据。
第三，管道允许管道阶段的并行执行，而文件方法要求第一个程序在第二个程序开始之前完成。
% 第四，如果您正在实现进程间通信，管道的阻塞读写比文件的非阻塞语义更有效。

%%
%%      文件系统
%%
\section{文件系统}

xv6文件系统提供数据文件，其中包含未解释的字节数组，以及目录，其中包含对数据文件和其他目录的命名引用。
目录形成一个树，从一个称为\indextext{根}的特殊目录开始。
像\lstinline{/a/b/c}这样的\indextext{路径}引用根目录\lstinline{/}中名为\lstinline{a}的目录中名为\lstinline{b}的目录中名为\lstinline{c}的文件或目录。
不以\lstinline{/}开头的路径是相对于调用进程的\indextext{当前目录}来评估的，该目录可以用\lstinline{chdir}系统调用来更改。
这两个代码片段打开相同的文件（假设所有涉及的目录都存在）：
\begin{lstlisting}[]
chdir("/a");
chdir("b");
open("c", O_RDONLY);
open("/a/b/c", O_RDONLY);
\end{lstlisting}
第一个片段将进程的当前目录更改为\lstinline{/a/b}；
第二个片段既不引用也不更改进程的当前目录。

有创建新文件和目录的系统调用：
\lstinline{mkdir}创建一个新目录，
带有\lstinline{O_CREATE}标志的\lstinline{open}创建一个新数据文件，
\lstinline{mknod}创建一个新设备文件。
这个例子说明了所有三种情况：
\begin{lstlisting}[]
mkdir("/dir");
fd = open("/dir/file", O_CREATE|O_WRONLY);
close(fd);
mknod("/console", 1, 1);
\end{lstlisting}
\lstinline{mknod}创建一个引用设备的特殊文件。
与设备文件关联的是主设备号和次设备号（\lstinline{mknod}的两个参数），它们唯一地标识一个内核设备。
当一个进程稍后打开一个设备文件时，内核会将\lstinline{read}和\lstinline{write}系统调用转移到内核设备实现，而不是将它们传递给文件系统。

一个文件的名称与文件本身是不同的；
同一个底层文件，称为\indextext{inode}，可以有多个名称，称为\indextext{链接}。
每个链接由一个目录中的一个条目组成；
该条目包含一个文件名和对一个inode的引用。
一个inode持有关于文件的\indextext{元数据}，包括
其类型（文件、目录或设备）、
其长度、
文件内容在磁盘上的位置，
以及文件的链接数。

\lstinline{fstat}系统调用从文件描述符引用的inode中检索信息。
它填充一个在\lstinline{stat.h} \fileref{kernel/stat.h}中定义的\lstinline{struct stat}：
\begin{lstlisting}[]
#define T_DIR     1   // Directory
#define T_FILE    2   // File
#define T_DEVICE  3   // Device
struct stat {
  int dev;     // File system's disk device
  uint ino;    // Inode number
  short type;  // Type of file
  short nlink; // Number of links to file
  uint64 size; // Size of file in bytes
};
\end{lstlisting}

\lstinline{link}系统调用创建另一个引用与现有文件相同inode的文件系统名称。
这个片段创建了一个名为\lstinline{a}和\lstinline{b}的新文件。
\begin{lstlisting}[]
open("a", O_CREATE|O_WRONLY);
link("a", "b");
\end{lstlisting}
从\lstinline{a}读取或写入与从\lstinline{b}读取或写入相同。
每个inode都由一个唯一的\textit{inode号}标识。
在上面的代码序列之后，可以通过检查\lstinline{fstat}的结果来确定\lstinline{a}和\lstinline{b}引用相同的基础内容：
两者都将返回相同的inode号（\lstinline{ino}），并且\lstinline{nlink}计数将设置为2。

\lstinline{unlink}系统调用从文件系统中删除一个名称。
只有当文件的链接计数为零且没有文件描述符引用它时，文件的inode和保存其内容的磁盘空间才会被释放。
因此，将\begin{lstlisting}[]
unlink("a");
\end{lstlisting}
添加到最后一个代码序列会使inode和文件内容可以作为\lstinline{b}访问。
此外，
\begin{lstlisting}[]
fd = open("/tmp/xyz", O_CREATE|O_RDWR);
unlink("/tmp/xyz");
\end{lstlisting}
是创建没有名称的临时inode的惯用方法，当进程关闭\lstinline{fd}或退出时，该inode将被清理。

Unix提供了可从shell调用的用户级程序作为文件实用程序，例如\lstinline{mkdir}、\lstinline{ln}和\lstinline{rm}。
这种设计允许任何人通过添加新的用户级程序来扩展命令行界面。事后看来，这个计划似乎是显而易见的，但在Unix时代设计的其他系统通常将这些命令内置到shell中（并将shell内置到内核中）。

一个例外是\lstinline{cd}，它内置在shell中\lineref{user/sh.c:/if.buf.0..==..c./}。
\lstinline{cd}必须更改shell本身的当前工作目录。如果\lstinline{cd}作为常规命令运行，那么shell将派生一个子进程，子进程将运行\lstinline{cd}，并且\lstinline{cd}将更改\textit{子进程}的工作目录。父进程（即shell）的工作目录不会改变。
%%
%%      现实世界
%%
\section{现实世界}

Unix的“标准”文件描述符、管道和方便的shell语法的组合，是编写通用可重用程序的一个重大进步。
这个想法激发了一种“软件工具”文化，这是Unix强大功能和普及的主要原因，而shell是第一个所谓的“脚本语言”。
Unix系统调用接口至今仍在BSD、Linux和macOS等系统中存在。

Unix系统调用接口已通过可移植操作系统接口（POSIX）标准进行了标准化。
Xv6\textit{不}符合POSIX标准：它缺少许多系统调用（包括像\lstinline{lseek}这样的基本调用），并且它提供的许多系统调用与标准不同。
我们对xv6的主要目标是简单和清晰，同时提供一个简单的类UNIX系统调用接口。
有几个人通过添加更多的系统调用和一个简单的C库来扩展xv6，以便运行基本的Unix程序。然而，现代内核提供的系统调用和内核服务种类比xv6多得多。例如，它们支持网络、窗口系统、用户级线程、许多设备的驱动程序等等。现代内核不断快速发展，并提供许多超出POSIX的功能。

Unix用一组文件名称和文件描述符接口统一了对多种类型资源（文件、目录和设备）的访问。
这个想法可以扩展到更多种类的资源；一个很好的例子是Plan 9\cite{Presotto91plan9}，它将“资源即文件”的概念应用于网络、图形等。
然而，大多数源自Unix的操作系统并没有走这条路。

文件系统和文件描述符是强大的抽象。
即便如此，还有其他操作系统接口的模型。
Multics是Unix的前身，它以一种使其看起来像内存的方式抽象文件存储，产生了一种截然不同的接口风格。
Multics设计的复杂性直接影响了Unix的设计者，他们的目标是构建更简单的东西。
% XXX 我们可以剪掉这个吗，因为它的观点和下一段相同？
% 一个几十年前过时但最近又回归的操作系统接口理念是虚拟机监视器。
% 这类系统提供了一个与xv6表面上不同的接口，但基本概念仍然相同：
% 虚拟机，像进程一样，由一些内存和一个或多个寄存器集组成；
% 虚拟机可以访问一个名为虚拟磁盘的大文件，而不是文件系统；
% 虚拟机使用虚拟网络设备而不是管道或文件来相互发送消息和与外界通信。

Xv6不提供用户或保护一个用户免受另一个用户侵害的概念；用Unix的术语来说，所有xv6进程都以root身份运行。

本书探讨了xv6如何实现其类Unix接口，但这些思想和概念不仅适用于Unix。
任何操作系统都必须将进程复用到基础硬件上，将进程相互隔离，并提供受控的进程间通信机制。
学习xv6之后，您应该能够看到其他更复杂的操作系统，并在这些系统中看到xv6的基本概念。

%%
\section{练习}
%%

\begin{enumerate}

\item 编写一个程序，使用UNIX系统调用在一对管道上（每个方向一个）在两个进程之间“乒乓”一个字节。测量程序的性能，以每秒交换次数为单位。

\end{enumerate}