% 谈谈初始页表的条件：
% 分页未开启，但虚拟地址大部分直接映射到物理地址，
% 这也是我们在创建第一个进程后开启分页时的样子。
% 提到为什么仍然有 SEG_UCODE/SEG_UDATA？
% 我们真的说过 %cs 的低两位是做什么的吗？
% 特别是它们与 PTE_U 的交互
% 关于为什么是 extern char[] 的旁注
\chapter{页表}
\label{CH:MEM}

页表是操作系统为每个进程提供其自己的私有地址空间和内存的最流行机制。页表决定了内存地址的含义，以及可以访问物理内存的哪些部分。它们允许 xv6 隔离不同进程的地址空间，并将它们复用到单个物理内存上。页表是一种流行的设计，因为它们提供了一个间接层，允许操作系统执行许多技巧。Xv6 执行了一些技巧：在多个地址空间中映射相同的内存（一个蹦床页），并用一个未映射的页面保护内核和用户栈。本章的其余部分解释了 RISC-V 硬件提供的页表以及 xv6 如何使用它们。

%%
\section{分页硬件}
%%
提醒一下，RISC-V 指令（用户和内核）操作的是虚拟地址。机器的 RAM 或物理内存，是使用物理地址索引的。RISC-V 页表硬件通过将每个虚拟地址映射到物理地址来连接这两种地址。

\begin{figure}[t]
\center
\includegraphics[scale=0.5]{fig/riscv_address.pdf}
\caption{RISC-V 虚拟和物理地址，带有一个简化的逻辑页表。}
\label{fig:riscv_address}
\end{figure}

Xv6 在 Sv39 RISC-V 上运行，这意味着 64 位虚拟地址中只有低 39 位被使用；高 25 位未使用。在这种 Sv39 配置中，RISC-V 页表在逻辑上是一个包含 $2^{27}$ (134,217,728) 个\indextext{页表条目 (PTE)} 的数组。每个 PTE 包含一个 44 位的物理页号 (PPN) 和一些标志。分页硬件通过使用 39 位中的高 27 位来索引页表以查找 PTE，并创建一个 56 位的物理地址，其高 44 位来自 PTE 中的 PPN，低 12 位从原始虚拟地址复制而来，从而转换虚拟地址。图~\ref{fig:riscv_address} 展示了这一过程，其中页表的逻辑视图是一个简单的 PTE 数组（更完整的故事请参见图~\ref{fig:riscv_pagetable}）。页表使操作系统能够以 4096 ($2^{12}$) 字节的对齐块（称为\indextext{页}）为粒度控制虚拟到物理地址的转换。

\begin{figure}[t]
\center
\includegraphics[scale=0.5]{fig/riscv_pagetable.pdf}
\caption{RISC-V 地址转换细节。}
\label{fig:riscv_pagetable}
\end{figure}

在 Sv39 RISC-V 中，虚拟地址的高 25 位不用于转换。物理地址也有增长空间：PTE 格式中有空间让物理页号再增长 10 位。RISC-V 的设计者基于技术预测选择了这些数字。$2^{39}$ 字节是 512 GB，这对于在 RISC-V 计算机上运行的应用程序来说应该是足够的地址空间。$2^{56}$ 的物理内存空间足以在近期内容纳许多 I/O 设备和 RAM 芯片。如果需要更多，RISC-V 设计者定义了具有 48 位虚拟地址的 Sv48~\cite{riscv:priv}。

如图~\ref{fig:riscv_pagetable} 所示，RISC-V CPU 分三步将虚拟地址转换为物理地址。页表以三级树的形式存储在物理内存中。树的根是一个 4096 字节的页表页，包含 512 个 PTE，这些 PTE 包含树中下一级页表页的物理地址。这些页中的每一个都包含 512 个 PTE，用于树的最后一级。分页硬件使用 27 位中的高 9 位在根页表页中选择一个 PTE，中间 9 位在树的下一级页表页中选择一个 PTE，低 9 位选择最终的 PTE。（在 Sv48 RISC-V 中，页表有四个级别，虚拟地址的第 39 到 47 位索引到顶层。）

如果转换地址所需的三个 PTE 中有任何一个不存在，分页硬件会引发一个\indextext{缺页异常}，由内核来处理该异常（参见第~\ref{CH:TRAP}章）。

图~\ref{fig:riscv_pagetable} 的三级结构与图~\ref{fig:riscv_address} 的单级设计相比，提供了一种内存高效的记录 PTE 的方式。在大量虚拟地址没有映射的常见情况下，三级结构可以省略整个页目录。例如，如果一个应用程序只使用从地址零开始的几个页面，那么顶级页目录的条目 1 到 511 都是无效的，内核就不必为这 511 个中间页目录分配页面。此外，内核也不必为这 511 个中间页目录的底层页目录分配页面。因此，在这个例子中，三级设计节省了 511 个中间页目录的页面和 $511\times512$ 个底层页目录的页面。

尽管 CPU 在硬件中遍历三级结构作为执行加载或存储指令的一部分，但三级的一个潜在缺点是 CPU 必须从内存加载三个 PTE 来执行加载/存储指令中虚拟地址到物理地址的转换。为了避免从物理内存加载 PTE 的成本，RISC-V CPU 在一个\indextext{翻译后备缓冲区 (TLB)} 中缓存页表条目。

每个 PTE 包含一些标志位，告诉分页硬件关联的虚拟地址允许如何使用。\indexcode{PTE_V} 指示 PTE 是否存在：如果未设置，引用该页会导致异常（即不允许）。\indexcode{PTE_R} 控制指令是否允许读取该页。\indexcode{PTE_W} 控制指令是否允许写入该页。\indexcode{PTE_X} 控制 CPU 是否可以将该页的内容解释为指令并执行它们。\indexcode{PTE_U} 控制用户模式下的指令是否允许访问该页；如果 \indexcode{PTE_U} 未设置，则该 PTE 只能在 supervisor 模式下使用。图~\ref{fig:riscv_pagetable} 展示了这一切是如何工作的。标志和所有其他与分页硬件相关的结构都在 \fileref{kernel/riscv.h} 中定义。

为了告诉 CPU 使用一个页表，内核必须将根页表页的物理地址写入 \texttt{satp}\index{satp@\lstinline{satp}} 寄存器。CPU 将使用其自己的 \texttt{satp} 指向的页表来转换后续指令生成的所有地址。每个 CPU 都有自己的 \texttt{satp}，这样不同的 CPU 就可以运行不同的进程，每个进程都有由其自己的页表描述的私有地址空间。

从内核的角度来看，页表是存储在内存中的数据，内核使用类似于任何树形数据结构的代码来创建和修改页表。

关于本书中使用的一些术语的说明。\textit{物理内存} 指的是 RAM 中的存储单元。物理内存的一个字节有一个地址，称为\textit{物理地址}。解引用地址的指令（如加载、存储、跳转和函数调用）只使用虚拟地址，分页硬件将其转换为物理地址，然后发送到 RAM 硬件以读取或写入存储。一个\textit{地址空间} 是在给定页表中有效的一组虚拟地址；每个 xv6 进程都有一个独立的的用户地址空间，xv6 内核也有自己的地址空间。\textit{用户内存} 指的是进程的用户地址空间加上页表允许进程访问的物理内存。\textit{虚拟内存} 指的是与管理页表和使用它们实现隔离等目标相关的思想和技术。

\begin{figure}[h]
\centering
 \includegraphics[scale=0.65]{fig/xv6_layout.pdf}
\caption{左侧是 xv6 的内核地址空间。{\sf \small{RWX}} 指的是 PTE 的读、写和执行权限。右侧是 xv6 期望看到的 RISC-V 物理地址空间。}
\label{fig:xv6_layout}
\end{figure}

%%
\section{内核地址空间}
%%
Xv6 为每个进程维护一个页表，描述每个进程的用户地址空间，外加一个描述内核地址空间的单一页表。内核配置其地址空间的布局，以便在可预测的虚拟地址上访问物理内存和各种硬件资源。图~\ref{fig:xv6_layout} 展示了这种布局如何将内核虚拟地址映射到物理地址。文件 \fileref{kernel/memlayout.h} 声明了 xv6 内核内存布局的常量。

QEMU 模拟了一台计算机，其 RAM（物理内存）从物理地址 \texttt{0x80000000} 开始，至少持续到 \texttt{0x88000000}，xv6 称之为 \texttt{PHYSTOP}。QEMU 模拟还包括 I/O 设备，如磁盘接口。QEMU 将设备接口作为\indextext{内存映射}的控制寄存器暴露给软件，这些寄存器位于物理地址空间中 \texttt{0x80000000} 以下。内核可以通过读/写这些特殊的物理地址与设备交互；这些读写操作是与设备硬件通信，而不是与 RAM 通信。第~\ref{CH:TRAP}章解释了 xv6 如何与设备交互。

内核使用“直接映射”来访问 RAM 和内存映射的设备寄存器；也就是说，将资源映射到与物理地址相等的虚拟地址。例如，内核本身位于虚拟地址空间和物理内存中的 \lstinline{KERNBASE=0x80000000}。直接映射简化了读写物理内存的内核代码。例如，当 \lstinline{fork} 为子进程分配用户内存时，分配器返回该内存的物理地址；\lstinline{fork} 在将父进程的用户内存复制给子进程时，直接使用该地址作为虚拟地址。

有几个内核虚拟地址不是直接映射的：

\begin{itemize}

\item 蹦床页。它被映射在虚拟地址空间的顶部；用户页表也有这个相同的映射。第~\ref{CH:TRAP}章讨论了蹦床页的作用，但我们在这里看到了页表的一个有趣用例；一个物理页（包含蹦床代码）在内核的虚拟地址空间中被映射了两次：一次在虚拟地址空间的顶部，一次是直接映射。

\item 内核栈页。每个进程都有自己的内核栈，它被映射在高地址，以便 xv6 可以在其下方留下一个未映射的\indextext{保护页}。保护页的 PTE 是无效的（即 \lstinline{PTE_V} 未设置），因此如果内核溢出一个内核栈，它很可能会导致异常，内核会恐慌。没有保护页，溢出的栈会覆盖其他内核内存，导致不正确的操作。恐慌崩溃是更可取的。

\end{itemize}

虽然内核通过高内存映射使用其栈，但它们也可以通过直接映射的地址被内核访问。另一种设计可能只有直接映射，并在直接映射的地址上使用栈。然而，在这种安排中，提供保护页将涉及取消映射那些否则会引用物理内存的虚拟地址，而这些物理内存之后将难以使用。

内核使用权限 \lstinline{PTE_R} 和 \lstinline{PTE_X} 映射蹦床和内核文本的页面。内核从这些页面读取和执行指令。内核使用权限 \lstinline{PTE_R} 和 \lstinline{PTE_W} 映射其他页面，以便它可以读写这些页面中的内存。保护页的映射是无效的。

%%
\section{代码：创建地址空间}
%%

大多数用于操作地址空间和页表的 xv6 代码位于 {\tt vm.c} \lineref{kernel/vm.c:1} 中。核心数据结构是 {\tt pagetable\_t}，它实际上是一个指向 RISC-V 根页表页的指针；一个 {\tt pagetable\_t} 可以是内核页表，也可以是每个进程的页表之一。核心函数是 {\tt walk}，它查找虚拟地址的 PTE，和 {\tt mappages}，它为新映射安装 PTE。以 {\tt kvm} 开头的函数操作内核页表；以 {\tt uvm} 开头的函数操作用户页表；其他函数则两者都用。{\tt copyout} 和 {\tt copyin} 将数据复制到或从来自由系统调用参数提供的用户虚拟地址；它们在 {\tt vm.c} 中，因为它们需要显式地转换这些地址以找到相应的物理内存。

在引导序列的早期，\indexcode{main} 调用 \indexcode{kvminit} \lineref{kernel/vm.c:/^kvminit/}，使用 \indexcode{kvmmake} \lineref{kernel/vm.c:/^kvmmake/} 创建内核的页表。这个调用发生在 xv6 在 RISC-V 上启用分页之前，所以地址直接指向物理内存。\lstinline{kvmmake} 首先分配一页物理内存来存放根页表页。然后它调用 \indexcode{kvmmap} 来安装内核需要的转换。这些转换包括内核的指令和数据、直到 \indexcode{PHYSTOP} 的物理内存，以及实际上是设备的内存范围。\indexcode{proc_mapstacks} \lineref{kernel/proc.c:/^proc_mapstacks/} 为每个进程分配一个内核栈。它调用 \lstinline{kvmmap} 将每个栈映射到由 \lstinline{KSTACK} 生成的虚拟地址，这为无效的栈保护页留出了空间。

\indexcode{kvmmap} \lineref{kernel/vm.c:/^kvmmap/} 调用 \indexcode{mappages} \lineref{kernel/vm.c:/^mappages/}，它将一个虚拟地址范围到相应物理地址范围的映射安装到一个页表中。它对范围内的每个虚拟地址，以页为间隔分别执行此操作。对于要映射的每个虚拟地址，\lstinline{mappages} 调用 \indexcode{walk} 来查找该地址的 PTE 地址。然后它初始化 PTE 以保存相关的物理页号、所需的权限（\lstinline{PTE_W}、\lstinline{PTE_X} 和/或 \lstinline{PTE_R}）和 \lstinline{PTE_V} 以将 PTE 标记为有效 \lineref{kernel/vm.c:/perm...PTE_V/}。

\indexcode{walk} \lineref{kernel/vm.c:/^walk/} 模仿 RISC-V 分页硬件查找虚拟地址的 PTE 的过程（参见图~\ref{fig:riscv_pagetable}）。\lstinline{walk} 一次下降一级页表，使用每一级的 9 位虚拟地址来索引相关的页目录页。在每一级，它要么找到下一级页目录页的 PTE，要么找到最终页的 PTE \lineref{kernel/vm.c:/pte.=..pagetable/}。如果第一或第二级页目录页中的 PTE 无效，则所需的目录页尚未分配；如果设置了 \lstinline{alloc} 参数，\lstinline{walk} 会分配一个新的页表页，并将其物理地址放入 PTE 中。它返回树中最底层 PTE 的地址 \lineref{kernel/vm.c:/return..pagetable/}。

上述代码依赖于物理内存被直接映射到内核虚拟地址空间。例如，当 \lstinline{walk} 下降页表的级别时，它从一个 PTE \lineref{kernel/vm.c:/pagetable.=..pa.*E2P/} 中提取下一级页表的（物理）地址，然后使用该地址作为虚拟地址来获取下一级的 PTE \lineref{kernel/vm.c:/t..pte.=..paget/}。

\indexcode{main} 调用 \indexcode{kvminithart} \lineref{kernel/vm.c:/^kvminithart/} 来安装内核页表。它将根页表页的物理地址写入 \texttt{satp} 寄存器。此后，CPU 将使用内核页表转换地址。由于内核使用直接映射，下一条指令的现在的虚拟地址将映射到正确的物理内存地址。

每个 RISC-V CPU 都在一个\indextext{翻译后备缓冲区 (TLB)} 中缓存页表条目，当 xv6 更改页表时，它必须告诉 CPU 使相应的缓存 TLB 条目无效。如果不这样做，那么在稍后的某个时间点，TLB 可能会使用一个旧的缓存映射，指向一个在此期间已分配给另一个进程的物理页，结果，一个进程可能会在另一个进程的内存上乱写。RISC-V 有一个指令 \indexcode{sfence.vma}，可以刷新当前 CPU 的 TLB。Xv6 在 \texttt{kvminithart} 中重新加载 \texttt{satp} 寄存器后，以及在切换到用户页表返回用户空间之前的蹦床代码中执行 {\tt sfence.vma} \lineref{kernel/trampoline.S:/sfence.vma/}。

在更改 \texttt{satp} 之前，也有必要发出 \texttt{sfence.vma}，以等待所有未完成的加载和存储完成。这种等待确保了对页表的先前更新已经完成，并确保了先前的加载和存储使用旧的页表，而不是新的页表。

为了避免刷新完整的 TLB，RISC-V CPU 可能支持地址空间标识符 (ASID)~\cite{riscv:priv}。然后，内核可以只刷新特定地址空间的 TLB 条目。Xv6 不使用此功能。

\section{物理内存分配}

内核必须在运行时为页表、用户内存、内核栈和管道缓冲区分配和释放物理内存。

Xv6 使用内核末尾和 \indexcode{PHYSTOP} 之间的物理内存进行运行时分配。它一次分配和释放整个 4096 字节的页面。它通过在页面本身中穿插一个链表来跟踪哪些页面是空闲的。分配包括从链表中删除一个页面；释放包括将被释放的页面添加到列表中。
%%
\section{代码：物理内存分配器}
%%

分配器位于 {\tt kalloc.c} \lineref{kernel/kalloc.c:1} 中。分配器的数据结构是一个可供分配的物理内存页面的\textit{空闲列表}。每个空闲页的列表元素是一个 \indexcode{struct run} \lineref{kernel/kalloc.c:/^struct.run/}。分配器从哪里获得内存来存放该数据结构？它将每个空闲页的 \lstinline{run} 结构存储在空闲页本身中，因为那里没有存储其他东西。空闲列表由一个自旋锁 \linerefs{kernel/kalloc.c:/^struct.{/,/}/} 保护。列表和锁被包装在一个结构中，以明确锁保护结构中的字段。现在，忽略锁和对 \lstinline{acquire} 和 \lstinline{release} 的调用；第~\ref{CH:LOCK}章将详细研究锁定。

函数 \indexcode{main} 调用 \indexcode{kinit} 来初始化分配器 \lineref{kernel/kalloc.c:/^kinit/}。\lstinline{kinit} 初始化空闲列表以容纳内核末尾和 {\tt PHYSTOP} 之间的每一页。Xv6 应该通过解析硬件提供的配置信息来确定有多少物理内存可用。相反，xv6 假设机器有 128 兆字节的 RAM。\lstinline{kinit} 调用 \indexcode{freerange}，通过对每页调用 \indexcode{kfree} 将内存添加到空闲列表中。一个 PTE 只能引用一个在 4096 字节边界上对齐的物理地址（是 4096 的倍数），所以 \lstinline{freerange} 使用 \indexcode{PGROUNDUP} 来确保它只释放对齐的物理地址。分配器开始时没有内存；这些对 \lstinline{kfree} 的调用给了它一些来管理。

分配器有时将地址视为整数以对其执行算术运算（例如，在 \lstinline{freerange} 中遍历所有页面），有时使用地址作为指针来读写内存（例如，操作存储在每个页面中的 \lstinline{run} 结构）；这种地址的双重用途是分配器代码充满 C 类型转换的主要原因。\index{type cast}

函数 \lstinline{kfree} \lineref{kernel/kalloc.c:/^kfree/} 首先将被释放的内存中的每个字节设置为值 1。这将导致在释放后使用内存的代码（使用“悬空引用”）读取垃圾而不是旧的有效内容；希望这会使此类代码更快地崩溃。然后 \lstinline{kfree} 将页面前置到空闲列表中：它将 \lstinline{pa} 转换为指向 \lstinline{struct} \lstinline{run} 的指针，将旧的空闲列表的开头记录在 \lstinline{r->next} 中，并将空闲列表设置为 \lstinline{r}。\indexcode{kalloc} 删除并返回空闲列表中的第一个元素。

\section{进程地址空间}

每个进程都有自己的页表，当 xv6 在进程之间切换时，它也会更改页表。图~\ref{fig:processlayout} 比图~\ref{fig:as} 更详细地显示了进程的地址空间。进程的用户内存从虚拟地址零开始，可以增长到 \texttt{MAXVA} \lineref{kernel/riscv.h:/MAXVA/}，原则上允许一个进程寻址 256 GB 的内存。

进程的地址空间由包含程序文本的页面（xv6 使用 \lstinline{PTE_R}、\lstinline{PTE_X} 和 \lstinline{PTE_U} 权限映射）、包含程序预初始化数据的页面、一个用于栈的页面和用于堆的页面组成。Xv6 使用 \lstinline{PTE_R}、\lstinline{PTE_W} 和 \lstinline{PTE_U} 权限映射数据、栈和堆。

在用户地址空间内使用权限是加固用户进程的常用技术。如果文本是用 \lstinline{PTE_W} 映射的，那么进程可能会意外地修改自己的程序；例如，一个编程错误可能导致程序写入一个空指针，修改地址 0 处的指令，然后继续运行，可能会造成更大的破坏。为了立即检测到此类错误，xv6 映射文本时不带 \lstinline{PTE_W}；如果程序意外尝试存储到地址 0，硬件将拒绝执行该存储并引发一个缺页（见第~\ref{sec:pagefaults}节）。然后内核杀死该进程并打印一条信息性消息，以便开发人员可以追查问题。

同样，通过不带 \lstinline{PTE_X} 映射数据，用户程序不能意外地跳转到程序数据中的地址并从该地址开始执行。

在现实世界中，通过仔细设置权限来加固进程也有助于防御安全攻击。攻击者可能会向程序（例如，Web 服务器）提供精心构造的输入，以触发程序中的一个错误，希望将该错误转化为一个漏洞利用~\cite{aleph:smashing}。仔细设置权限和其他技术，例如随机化用户地址空间的布局，使此类攻击更加困难。

栈是一个单独的页面，并显示了由 exec 创建的初始内容。包含命令行参数的字符串，以及指向它们的指针数组，位于栈的顶端。紧随其后的是允许程序从 \lstinline{main} 开始的值，就好像函数 \lstinline{main(argc}, \lstinline{argv)} 刚刚被调用一样。

为了检测用户栈溢出分配的栈内存，xv6 在栈的正下方放置了一个不可访问的保护页，方法是清除 \lstinline{PTE_U} 标志。如果用户栈溢出并且进程试图使用栈下方的地址，硬件将生成一个缺页异常，因为保护页对于在用户模式下运行的程序是不可访问的。一个现实世界的操作系统可能会在用户栈溢出时自动分配更多的内存。

当一个进程向 xv6 请求更多用户内存时，xv6 会增长进程的堆。Xv6 首先使用 {\tt kalloc} 分配物理页面。然后它向进程的页表添加指向新物理页面的 PTE。Xv6 在这些 PTE 中设置 \lstinline{PTE_W}、\lstinline{PTE_R}、\lstinline{PTE_U} 和 \lstinline{PTE_V} 标志。大多数进程不使用整个用户地址空间；xv6 在未使用的 PTE 中将 \lstinline{PTE_V} 保持清除。

我们在这里看到了一些页表使用的好例子。首先，不同进程的页表将用户地址转换为不同的物理内存页面，因此每个进程都有私有的用户内存。其次，每个进程都看到其内存具有从零开始的连续虚拟地址，而进程的物理内存可以是不连续的。第三，内核在用户地址空间的顶部映射一个带有蹦床代码的页面（不带 \lstinline{PTE_U}），因此一个物理内存页面出现在所有地址空间中，但只能由内核使用。

\begin{figure}[t]
\center
\includegraphics[scale=0.5]{fig/processlayout.png}
\caption{一个进程的用户地址空间，及其初始栈。}
\label{fig:processlayout}
\end{figure}

\section{代码：sbrk}

\lstinline{sbrk} 是一个进程用来收缩或增长其内存的系统调用。该系统调用由函数 \lstinline{growproc} \lineref{kernel/proc.c:/^growproc/} 实现。\lstinline{growproc} 根据 \lstinline{n} 是正数还是负数调用 \lstinline{uvmalloc} 或 \lstinline{uvmdealloc}。\lstinline{uvmalloc} \lineref{kernel/vm.c:/^uvmalloc/} 使用 {\tt kalloc} 分配物理内存，将分配的内存清零，并使用 {\tt mappages} 向用户页表添加 PTE。\lstinline{uvmdealloc} 调用 {\tt uvmunmap} \lineref{kernel/vm.c:/^uvmunmap/}，它使用 {\tt walk} 查找 PTE 并使用 {\tt kfree} 释放它们引用的物理内存。

Xv6 不仅使用进程的页表来告诉硬件如何映射用户虚拟地址，而且还将其作为分配给该进程的物理内存页面的唯一记录。这就是为什么释放用户内存（在 {\tt uvmunmap} 中）需要检查用户页表的原因。

%%
\section{代码：exec}
%%
\lstinline{exec} 是一个系统调用，它用从文件中读取的数据（称为二进制文件或可执行文件）替换进程的用户地址空间。二进制文件通常是编译器和链接器的输出，包含机器指令和程序数据。\lstinline{exec} \lineref{kernel/exec.c:/^exec/} 使用 \indexcode{namei} \lineref{kernel/exec.c:/namei/} 打开名为 \lstinline{path} 的二进制文件，这在第~\ref{CH:FS}章中有解释。然后，它读取 ELF 头。Xv6 二进制文件采用广泛使用的\indextext{ELF 格式}进行格式化，该格式在 \fileref{kernel/elf.h} 中定义。一个 ELF 二进制文件由一个 ELF 头 \indexcode{struct elfhdr} \lineref{kernel/elf.h:/^struct.elfhdr/}，后跟一系列程序段头 \lstinline{struct proghdr} \lineref{kernel/elf.h:/^struct.proghdr/} 组成。每个 \lstinline{progvhdr} 描述了必须加载到内存中的应用程序的一个段；xv6 程序有两个程序段头：一个用于指令，一个用于数据。

第一步是快速检查文件是否可能包含 ELF 二进制文件。一个 ELF 二进制文件以四字节的“魔数” \lstinline{0x7F}、\lstinline{`E'}、\lstinline{`L'}、\lstinline{`F'} 或 \indexcode{ELF_MAGIC} \lineref{kernel/elf.h:/ELF_MAGIC/} 开头。如果 ELF 头有正确的魔数，\lstinline{exec} 就假定该二进制文件格式正确。

\lstinline{exec} 使用 \indexcode{proc_pagetable} \lineref{kernel/exec.c:/proc_pagetable/} 分配一个没有用户映射的新页表，使用 \indexcode{uvmalloc} \lineref{kernel/exec.c:/uvmalloc/} 为每个 ELF 段分配内存，并使用 \indexcode{loadseg} \lineref{kernel/exec.c:/loadseg/} 将每个段加载到内存中。\lstinline{loadseg} 使用 \indexcode{walkaddr} 查找分配的内存的物理地址，以写入 ELF 段的每一页，并使用 \indexcode{readi} 从文件中读取。

\indexcode{/init} 的程序段头，即用 \lstinline{exec} 创建的第一个用户程序，如下所示：
\begin{footnotesize}
\begin{verbatim}
# objdump -p user/_init

user/_init:     file format elf64-little

Program Header:
0x70000003 off    0x0000000000006bb0 vaddr 0x0000000000000000
                                       paddr 0x0000000000000000 align 2**0
         filesz 0x000000000000004a memsz 0x0000000000000000 flags r--
    LOAD off    0x0000000000001000 vaddr 0x0000000000000000
                                       paddr 0x0000000000000000 align 2**12
         filesz 0x0000000000001000 memsz 0x0000000000001000 flags r-x
    LOAD off    0x0000000000002000 vaddr 0x0000000000001000
                                       paddr 0x0000000000001000 align 2**12
         filesz 0x0000000000000010 memsz 0x0000000000000030 flags rw-
   STACK off    0x0000000000000000 vaddr 0x0000000000000000
                                       paddr 0x0000000000000000 align 2**4
         filesz 0x0000000000000000 memsz 0x0000000000000000 flags rw-
\end{verbatim}
\end{footnotesize}

我们看到文本段应该加载到内存中的虚拟地址 0x0（没有写权限），其内容来自文件中的偏移量 0x1000。我们还看到数据应该加载到地址 0x1000，这是一个页边界，并且没有执行权限。

程序段头的 \lstinline{filesz} 可能小于 \lstinline{memsz}，这表明它们之间的差距应该用零填充（对于 C 全局变量），而不是从文件中读取。对于 \lstinline{/init}，数据 \lstinline{filesz} 是 0x10 字节，\lstinline{memsz} 是 0x30 字节，因此 \indexcode{uvmalloc} 分配了足够的物理内存来容纳 0x30 字节，但只从文件 \lstinline{/init} 中读取 0x10 字节。

现在 \indexcode{exec} 分配并初始化用户栈。它只分配一个栈页。\lstinline{exec} 一次一个地将参数字符串复制到栈顶，并将指向它们的指针记录在 \indexcode{ustack} 中。它在将传递给 \lstinline{main} 的 \indexcode{argv} 列表的末尾放置一个空指针。值 \indexcode{argc} 和 \lstinline{argv} 通过系统调用返回路径传递给 \lstinline{main}：\lstinline{argc} 通过系统调用返回值传递，该返回值进入 {\tt a0}，\lstinline{argv} 通过进程的陷阱帧的 {\tt a1} 条目传递。

\lstinline{exec} 在栈页的正下方放置一个不可访问的页面，这样试图使用多于一个页面的程序就会出错。这个不可访问的页面也允许 \lstinline{exec} 处理过大的参数；在这种情况下，\lstinline{exec} 用来将参数复制到栈的 \indexcode{copyout} \lineref{kernel/vm.c:/^copyout/} 函数会注意到目标页面不可访问，并返回 -1。

在准备新内存映像期间，如果 \lstinline{exec} 检测到错误，如无效的程序段，它会跳转到标签 \lstinline{bad}，释放新映像，并返回 -1。\lstinline{exec} 必须等到它确定系统调用会成功后才能释放旧映像：如果旧映像不见了，系统调用就无法向其返回 -1。\lstinline{exec} 中唯一的错误情况发生在创建映像期间。一旦映像完成，\lstinline{exec} 就可以提交到新页表 \lineref{kernel/exec.c:/pagetable.=.pagetable/} 并释放旧页表 \lineref{kernel/exec.c:/proc_freepagetable/}。

\lstinline{exec} 将字节从 ELF 文件加载到由 ELF 文件指定的地址的内存中。用户或进程可以将任何他们想要的地址放入 ELF 文件中。因此 \lstinline{exec} 是有风险的，因为 ELF 文件中的地址可能会意外地或故意地引用内核。对于一个不警惕的内核来说，后果可能从崩溃到恶意颠覆内核的隔离机制（即安全漏洞利用）。Xv6 执行了许多检查来避免这些风险。例如 \lstinline{if(ph.vaddr + ph.memsz < ph.vaddr)} 检查和是否溢出一个 64 位整数。危险在于用户可以构造一个 ELF 二进制文件，其中 \lstinline{ph.vaddr} 指向一个用户选择的地址，而 \lstinline{ph.memsz} 足够大，以至于和溢出到 0x1000，这看起来像一个有效值。在旧版本的 xv6 中，用户地址空间也包含内核（但在用户模式下不可读/写），用户可以选择一个对应于内核内存的地址，从而将数据从 ELF 二进制文件复制到内核中。在 RISC-V 版本的 xv6 中，这不会发生，因为内核有自己独立的页表；\lstinline{loadseg} 加载到进程的页表中，而不是内核的页表中。

内核开发人员很容易忽略一个关键的检查，而现实世界的内核有很长的历史，都存在着被用户程序利用以获取内核权限的缺失检查。xv6 很可能没有完全验证提供给内核的用户级数据，恶意用户程序可能会利用这一点来规避 xv6 的隔离。
%%
\section{现实世界}
%%

像大多数操作系统一样，xv6 使用分页硬件进行内存保护和映射。大多数操作系统通过结合分页和缺页异常，对分页的使用比 xv6 复杂得多，我们将在第~\ref{CH:TRAP}章中讨论。

Xv6 因为内核使用虚拟地址和物理地址之间的直接映射，以及它假设在地址 0x80000000 处有物理 RAM（内核期望被加载到那里）而得以简化。这在 QEMU 中可行，但在真实硬件上却是个坏主意；真实硬件将 RAM 和设备放置在不可预测的物理地址，因此（例如）在 0x80000000 可能没有 RAM，而 xv6 期望能够存储内核。更严肃的内核设计利用页表将任意硬件物理内存布局转换为可预测的内核虚拟地址布局。

RISC-V 支持物理地址级别的保护，但 xv6 没有使用该功能。

在拥有大量内存的机器上，使用 RISC-V 对“超级页”的支持可能是有意义的。当物理内存较小时，小页面是有意义的，以便以细粒度进行分配和页面换出到磁盘。例如，如果一个程序只使用 8 千字节的内存，给它一个完整的 4 兆字节的超级页物理内存是浪费的。在拥有大量 RAM 的机器上，较大的页面更有意义，并且可以减少页表操作的开销。

xv6 内核缺乏一个类似 {\tt malloc} 的分配器，可以为小对象提供内存，这使得内核无法使用需要动态分配的复杂数据结构。一个更复杂的内核可能会分配许多不同大小的小块，而不是（像在 xv6 中那样）只分配 4096 字节的块；一个真正的内核分配器需要处理小分配和大的分配。

内存分配是一个常年热门的话题，基本问题是有效利用有限的内存和为未知的未来请求做准备~\cite{knuth}。如今，人们更关心速度而不是空间效率。
%%
\section{练习}
%%

\begin{enumerate}

\item 解析 RISC-V 的设备树以找出计算机拥有的物理内存量。

\item 编写一个用户程序，通过调用 \lstinline{sbrk(1)} 将其地址空间增加一个字节。运行该程序，并在调用 \lstinline{sbrk} 之前和之后调查程序的页表。内核分配了多少空间？新内存的 PTE 包含什么？

\item 修改 xv6 以便为内核使用超级页。

\item Unix 的 \lstinline{exec} 实现传统上包括对 shell 脚本的特殊处理。如果待执行文件的开头是文本 \lstinline{#!}，那么第一行就被视为运行以解释该文件的程序。例如，如果调用 \lstinline{exec} 来运行 \lstinline{myprog} \lstinline{arg1}，并且 \lstinline{myprog} 的第一行是 \lstinline{#!/interp}，那么 \lstinline{exec} 就会用命令行 \lstinline{/interp} \lstinline{myprog} \lstinline{arg1} 来运行 \lstinline{/interp}。在 xv6 中实现对此约定的支持。

\item 为内核实现地址空间布局随机化。

\end{enumerate}