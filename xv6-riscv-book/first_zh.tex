%   术语:
%     进程: 指在用户空间中的执行，或者可能指 struct proc 等
%     进程内存: 地址空间的低地址部分
%     进程有一个线程，带有两个栈 (一个用于内核模式，一个用于用户模式)
%     讨论一下初始页表条件:
%     分页未开启，但虚拟地址大部分直接映射到物理地址，
%     这也是我们开启分页后情况的样子，
%     因为分页是在我们创建第一个进程后才开启的。
%   提到为什么仍然有 SEG_UCODE/SEG_UDATA?
%   我们是否曾真正说过 %cs 的低两位是做什么的？
%     特别是它们与 PTE_U 的交互
%   关于为什么是 extern char[] 的旁注

\chapter{操作系统组织}
\label{CH:FIRST}

操作系统的关键要求是同时支持多种活动。例如，使用第~\ref{CH:UNIX}章中描述的系统调用接口，一个进程可以用 \lstinline{fork} 启动新进程。操作系统必须在这些进程之间\indextext{分时共享}计算机的资源。例如，即使进程数量多于硬件CPU的数量，操作系统也必须确保所有进程都有机会执行。操作系统还必须为进程之间安排\indextext{隔离}。也就是说，如果一个进程有bug并发生故障，它不应该影响不依赖于该有bug进程的进程。然而，完全隔离又太强了，因为进程应该能够有意地进行交互；管道就是一个例子。因此，操作系统必须满足三个要求：多路复用、隔离和交互。

本章概述了操作系统如何组织以实现这三个要求。事实证明，有很多方法可以做到这一点，但本文侧重于围绕\indextext{单体内核}的主流设计，许多Unix操作系统都使用这种设计。本章还概述了xv6进程（xv6中的隔离单元）以及xv6启动时第一个进程的创建。

Xv6运行在\indextext{多核}\footnote{
本文中的“多核”指的是共享内存但并行执行的多个CPU，每个CPU都有自己的一组寄存器。本文有时使用术语\indextext{多处理器}作为多核的同义词，尽管多处理器也可以更具体地指具有多个不同处理器芯片的计算机。}RISC-V微处理器上，其许多底层功能（例如，其进程实现）都特定于RISC-V。RISC-V是一个64位CPU，xv6是用“LP64” C语言编写的，这意味着C编程语言中的long（L）和指针（P）是64位的，但一个{\tt int}是32位的。本书假定读者在某种架构上做过一些机器级编程，并将随着RISC-V特定思想的出现而介绍它们。用户级ISA~\cite{riscv:user}和特权架构~\cite{riscv:priv}文档是完整的规范。您也可以参考《RISC-V读本：一本开放架构地图集》~\cite{riscv}。

一台完整计算机中的CPU被支持硬件所包围，其中大部分是I/O接口的形式。Xv6是为qemu的“-machine virt”选项所模拟的支持硬件编写的。这包括RAM、一个包含引导代码的ROM、一个与用户键盘/屏幕的串行连接，以及一个用于存储的磁盘。

%%
\section{抽象物理资源}
%%

当遇到操作系统时，人们可能问的第一个问题是为什么要有它？也就是说，人们可以把图~\ref{fig:api}中的系统调用实现为一个库，应用程序与之链接。在这个方案中，每个应用程序甚至可以有自己根据其需求量身定制的库。应用程序可以直接与硬件资源交互，并以最适合应用程序的方式使用这些资源（例如，为了获得高或可预测的性能）。一些用于嵌入式设备或实时系统的操作系统就是以这种方式组织的。

这种库方法的缺点是，如果有多个应用程序在运行，这些应用程序必须是行为良好的。例如，每个应用程序必须定期放弃CPU，以便其他应用程序可以运行。如果所有应用程序都相互信任并且没有bug，那么这种\textit{协作式}分时方案可能是可以的。但更典型的情况是应用程序互不信任，并且有bug，所以人们通常希望有比协作式方案提供的更强的隔离。

为了实现强隔离，禁止应用程序直接访问敏感的硬件资源，而是将资源抽象成服务是有帮助的。例如，Unix应用程序仅通过文件系统的\lstinline{open}、\lstinline{read}、\lstinline{write}和\lstinline{close}系统调用与存储交互，而不是直接读写磁盘。这为应用程序提供了路径名的便利，并且允许操作系统（作为接口的实现者）来管理磁盘。即使隔离不是一个问题，有意交互（或只是希望互不干扰）的程序也可能会发现文件系统是比直接使用磁盘更方便的抽象。

同样，Unix在进程之间透明地切换硬件CPU，根据需要保存和恢复寄存器状态，这样应用程序就不必意识到分时。这种透明性使得操作系统即使在某些应用程序处于无限循环中时也能共享CPU。

再举一个例子，Unix进程使用\lstinline{exec}来建立它们的内存映像，而不是直接与物理内存交互。这使得操作系统可以决定将进程放在内存的哪个位置；如果内存紧张，操作系统甚至可能将进程的一部分数据存储在磁盘上。\lstinline{exec}还为用户提供了使用文件系统存储可执行程序映像的便利。

Unix进程之间的许多交互形式都是通过文件描述符进行的。文件描述符不仅抽象掉了许多细节（例如，管道或文件中的数据存储在哪里），而且它们的定义方式也简化了交互。例如，如果一个管道中的一个应用程序失败了，内核会为管道中的下一个进程生成一个文件结束信号。

图~\ref{fig:api}中的系统调用接口经过精心设计，既提供了程序员的便利性，也提供了强隔离的可能性。Unix接口不是抽象资源的唯一方式，但它已被证明是一种很好的方式。

%%
\section{用户模式、监管者模式和系统调用}
%%

强隔离要求在应用程序和操作系统之间有一个硬边界。如果应用程序出错，我们不希望操作系统失败或其他应用程序失败。相反，操作系统应该能够清理失败的应用程序并继续运行其他应用程序。为了实现强隔离，操作系统必须安排应用程序不能修改（甚至读取）操作系统的数据结构和指令，并且应用程序不能访问其他进程的内存。

CPU为强隔离提供了硬件支持。例如，RISC-V有三种CPU可以执行指令的模式：\indextext{机器模式}、\indextext{监管者模式}和\indextext{用户模式}。在机器模式下执行的指令拥有完全的特权；CPU在机器模式下启动。机器模式主要用于在引导期间设置计算机。Xv6在机器模式下执行几行代码，然后切换到监管者模式。

在监管者模式下，CPU被允许执行\indextext{特权指令}：例如，启用和禁用中断，读写存放页表地址的寄存器等。如果用户模式下的应用程序试图执行特权指令，那么CPU不会执行该指令，而是切换到监管者模式，以便监管者模式的代码可以终止该应用程序，因为它做了不该做的事情。第~\ref{CH:UNIX}章中的图~\ref{fig:os}说明了这种组织结构。一个应用程序只能执行用户模式指令（例如，加法等），并且据说是在\indextext{用户空间}中运行，而在监管者模式下的软件也可以执行特权指令，并且据说是在\indextext{内核空间}中运行。在内核空间（或监管者模式）中运行的软件被称为\indextext{内核}。

想要调用内核函数（例如，xv6中的\lstinline{read}系统调用）的应用程序必须\indextext{转换}到内核；应用程序\emph{不能}直接调用内核函数。CPU提供一个特殊的指令，将CPU从用户模式切换到监管者模式，并在内核指定的入口点进入内核。（RISC-V为此提供了\indexcode{ecall}指令。）一旦CPU切换到监管者模式，内核就可以验证系统调用的参数（例如，检查传递给系统调用的地址是否是应用程序内存的一部分），决定是否允许应用程序执行请求的操作（例如，检查应用程序是否被允许写入指定的文件），然后拒绝它或执行它。内核控制到监管者模式的转换入口点是很重要的；如果应用程序可以决定内核入口点，一个恶意的应用程序就可以，例如，在跳过参数验证的地方进入内核。

%%
\section{内核组织}
%%

一个关键的设计问题是操作系统的哪一部分应该在监管者模式下运行。一种可能性是整个操作系统都驻留在内核中，因此所有系统调用的实现都在监管者模式下运行。这种组织被称为\indextext{单体内核}。

在这种组织中，整个操作系统由一个以完全硬件特权运行的单一程序组成。这种组织很方便，因为操作系统设计者不必决定操作系统的哪些部分不需要完全的硬件特权。此外，操作系统不同部分之间的协作也更容易。例如，一个操作系统可能有一个缓冲区缓存，可以被文件系统和虚拟内存系统共享。

单体组织的缺点是，操作系统不同部分之间的交互通常很复杂（我们将在本文的其余部分看到），因此操作系统开发人员很容易犯错。在单体内核中，一个错误是致命的，因为在监管者模式下的一个错误通常会导致内核失败。如果内核失败，计算机就会停止工作，因此所有应用程序也会失败。计算机必须重新启动才能再次启动。

为了降低内核中出错的风险，操作系统设计者可以最小化在监管者模式下运行的操作系统代码量，并在用户模式下执行大部分操作系统。这种内核组织被称为\indextext{微内核}。

\begin{figure}[t]
\center
\includegraphics[scale=0.5]{fig/mkernel.pdf}
\caption{一个带有文件系统服务器的微内核}
\label{fig:mkernel}
\end{figure}

图~\ref{fig:mkernel}说明了这种微内核设计。在图中，文件系统作为一个用户级进程运行。作为进程运行的操作系统服务被称为服务器。为了允许应用程序与文件服务器交互，内核提供了一种进程间通信机制，用于将消息从一个用户模式进程发送到另一个用户模式进程。例如，如果像shell这样的应用程序想要读或写一个文件，它会向文件服务器发送一个消息并等待响应。

在微内核中，内核接口由一些用于启动应用程序、发送消息、访问设备硬件等的低级函数组成。这种组织使得内核相对简单，因为大部分操作系统都驻留在用户级服务器中。

在现实世界中，单体内核和微内核都很流行。许多Unix内核是单体的。例如，Linux有一个单体内核，尽管一些操作系统功能作为用户级服务器运行（例如，窗口系统）。Linux为操作系统密集型应用程序提供了高性能，部分原因是因为内核的子系统可以紧密集成。

诸如Minix、L4和QNX等操作系统被组织为带有服务器的微内核，并在嵌入式领域得到了广泛部署。L4的一个变体seL4足够小，以至于已经针对内存安全和其他安全属性进行了验证~\cite{sel4}。

操作系统开发者之间关于哪种组织更好有很多争论，并且没有确凿的证据表明一种比另一种更好。此外，它很大程度上取决于“更好”意味着什么：更快的性能、更小的代码大小、内核的可靠性、整个操作系统（包括用户级服务）的可靠性等。

还有一些实际考虑因素可能比哪个组织更好的问题更重要。一些操作系统有一个微内核，但为了性能原因，在内核空间运行一些用户级服务。一些操作系统有单体内核，因为它们就是这样开始的，而且几乎没有动力去转向纯粹的微内核组织，因为新功能可能比重写现有操作系统以适应微内核设计更重要。

从本书的角度来看，微内核和单体操作系统共享许多关键思想。它们实现系统调用，它们使用页表，它们处理中断，它们支持进程，它们使用锁进行并发控制，它们实现一个文件系统，等等。本书侧重于这些核心思想。

Xv6是作为一个单体内核实现的，像大多数Unix操作系统一样。因此，xv6内核接口对应于操作系统接口，并且内核实现了完整的操作系统。由于xv6不提供许多服务，其内核比一些微内核要小，但概念上xv6是单体的。

\section{代码: xv6 组织}

\begin{figure}[t]
\center
\begin{tabular}{l|l}
{\bf 文件} & {\bf 描述} \\
\midrule
bio.c & 文件系统的磁盘块缓存。 \\
console.c & 连接到用户键盘和屏幕。 \\
entry.S & 最开始的引导指令。 \\
exec.c & exec() 系统调用。 \\
file.c & 文件描述符支持。 \\
fs.c & 文件系统。 \\
kalloc.c & 物理页分配器。 \\
kernelvec.S & 处理来自内核的陷阱。 \\
log.c & 文件系统日志和崩溃恢复。 \\
main.c & 在引导期间控制其他模块的初始化。 \\
pipe.c & 管道。 \\
plic.c & RISC-V 中断控制器。 \\
printf.c & 向控制台格式化输出。 \\
proc.c & 进程和调度。 \\
sleeplock.c & 放弃CPU的锁。 \\
spinlock.c & 不放弃CPU的锁。 \\
start.c & 早期的机器模式引导代码。 \\
string.c & C 字符串和字节数组库。 \\
swtch.S & 线程切换。 \\
syscall.c & 将系统调用分派给处理函数。 \\
sysfile.c & 文件相关的系统调用。 \\
sysproc.c & 进程相关的系统调用。 \\
trampoline.S & 用于在用户和内核之间切换的汇编代码。 \\
trap.c & 用于处理陷阱和中断并从中返回的C代码。 \\
uart.c & 串行端口控制台设备驱动程序。 \\
virtio\_disk.c & 磁盘设备驱动程序。 \\
vm.c & 管理页表和地址空间。 \\
\end{tabular}
\caption{Xv6 内核源文件。}
\label{fig:source}
\end{figure}

xv6 内核源代码位于 {\tt kernel/} 子目录中。源代码被划分为多个文件，遵循一个粗略的模块化概念；图~\ref{fig:source} 列出了这些文件。模块间的接口在 \lstinline{defs.h} \fileref{kernel/defs.h} 中定义。

%%
\section{进程概述}
%%

xv6 中的隔离单位（与其他 Unix 操作系统一样）是\indextext{进程}。进程抽象防止一个进程破坏或窥探另一个进程的内存、CPU、文件描述符等。它还防止进程破坏内核本身，这样进程就无法颠覆内核的隔离机制。内核必须小心地实现进程抽象，因为一个有bug或恶意的应用程序可能会欺骗内核或硬件去做一些坏事（例如，规避隔离）。内核用来实现进程的机制包括用户/监管者模式标志、地址空间和线程的时间分片。

为了帮助强制隔离，进程抽象为程序提供了一种它拥有自己的私有机器的错觉。进程为程序提供了一个看起来是私有的内存系统，或\indextext{地址空间}，其他进程无法读写。进程还为程序提供了一个看起来是它自己的CPU来执行程序的指令。

Xv6 使用页表（由硬件实现）来为每个进程提供自己的地址空间。RISC-V 页表将一个\indextext{虚拟地址}（RISC-V 指令操作的地址）翻译（或“映射”）到一个\indextext{物理地址}（CPU 发送到主内存的地址）。

\begin{figure}[t]
\centering
\includegraphics[scale=0.5]{fig/as.pdf}
\caption{进程虚拟地址空间的布局}
\label{fig:as}
\end{figure}

Xv6 为每个进程维护一个单独的页表，该页表定义了该进程的地址空间。如图~\ref{fig:as}所示，一个地址空间包括从虚拟地址零开始的进程的\indextext{用户内存}。指令排在最前面，然后是全局变量，然后是栈，最后是一个进程可以根据需要扩展的“堆”区（用于 malloc）。有许多因素限制了进程地址空间的最大大小：RISC-V 上的指针是 64 位宽的；硬件在页表中查找虚拟地址时只使用低 39 位；xv6 只使用这 39 位中的 38 位。因此，最大地址是 $2^{38}-1$ = 0x3fffffffff，即 \lstinline{MAXVA}~\lineref{kernel/riscv.h:/define.MAXVA/}。在地址空间的顶部，xv6 放置了一个\indextext{蹦床}页（4096 字节）和一个\indextext{陷阱帧}页。Xv6 使用这两个页面来转换到内核并返回；蹦床页包含转换进出内核的代码，陷阱帧是内核保存进程用户寄存器的地方，如第~\ref{CH:TRAP}章所述。

xv6 内核为每个进程维护许多状态，它将这些状态收集到一个 \indexcode{struct proc} \lineref{kernel/proc.h:/^struct.proc/} 中。一个进程最重要的内核状态是它的页表、它的内核栈和它的运行状态。我们将使用符号 \indexcode{p->xxx} 来引用 \lstinline{proc} 结构的元素；例如，\indexcode{p->pagetable} 是指向进程页表的指针。

每个进程都有一个控制线程（或简称\indextext{线程}），它持有执行进程所需的状态。在任何给定时间，一个线程可能正在一个 CPU 上执行，或者被挂起（不执行，但能够在将来恢复执行）。为了在进程之间切换 CPU，内核会挂起当前在该 CPU 上运行的线程并保存其状态，然后恢复另一个进程先前挂起的线程的状态。线程的大部分状态（局部变量、函数调用返回地址）都存储在线程的栈上。每个进程有两个栈：一个用户栈和一个内核栈 (\indexcode{p->kstack})。当进程执行用户指令时，只有它的用户栈在使用，而它的内核栈是空的。当进程进入内核时（对于系统调用或中断），内核代码在进程的内核栈上执行；当一个进程在内核中时，它的用户栈仍然包含保存的数据，但没有被主动使用。一个进程的线程交替地主动使用其用户栈和内核栈。内核栈是独立的（并且受到用户代码的保护），这样即使一个进程破坏了它的用户栈，内核也可以执行。

进程可以通过执行 RISC-V \indexcode{ecall} 指令来进行系统调用。该指令提升硬件特权级别并将程序计数器更改为内核定义的入口点。入口点的代码切换到进程的内核栈并执行实现系统调用的内核指令。当系统调用完成时，内核切换回用户栈并通过调用 \indexcode{sret} 指令返回用户空间，该指令降低硬件特权级别并恢复执行紧跟在系统调用指令之后的用户指令。一个进程的线程可以在内核中“阻塞”以等待 I/O，并在 I/O 完成后从它离开的地方恢复。

\indexcode{p->state} 指示进程是否已分配、准备运行、当前正在 CPU 上运行、等待 I/O 或正在退出。

\indexcode{p->pagetable} 以 RISC-V 硬件期望的格式保存进程的页表。当在用户空间执行该进程时，Xv6 会使分页硬件使用进程的 \lstinline{p->pagetable}。进程的页表也作为记录为存储进程内存而分配的物理页地址的记录。

总之，一个进程捆绑了两个设计思想：一个地址空间，给进程一个拥有自己内存的错觉；一个线程，给进程一个拥有自己CPU的错觉。在xv6中，一个进程由一个地址空间和一个线程组成。在真正的操作系统中，一个进程可能有多个线程以利用多个CPU。

%%
\section{代码：启动xv6，第一个进程和系统调用}
%%

为了让xv6更具体，我们将概述内核如何启动和运行第一个进程。后续章节将更详细地描述本次概述中出现的机制。

当RISC-V计算机上电时，它会自行初始化并运行存储在只读存储器中的引导加载程序。引导加载程序将xv6内核加载到内存中。然后，在机器模式下，CPU从\indexcode{_entry} \lineref{kernel/entry.S:/^.entry:/}开始执行xv6。RISC-V启动时分页硬件是禁用的：虚拟地址直接映射到物理地址。

加载程序将xv6内核加载到物理地址\texttt{0x80000000}的内存中。它将内核放在\texttt{0x80000000}而不是\texttt{0x0}的原因是地址范围\texttt{0x0:0x80000000}包含I/O设备。

\lstinline{_entry}处的指令设置一个堆栈，以便xv6可以运行C代码。Xv6在文件\lstinline{start.c} \lineref{kernel/start.c:/stack0/}中为初始堆栈\lstinline{stack0}声明了空间。 \lstinline{_entry}处的代码将堆栈指针寄存器\texttt{sp}加载为地址\lstinline{stack0+4096}，即堆栈的顶部，因为RISC-V上的堆栈是向下增长的。现在内核有了一个堆栈，\lstinline{_entry}在\lstinline{start} \lineref{kernel/start.c:/^start/}处调用C代码。

函数\lstinline{start}执行一些只能在机器模式下进行的配置，然后切换到监管者模式。为了进入监管者模式，RISC-V提供了指令\lstinline{mret}。该指令最常用于从先前从监管者模式到机器模式的调用中返回。\lstinline{start}并非从这样的调用中返回，而是将其设置为好像是这样：它在寄存器\lstinline{mstatus}中将先前的特权模式设置为监管者，通过将\lstinline{main}的地址写入寄存器\lstinline{mepc}来将返回地址设置为\lstinline{main}，通过将\lstinline{0}写入页表寄存器\lstinline{satp}来禁用监管者模式下的虚拟地址转换，并将所有中断和异常委托给监管者模式。

在跳转到监管者模式之前，\lstinline{start}执行了另一项任务：它对时钟芯片进行编程以生成定时器中断。完成这些内务处理后，\lstinline{start}通过调用\lstinline{mret}“返回”到监管者模式。这导致程序计数器更改为\lstinline{main} \lineref{kernel/main.c:/^main/}，即先前存储在\lstinline{mepc}中的地址。

在\lstinline{main} \lineref{kernel/main.c:/^main/}初始化了几个设备和子系统之后，它通过调用\lstinline{userinit} \lineref{kernel/proc.c:/^userinit/}创建了第一个进程。第一个进程执行一个用RISC-V汇编编写的小程序，该程序在xv6中进行了第一个系统调用。\indexcode{initcode.S} \lineref{user/initcode.S:3}将\lstinline{exec}系统调用的编号\lstinline{SYS_EXEC} \lineref{kernel/syscall.h:/exec/}加载到寄存器{\tt a7}中，然后调用\lstinline{ecall}重新进入内核。

内核在\lstinline{syscall} \lineref{kernel/syscall.c:/^syscall/}中使用寄存器{\tt a7}中的编号来调用所需的系统调用。系统调用表\lineref{kernel/syscall.c:/syscalls/}将\lstinline{SYS_EXEC}映射到函数\lstinline{sys_exec}，内核将调用该函数。正如我们在第~\ref{CH:UNIX}章中看到的，\indexcode{exec}用一个新程序（在本例中为\indexcode{/init}）替换当前进程的内存和寄存器。

一旦内核完成了\lstinline{exec}，它就会在\lstinline{/init}进程中返回用户空间。\lstinline{init} \lineref{user/init.c:/^main/}如果需要，会创建一个新的控制台设备文件，然后将其作为文件描述符0、1和2打开。然后它在控制台上启动一个shell。系统启动完成。

\section{安全模型}

您可能想知道操作系统如何处理有bug或恶意的代码。因为应对恶意比处理意外的bug要困难得多，所以主要关注提供针对恶意的安全性是合理的。以下是操作系统设计中典型安全假设和目标的高级视图。

操作系统必须假设进程的用户级代码会尽其所能破坏内核或其他进程。用户代码可能会尝试解引用其允许的地址空间之外的指针；它可能会尝试执行任何RISC-V指令，甚至是那些不打算用于用户代码的指令；它可能会尝试读写任何RISC-V控制寄存器；它可能会尝试直接访问设备硬件；它可能会向系统调用传递巧妙的值，以试图欺骗内核崩溃或做一些愚蠢的事情。内核的目标是限制每个用户进程，使其所能做的只是读/写/执行自己的用户内存，使用32个通用RISC-V寄存器，以及以系统调用旨在允许的方式影响内核和其他进程。内核必须阻止任何其他操作。这通常是内核设计中的一个绝对要求。

对内核自身代码的期望则大不相同。内核代码被假定是由善意和谨慎的程序员编写的。内核代码被期望是无bug的，当然也不包含任何恶意内容。这个假设影响了我们如何分析内核代码。例如，有许多内部内核函数（例如，自旋锁）如果内核代码使用不当会导致严重问题。在检查任何特定的内核代码片段时，我们会希望说服自己它的行为是正确的。然而，我们假设，总的来说，内核代码是正确编写的，并遵循了关于使用内核自身函数和数据结构的所有规则。在硬件层面，RISC-V CPU、RAM、磁盘等被假定按照文档中的描述运行，没有硬件bug。

当然，在现实生活中，事情并非如此简单。很难阻止聪明的用户代码通过消耗受内核保护的资源（磁盘空间、CPU时间、进程表槽位等）来使系统无法使用（或导致其恐慌）。编写无bug的内核代码或设计无bug的硬件通常是不可能的；如果恶意用户代码的编写者意识到内核或硬件的bug，他们会利用它们。即使在成熟、广泛使用的内核中，例如Linux，人们也不断发现新的漏洞~\cite{mitre:cves}。在内核中设计针对其可能存在bug的保障措施是值得的：断言、类型检查、堆栈保护页等。最后，用户和内核代码之间的区别有时会变得模糊：一些特权用户级进程可能提供基本服务，并实际上成为操作系统的一部分，在某些操作系统中，特权用户代码可以将新代码插入内核（就像Linux的可加载内核模块一样）。


%%
\section{现实世界}
%%

大多数操作系统都采用了进程概念，而且大多数进程看起来都与xv6的相似。然而，现代操作系统支持一个进程内的多个线程，以允许单个进程利用多个CPU。在一个进程中支持多个线程涉及到xv6所没有的大量机制，通常包括接口的改变（例如，Linux的\lstinline{clone}，\lstinline{fork}的一个变体），以控制线程共享进程的哪些方面。
%%
\section{练习}
%%

\begin{enumerate}

\item 向xv6添加一个系统调用，返回可用的空闲内存量。

% break *0x3ffffff000
% disas 0x3ffffff000,+8
% set disassemble-next-line auto
% x/i $pc
% break *0x3ffffff10e
% print/x $sepc
% print/x $pc

\end{enumerate}