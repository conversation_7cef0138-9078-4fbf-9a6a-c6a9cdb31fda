<html>
<head>
<title>Lab: Copy-on-Write Fork for xv6</title>
<link rel="stylesheet" href="labs.css" type="text/css" />
<script src="guidance.js"></script>
</head>
<body>

<h1>Lab: Copy-on-Write Fork for xv6</h2>

<p> Virtual memory provides a level of indirection: the kernel can
intercept memory references by marking PTEs invalid or read-only,
leading to page faults,
and can change what addresses mean by modifying PTEs.
There is a saying in computer systems that any systems problem can be
solved with a level of indirection.  
This lab explores an example: copy-on-write fork.
    
<p>To start the lab, switch to the cow branch:
<pre>
$ <kbd>git fetch</kbd>
$ <kbd>git checkout cow</kbd>
$ <kbd>make clean</kbd>
</pre>

<h2>The problem</h2>

The fork() system call in xv6 copies all of the parent process's
user-space memory into the child. If the parent is large, copying can
take a long time. Worse, the work is often largely wasted:
fork() is commonly followed by exec() in the child, which 
discards the copied memory, usually without using most of it.
On the other hand, if both parent and child use a copied page, and one or both
writes it, the copy is truly needed.

<h2>The solution</h2>

Your goal in implementing copy-on-write (COW) fork() is to defer allocating and
copying physical memory pages until the copies are actually
needed, if ever.

<p>
COW fork() creates just a pagetable for the child, with PTEs for user
memory pointing to the parent's physical pages. COW fork() marks all
the user PTEs in both parent and child as read-only. When either
process tries to write one of these COW pages, the CPU will force a
page fault. The kernel page-fault handler detects this case, allocates
a page of physical memory for the faulting process, copies the
original page into the new page, and modifies the relevant PTE in the
faulting process to refer to the new page, this time with the PTE
marked writeable. When the page fault handler returns, the user
process will be able to write its copy of the page.

<p>
COW fork() makes freeing of the physical pages that implement user
memory a little trickier. A given physical page may be referred to by
multiple processes' page tables, and should be freed only when the
last reference disappears.  In a simple kernel like xv6 this
bookkeeping is reasonably straightforward, but in production kernels
this can be difficult to get right; see, for example,
<a href="https://lwn.net/Articles/849638/">Patching until the COWs
come home</a>.

<h2>Implement copy-on-write fork<script>g("hard")</script></h2>

<p>
<div class="required">
Your task is to implement copy-on-write fork in the xv6 kernel. You are
done if your modified kernel executes both the cowtest and 'usertests -q'
programs successfully.
</div>

<p>To help you test your implementation, we've provided an xv6 program
called cowtest (source in user/cowtest.c). cowtest runs various tests, but
even the first will fail on unmodified xv6. Thus, initially, you
will see:

<pre>
$ <kbd>cowtest</kbd>
simple: fork() failed
$ 
</pre>

The "simple" test allocates more than half of available physical
memory, and then fork()s. The fork fails because there is not enough
free physical memory to give the child a complete copy of the parent's memory.

<p>
When you are done, your kernel should pass all the tests in both cowtest and
usertests -q. That is:

<pre>
$ <kbd>cowtest</kbd>
simple: ok
simple: ok
three: ok
three: ok
three: ok
file: ok
forkfork: ok
ALL COW TESTS PASSED
$ <kbd>usertests -q</kbd>
...
ALL TESTS PASSED
$
</pre>

<p>
Here's a reasonable plan of attack.

<ol>

<li> Modify uvmcopy() to map the parent's physical pages into the
child, instead of allocating new pages. Clear <tt>PTE_W</tt> in the
PTEs of both child and parent for pages that have <tt>PTE_W</tt> set.

  
<li> Modify usertrap() to recognize page faults. When a write page-fault
occurs on a COW page that was originally writeable,
allocate a new page with kalloc(), copy the old
page to the new page, and install the new page in the PTE with <tt>PTE_W</tt>
set.
Pages that were originally read-only (not mapped <tt>PTE_W</tt>,
like pages in the text segment) should remain read-only and
shared between parent and child; a process that tries to write
such a page should be killed.

<li> Ensure that each physical page is freed when the last PTE
reference to it goes away -- but not before.
A good way to do this is to keep, for each physical page, a "reference count"
of the number of user page tables that refer to that page.
Set a page's reference count to one when <tt>kalloc()</tt> allocates it.
Increment a page's reference count
when fork causes a child to share the page, and
decrement a page's count each time any process drops
the page from its page table.
<tt>kfree()</tt> should only place a page back on the free list if its
reference count is zero.  It's OK to to keep these counts in a
fixed-size array of integers.  You'll have to work out a scheme for
how to index the array and how to choose its size. For example, you
could index the array with the page's physical address divided by
4096, and give the array a number of elements equal to highest
physical address of any page placed on the free list
by <tt>kinit()</tt> in kalloc.c. Feel free to modify kalloc.c
(e.g., <tt>kalloc()</tt> and <tt>kfree()</tt>) to maintain the
reference counts.

<li> Modify copyout() to use the
same scheme as page faults when it encounters a COW page.

</ol>

<p>Some hints:

  <ul>
    
<li>
It may be useful to have a way to record, for each PTE, whether it is
a COW mapping. You can use the RSW (reserved for software) bits in
the RISC-V PTE for this.

<li>
<tt>usertests -q</tt> explores scenarios that <tt>cowtest</tt> does not test,
so don't forget to check that all tests pass for both.

<li>Some helpful macros and definitions for page table flags are at the
end of <tt>kernel/riscv.h</tt>.

<li>If a COW page fault occurs and there's no free memory, the process
should be killed.

  </ul>
  
<p><a name="submit"></>
<h2>Submit the lab</h2>

<h3>Time spent</h3>

<p>Create a new file, <tt>time.txt</tt>, and put in a single integer, the
number of hours you spent on the lab.
<kbd>git add</kbd> and <kbd>git commit</kbd> the file.

<h3>Answers</h3>

<p>If this lab had questions, write up your answers in <tt>answers-*.txt</tt>.
<kbd>git add</kbd> and <kbd>git commit</kbd> these files.

<h3>Submit</h3>

<p>Assignment submissions are handled by Gradescope.
You will need an MIT gradescope account.
See Piazza for the entry code to join the class.
Use <a href="https://help.gradescope.com/article/gi7gm49peg-student-add-course#joining_a_course_using_a_course_code">this link</a>
if you need more help joining.

<p>When you're ready to submit, run <kbd>make zipball</kbd>,
which will generate <tt>lab.zip</tt>.
Upload this zip file to the corresponding Gradescope assignment.

<p> If you run <kbd>make zipball</kbd> and you have either uncomitted changes or
untracked files, you will see output similar to the following:
<pre>
 M hello.c
?? bar.c
?? foo.pyc
Untracked files will not be handed in.  Continue? [y/N]
</pre>
Inspect the above lines and make sure all files that your lab solution needs
are tracked, i.e., not listed in a line that begins with <tt>??</tt>.
You can cause <tt>git</tt> to track a new file that you create using
<kbd>git add {filename}</kbd>.
</p>

<p>
<div class="warning">
<ul>
  <li>Please run <kbd>make grade</kbd> to ensure that your code passes all of the tests.
    The Gradescope autograder will use the same grading program to assign your submission a grade.</li>
  <li>Commit any modified source code before running <kbd>make zipball</kbd>.</li>
  <li>You can inspect the status of your submission and download the submitted
    code at Gradescope. The Gradescope lab grade is your final lab grade.</li>
</ul>
</div>



<h2>Optional challenge exercise</h2>

<ul>

  <li>Measure how much your COW implementation reduces the number
  of bytes xv6 copies and the number of physical pages it allocates.
  Find and exploit opportunities to further reduce those numbers.

</ul>

</body>
</html>
