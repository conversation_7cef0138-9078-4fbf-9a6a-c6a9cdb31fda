<html>
<head>
<title>Lab: mmap</title>
<link rel="stylesheet" href="labs.css" type="text/css" />
<script src="guidance.js"></script>
</head>
<body>

<h1>Lab: mmap <script>g("hard")</script></h1>

The <tt>mmap</tt> and <tt>munmap</tt> system calls allow UNIX programs
to exert detailed control over their address spaces. They can be used
to share memory among processes, to map files into process address
spaces, and as part of user-level page fault schemes such as the
garbage-collection algorithms discussed in lecture.
In this lab you'll add <tt>mmap</tt> and <tt>munmap</tt>
to xv6, focusing on memory-mapped files.

<p>Fetch the xv6 source for the lab and check out the <tt>mmap</tt> branch:

<pre>
  $ <kbd>git fetch</kbd>
  $ <kbd>git checkout mmap</kbd>
  $ <kbd>make clean</kbd>
</pre>

<p>
The manual page
(run <kbd>man 2 mmap</kbd>) shows this declaration for <tt>mmap</tt>:
<pre>
void *mmap(void *addr, size_t len, int prot, int flags,
           int fd, off_t offset);
</pre>
<p>
<tt>mmap</tt> can be called in many ways,
but this lab requires only
a subset of its features relevant to memory-mapping a file.
You can assume that
<tt>addr</tt> will always be zero, meaning that the
kernel should decide the virtual address at which to map the file.
<tt>mmap</tt> returns that address, or 0xffffffffffffffff if
it fails.
<tt>len</tt> is the number of bytes to map; it might not be
the same as the file's length.
<tt>prot</tt> indicates whether the memory should be mapped
readable, writeable, and/or executable; you can assume 
that <tt>prot</tt> is <tt>PROT_READ</tt> or <tt>PROT_WRITE</tt>
or both.
<tt>flags</tt> will be either <tt>MAP_SHARED</tt>,
meaning that modifications to the mapped memory should
be written back to the file, or <tt>MAP_PRIVATE</tt>,
meaning that they should not. You don't have to implement any
other bits in <tt>flags</tt>.
<tt>fd</tt> is the open file descriptor of the file to map.
You can assume <tt>offset</tt> is zero (it's the starting point
in the file at which to map).

<p>
Your implementation should fill
in the page table lazily, in response to page faults.
That is, <tt>mmap</tt> itself should not allocate physical memory or
read the file. Instead, do that in page fault handling code
in (or called by) <tt>usertrap</tt>, as in the copy-on-write lab.
The reason to be lazy is to ensure that <tt>mmap</tt> of
a large file is fast, and that <tt>mmap</tt> of a file larger
than physical memory is possible.

<p>
It's OK if processes that map the same <tt>MAP_SHARED</tt>
file do <b>not</b> share physical pages.

<p>
The manual page
(run <kbd>man 2 munmap</kbd>) shows this declaration for <tt>munmap</tt>:
<pre>
int munmap(void *addr, size_t len);
</pre>
<p>
<tt>munmap</tt> should remove mmap mappings in the
indicated address range, if any. If the process has modified the memory and
has it mapped <tt>MAP_SHARED</tt>, the modifications should first be
written to the file. An <tt>munmap</tt> call might cover only a
portion of an mmap-ed region, but you can assume that it will either
unmap at the start, or at the end, or the whole region (but not punch
a hole in the middle of a region). When a process exits, any
modifictions it has made to <tt>MAP_SHARED</tt> regions should be
written to the relevant files, as if the process had called <tt>munmap</tt>.

<div class="required">
<p>
You should implement enough <tt>mmap</tt> and <tt>munmap</tt>
functionality to make the
<tt>mmaptest</tt> test program work. If <tt>mmaptest</tt>
doesn't use a <tt>mmap</tt> feature, you don't need to implement
that feature.
You must also ensure that <tt>usertests -q</tt> continues to work.
</div>

<p>
When you're done, you should see output similar to this:
<pre>
$ <kbd>mmaptest</kbd>
test basic mmap
test basic mmap: OK
test mmap private
test mmap private: OK
test mmap read-only
test mmap read-only: OK
test mmap read/write
test mmap read/write: OK
test mmap dirty
test mmap dirty: OK
test not-mapped unmap
test not-mapped unmap: OK
test lazy access
test lazy access: OK
test mmap two files
test mmap two files: OK
test fork
test fork: OK
test munmap prevents access
usertrap(): unexpected scause 0xd pid=7
            sepc=0x924 stval=0xc0001000
usertrap(): unexpected scause 0xd pid=8
            sepc=0x9ac stval=0xc0000000
test munmap prevents access: OK
test writes to read-only mapped memory
usertrap(): unexpected scause 0xf pid=9
            sepc=0xaf4 stval=0xc0000000
test writes to read-only mapped memory: OK
mmaptest: all tests succeeded
$ <kbd>usertests -q</kbd>
usertests starting
...
ALL TESTS PASSED
$ 
</pre>

<p>Here are some hints:

  <ul>
    <li>Start by adding <tt>_mmaptest</tt> to <tt>UPROGS</tt>,
      and <tt>mmap</tt> and <tt>munmap</tt> system calls, in order to
      get <tt>user/mmaptest.c</tt> to compile. For now, just return
      errors from <tt>mmap</tt> and <tt>munmap</tt>.  We defined
      <tt>PROT_READ</tt> etc for you in <tt>kernel/fcntl.h</tt>.
      Run <tt>mmaptest</tt>, which will fail at the first mmap call.
      
    <li>Keep track of what <tt>mmap</tt> has mapped for each process.
      Define a structure corresponding to the VMA (virtual
      memory area) described in the "virtual memory for applications" lecture.
      This should record the address, length, permissions, file, etc.
      for a virtual memory range created by <tt>mmap</tt>. Since the xv6
      kernel doesn't have a variable-size memory allocator in the kernel,
      it's OK to
      declare a fixed-size array of VMAs and allocate
      from that array as needed. A size of 16 should be sufficient.

    <li>Implement <tt>mmap</tt>:
      find an unused region in the process's
      address space in which to map the file,
      and add a VMA to the process's
      table of mapped regions.
      The VMA should contain a pointer to
      a <tt>struct file</tt> for the file being mapped; <tt>mmap</tt> should
      increase the file's reference count so that the structure doesn't
      disappear when the file is closed (hint:
      see <tt>filedup</tt>).
      Run <tt>mmaptest</tt>: the first <tt>mmap</tt> should
      succeed, but the first access to the mmap-ed memory will 
      cause a page fault and kill <tt>mmaptest</tt>.

    <li>Add code to cause a page-fault in a mmap-ed region to
      allocate a page of physical memory, read 4096 bytes of
      the relevant file into
      that page, and map it into the user address space.
      Read the file with <tt>readi</tt>,
      which takes an offset argument at which to read in the
      file (but you will have to lock/unlock the inode passed
      to <tt>readi</tt>).  Don't forget to set the permissions correctly
      on the page.  Run <tt>mmaptest</tt>; it should get to the
      first <tt>munmap</tt>.
      
    <li>Implement <tt>munmap</tt>: find the VMA for the address range and
      unmap the specified pages (hint: use <tt>uvmunmap</tt>).
      If <tt>munmap</tt> removes all pages of a
      previous <tt>mmap</tt>, it should decrement the reference count
      of the corresponding <tt>struct file</tt>. If an unmapped page
      has been modified and the file is mapped <tt>MAP_SHARED</tt>,
      write the page back to the file.
      Look at <tt>filewrite</tt> for inspiration.

    <li>Ideally your implementation would only write back
      <tt>MAP_SHARED</tt> pages that the program actually modified.
      The dirty bit (<tt>D</tt>) in the RISC-V PTE indicates whether a
      page has been written. However, <tt>mmaptest</tt> does not check
      that non-dirty pages are not written back; thus you can get away
      with writing pages back without looking at <tt>D</tt> bits.

    <li>Modify <tt>exit</tt> to unmap the process's mapped regions as
      if <tt>munmap</tt> had been called.
      Run <tt>mmaptest</tt>; all tests through <tt>test mmap two files</tt>
      should pass, but probably not <tt>test fork</tt>.

    <li>Modify <tt>fork</tt> to ensure that the child has the
    same mapped regions as the parent.
    Don't
    forget to increment the reference count for a VMA's <tt>struct
    file</tt>.  In the page fault handler of the child, it is OK to
    allocate a new physical page instead of sharing a page with the
    parent. The latter would be cooler, but it would require more
    implementation work.  Run <tt>mmaptest</tt>; it should pass
    all the tests.
          
  </ul>
  
<p>Run <tt>usertests -q</tt> to make sure everything still works.

<p><a name="submit"></>
<h2>Submit the lab</h2>

<h3>Time spent</h3>

<p>Create a new file, <tt>time.txt</tt>, and put in a single integer, the
number of hours you spent on the lab.
<kbd>git add</kbd> and <kbd>git commit</kbd> the file.

<h3>Answers</h3>

<p>If this lab had questions, write up your answers in <tt>answers-*.txt</tt>.
<kbd>git add</kbd> and <kbd>git commit</kbd> these files.

<h3>Submit</h3>

<p>Assignment submissions are handled by Gradescope.
You will need an MIT gradescope account.
See Piazza for the entry code to join the class.
Use <a href="https://help.gradescope.com/article/gi7gm49peg-student-add-course#joining_a_course_using_a_course_code">this link</a>
if you need more help joining.

<p>When you're ready to submit, run <kbd>make zipball</kbd>,
which will generate <tt>lab.zip</tt>.
Upload this zip file to the corresponding Gradescope assignment.

<p> If you run <kbd>make zipball</kbd> and you have either uncomitted changes or
untracked files, you will see output similar to the following:
<pre>
 M hello.c
?? bar.c
?? foo.pyc
Untracked files will not be handed in.  Continue? [y/N]
</pre>
Inspect the above lines and make sure all files that your lab solution needs
are tracked, i.e., not listed in a line that begins with <tt>??</tt>.
You can cause <tt>git</tt> to track a new file that you create using
<kbd>git add {filename}</kbd>.
</p>

<p>
<div class="warning">
<ul>
  <li>Please run <kbd>make grade</kbd> to ensure that your code passes all of the tests.
    The Gradescope autograder will use the same grading program to assign your submission a grade.</li>
  <li>Commit any modified source code before running <kbd>make zipball</kbd>.</li>
  <li>You can inspect the status of your submission and download the submitted
    code at Gradescope. The Gradescope lab grade is your final lab grade.</li>
</ul>
</div>



  <h2>Optional challenges</h2>
  
  <ul>
    
    <li>If two processes have the same file mmap-ed (as
      in the fork tests), share their physical pages. You will need
      reference counts on physical pages.

    <li>Your solution probably allocates a new physical page for each page
    read from the mmap-ed file, even though the data is also in kernel
    memory in the buffer cache.  Modify your implementation to use
    that physical memory, instead of allocating a new page.  This requires that
    file blocks be the same size as pages (set <tt>BSIZE</tt> to
    4096).  You will need to pin mmap-ed blocks into the buffer cache.
    You will need worry about reference counts.

    <li>Remove redundancy between your implementation for lazy
    allocation and your implementation of mmap-ed files.  (Hint:
    create a VMA for the lazy allocation area.)

    <li>Modify <tt>exec</tt> to use a VMA for different sections of
    the binary so that you get on-demand-paged executables. This will
    make starting programs faster, because <tt>exec</tt> will not have
      to read any data from the file system.

    <li>Implement page-out and page-in: have
    the kernel move some parts of processes to disk when
    physical memory is low.  Then, page in the paged-out memory when
    the process references it.
      
  </ul>

</body>
</html>

<!--  LocalWords:  mmap munmap macOS addr prot fd unmap mmaptest VMA
 -->
<!--  LocalWords:  usertests reparent bigdir usertrap allocator VMAs
 -->
<!--  LocalWords:  struct filedup readi inode uvmunmap unmapped PTE
 -->
<!--  LocalWords:  filewrite VMA's BSIZE executables unmapping
 -->
