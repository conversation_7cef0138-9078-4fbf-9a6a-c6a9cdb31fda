
<HTML>
<HEAD>
<TITLE>
6.1810 / Fall 2024
</TITLE>
<META HTTP-EQUIV="pragma" CONTENT="no-cache">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">

<link href="css/bootstrap.min.css" rel="stylesheet" media="screen">
<link rel="stylesheet" type="text/css" href="css/style.css">
<link rel="stylesheet" href="labs/labs.css" type="text/css" />

</HEAD>

<BODY BGCOLOR=#ffffff TEXT=#000000>

    <!-- Fixed navbar -->
    <div class="navbar navbar-default navbar-fixed-top" role="navigation">
      <div class="container-fluid">
        <div class="navbar-header">
          <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
            <span class="sr-only">Toggle navigation</span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
          <a class="navbar-brand" href="index.html">6.1810: Operating System Engineering</a>
        </div>
        <div class="navbar-collapse collapse">
          <ul class="nav navbar-nav">
	    <li><a href="schedule.html">Schedule</a></li>
	    <li class="dropdown">
              <a href="#" class="dropdown-toggle" data-toggle="dropdown">Class<span class="caret"></span></a>
	      <ul class="dropdown-menu" role="menu">
                <li><a href="overview.html">Overview</a></li>
                <li><a href="general.html">Course Structure</a></li>
		<li><a href="https://pdos.csail.mit.edu/6.1810/2023/">6.1810 2023</a></li>
	      </ul>
	    </li>
            <li class="dropdown">
              <a href="#" class="dropdown-toggle" data-toggle="dropdown">Labs<span class="caret"></span></a>
              <ul class="dropdown-menu" role="menu">
		<li><a href="tools.html">Tools</a></li>
		<li><a href="labs/guidance.html">Guidance</a></li>
                <li><a href="labs/util.html">Lab Utilities</a></li>
		<li><a href="labs/syscall.html">Lab System calls</a></li>
		<li><a href="labs/pgtbl.html">Lab Page tables</a></li>
		<li><a href="labs/traps.html">Lab Traps</a></li>
		<li><a href="labs/cow.html">Lab Copy on-write</a></li>
		<li><a href="labs/net.html">Lab network driver</a></li>
		<li><a href="labs/lock.html">Lab Lock</a></li>
		<li><a href="labs/fs.html">Lab File system</a></li>
		<li><a href="labs/mmap.html">Lab mmap</a></li>
              </ul>
            </li>
            <li class="dropdown">
              <a href="#" class="dropdown-toggle" data-toggle="dropdown">xv6<span class="caret"></span></a>
	      <ul class="dropdown-menu" role="menu">
		<li><a href="xv6.html">xv6</a></li>
		<li><a href="xv6/book-riscv-rev4.pdf">xv6 book</a></li>
	      </ul>
	    </li>
	    <li><a href="reference.html">References</a></li>  
	    <li><a href="https://piazza.com/mit/fall2024/61810">Piazza</a></li>
          </ul>
	  <ul class="nav navbar-nav navbar-right">
	    <p class="navbar-text">2024</p>
	  </ul>
        </div>
      </div>
    </div>


<div class="container">





<style type='text/css'>
.l {
	margin-left: 4em;
}
</style>

<script type='text/javascript'>
function unfoldinstrux(unfold) {
  var e = document.getElementById("installinstrux");
  e.className = unfold ? "foldo" : "foldc";
}
</script>

<H1>
Tools Used in 6.1810
</H1>

<p>For this class you'll need the RISC-V versions of 
QEMU 7.2+, GDB 8.3+, GCC, and Binutils.</p>

<p>If you are having trouble getting things set up, please come to office
hours or post on Piazza. We're happy to help!</p>

<h3>Debian or Ubuntu</h3>
<pre>sudo apt-get install git build-essential gdb-multiarch qemu-system-misc gcc-riscv64-linux-gnu binutils-riscv64-linux-gnu </pre>

<p>
You'll likely need to be running Ubuntu 24 (or later) in order for
apt-get to install a recent enough qemu.

<h3>Arch Linux</h3>
<pre>sudo pacman -S riscv64-linux-gnu-binutils riscv64-linux-gnu-gcc riscv64-linux-gnu-gdb qemu-emulators-full</pre>

<h3>Installing on Windows</h3>

<p> Students running Windows are encouraged to either install Linux
on their local machine or use WSL 2 (Windows Subsystem for Linux 2).</p>

<p> We also encourage students to install the <a href="https://apps.microsoft.com/store/detail/windows-terminal/9N0DX20HK701">Windows Terminal</a> tool in lieu of using Powershell/Command Prompt. </p>

<p> To use WSL 2, first make sure you have the <a href="https://docs.microsoft.com/en-us/windows/wsl/install-win10">Windows Subsystem for Linux</a> installed. Then add <a href="https://apps.microsoft.com/detail/9nz3klhxdjp5">Ubuntu 24.04 from the Microsoft Store</a>. Afterwards you should be able to launch Ubuntu and interact with the machine.</p>

<p> <i> IMPORTANT: Make sure that you are running version 2 of WSL.
WSL 1 does not work with the 6.1810 labs.
To check,
run <code> wsl -l -v </code> in a Windows terminal to confirm that WSL
2 and the correct Ubuntu version are installed. </i> </p>

<p>To install all the software you need for this class, run:</p>

<pre>
$ sudo apt-get update && sudo apt-get upgrade
$ sudo apt-get install git build-essential gdb-multiarch qemu-system-misc gcc-riscv64-linux-gnu binutils-riscv64-linux-gnu
</pre>

<p>From Windows, you can access all of your WSL files under the <i>"\\wsl$\"</i>
directory. For instance, the home directory for an Ubuntu 20.04 installation should
be at <i>"\\wsl$\Ubuntu-20.04\home\&lt;username&gt;\"</i>. <p>

<h3>Running a Linux VM</h3>
<p>If you're running an operating system on which it's not convenient to
install the RISC-V tools, you may find it useful to run a Linux
virtual machine (VM) and install the tools in the VM.
  Installing a Linux virtual machine is a two step process.  First, get
  a virtualization platform; we suggest:</p>

  <ul><li><a href='https://www.virtualbox.org/'><strong>VirtualBox</strong></a>
  (free for Mac, Linux, Windows) &mdash; <a
  href='https://www.virtualbox.org/wiki/Downloads'>Download
  page</a></li>
  </ul>

  <p>Once the virtualization platform is installed, fetch a boot
  disk image for the Linux distribution of your choice.</p>

  <ul><li><a
  href='http://www.ubuntu.com/download/desktop'>Ubuntu
  Desktop</a> is one option.</li>
  </ul>

  <p>This will download a file named something like
  <tt>ubuntu-20.04.3-desktop-amd64.iso</tt>.  Start up your
  virtualization platform and create a new (64-bit) virtual machine.
  Use the Ubuntu image as a boot disk; the
  procedure differs among VMs but shouldn't be too difficult.
</p>

<h3>Installing on macOS</h3>

<p>First, install developer tools:

<pre>
$ xcode-select --install
</pre>

<p>Next, install <a href="https://brew.sh/">Homebrew</a>, a package manager for macOS:

<pre>
$ /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
</pre>

<p>Next, install the <a href="https://github.com/riscv/homebrew-riscv">RISC-V compiler toolchain</a>:

<pre>
$ brew tap riscv/riscv
$ brew install riscv-tools
</pre>

<p>The brew formula may not link into <tt>/usr/local</tt>. You will need to
update your shell's rc file (e.g. <a
href="https://www.gnu.org/software/bash/manual/html_node/Bash-Startup-Files.html">~/.bashrc</a>)
to add the appropriate directory to <a
href="http://www.linfo.org/path_env_var.html">$PATH</a>.

<pre>
PATH=$PATH:/usr/local/opt/riscv-gnu-toolchain/bin
</pre>

<p>Finally, install QEMU:

<pre>
brew install qemu
</pre>

<h3>Athena</h3>
<p>We <i>strongly</i> discourage the use of Athena since there have been many problems with running the labs on Athena in the past. </p> 

<p> If you must use Athena, it is possible to work on the labs using the MIT Athena machines running Linux via athena.dialup.mit.edu. All of the tools necessary for the labs are located in the 6.828 locker. </p>

<p>ssh into one of the Athena dialup machines and add the tools: </p>

<pre>
$ ssh {your kerberos}@athena.dialup.mit.edu
$ add -f 6.828
</pre>

<p>If you use Athena, you must use an x86 machine; that is, <tt>uname -a</tt>
should mention <tt>i686 GNU/Linux</tt> or
<tt>x86_64 GNU/Linux</tt>.

<h2> Testing your Installation </h2>
<p>To test your installation, you should be able to compile and run xv6. You can try this by following the instructions in the <a href="labs/util.html">first lab</a>.</p>

<p>You can also double check your installation is correct by running the following:</p>

<pre>
$ qemu-system-riscv64 --version
QEMU emulator version 7.2.0
</pre>

<p>And at least one RISC-V version of GCC:</p>

<pre>
$ riscv64-linux-gnu-gcc --version
riscv64-linux-gnu-gcc (Debian 10.3.0-8) 10.3.0
...
</pre>

<pre>
$ riscv64-unknown-elf-gcc --version
riscv64-unknown-elf-gcc (GCC) 10.1.0
...
</pre>

<pre>
$ riscv64-unknown-linux-gnu-gcc --version
riscv64-unknown-linux-gnu-gcc (GCC) 10.1.0
...
</pre>

<!-- End Page Content -->

<hr>

<font style="font-size: 12px;">
<p>Questions or comments regarding 6.1810?  Send e-mail to the course staff at
<A
HREF="mailto:<EMAIL>"><I><EMAIL></I></A>.

<p><a rel="license" href="https://creativecommons.org/licenses/by/3.0/us/"><img
alt="Creative Commons License" style="border-width:0"
src="https://i.creativecommons.org/l/by/3.0/us/88x31.png" ></a> <B><A HREF="#top">Top</A></B> //
<B><A HREF="index.html">6.1810 home</A></B> // 
<i>Last updated Friday, 06-Sep-2024 14:54:57 EDT</i>
</font>

</div>

<script src="js/jquery-1.10.2.min.js"></script>
<script src="js/bootstrap.min.js"></script>

</BODY>
</HTML>


<!--  LocalWords:  Cygwin cygwin Toolchain Linuxes BSDs toolchain gcc ld jos cd
 -->
<!--  LocalWords:  objdump conf GCCPREFIX linux binutils libstdc GNUmakefile sh
 -->
<!--  LocalWords:  Bochs athena scp username bochs prebuilt wget xzvf
 -->
<!--  LocalWords:  disasm
 -->
