<html>
<head>
<title>Lab: page tables</title>
<link rel="stylesheet" href="labs.css" type="text/css" />
<script src="guidance.js"></script>
</head>
<body>

<h1>Lab: page tables</h1>

<p> In this lab you will explore page tables and modify them to
  implement common OS features.

<div class="prereq">
<p>Before you start coding, read Chapter 3 of
  the <a href="../xv6/book-riscv-rev4.pdf">xv6 book</a>, and related files:

  <ul>
   <li> <tt>kernel/memlayout.h</tt>, which captures the layout of memory.
	  
    <li> <tt>kernel/vm.c</tt>, which contains most virtual memory (VM) code.

    <li> <tt>kernel/kalloc.c</tt>, which contains code for allocating and
    freeing physical memory.
  
  </ul>
  
It may also help to consult the <a href="https://drive.google.com/file/d/17GeetSnT5wW3xNuAHI95-SI1gPGd5sJ_/view?usp=drive_link">RISC-V privileged architecture manual</a>.

</div>

<p>To start the lab, switch to the pgtbl branch:
  <pre>
  $ <kbd>git fetch</kbd>
  $ <kbd>git checkout pgtbl</kbd>
  $ <kbd>make clean</kbd>
  </pre>

<h2>Inspect a user-process page table <script>g("easy")</script></h2>

<p>
To help you understand RISC-V page tables, your first task is to explain
the page table for a user process.

<p>Run <tt>make qemu</tt> and run the user program <tt>pgtbltest</tt>.
The <tt>print_pgtbl</tt> functions prints out the page-table entries
for the first 10 and last 10 pages of the <tt>pgtbltest</tt> process
using the <tt>pgpte</tt> system call that we added to xv6 for this
lab. The output looks as follows:
  <pre>
va 0 pte 0x21FCF45B pa 0x87F3D000 perm 0x5B
va 1000 pte 0x21FCE85B pa 0x87F3A000 perm 0x5B
...
va 0xFFFFD000 pte 0x0 pa 0x0 perm 0x0
va 0xFFFFE000 pte 0x21FD80C7 pa 0x87F60000 perm 0xC7
va 0xFFFFF000 pte 0x20001C4B pa 0x80007000 perm 0x4B
 </pre>

<div class="question">
For every page table entry in the <tt>print_pgtbl</tt> output, explain
what it logically contains and what its permission bits are. Figure
3.4 in the xv6 book might be helpful, although note that the figure
might have a slightly different set of pages than process that's being
inspected here.  Note that xv6 doesn't place the virtual pages
consecutively in physical memory.
</div>

<h2>Speed up system calls <script>g("easy")</script></h2>

<p>
Some operating systems (e.g., Linux) speed up certain system calls by sharing
data in a read-only region between userspace and the kernel. This eliminates the
need for kernel crossings when performing these system calls.  To help you learn
how to insert mappings into a page table, your first task is to implement this
optimization for the <tt>getpid()</tt> system call in xv6.

<div class="required">
When each process is created, map one read-only page at USYSCALL (a
virtual address defined
in <tt>memlayout.h</tt>). At the start of this page, store a <tt>struct
usyscall</tt> (also defined in <tt>memlayout.h</tt>), and initialize it to store
the PID of the current process. For this lab, <tt>ugetpid()</tt> has been
provided on the userspace side and will automatically use the USYSCALL mapping.
You will receive full credit for this part of the lab if the <tt>ugetpid</tt> test
case passes when running <tt>pgtbltest</tt>.
</div>

<p>Some hints:
<ul>
  <li>Choose permission bits that allow userspace to only read the page.
  <li>There are a few things that need to be done over the lifecycle of a new page.
      For inspiration, understand the trapframe handling in <tt>kernel/proc.c</tt>.
</ul>
 
<div class="question">
Which other xv6 system call(s) could be made faster using this shared page?
Explain how.
</div>

<h2>Print a page table <script>g("easy")</script></h2>

<p>
To help you visualize RISC-V page tables, and perhaps
to aid future debugging, your next task is to write a function
that prints the contents of a page table.

<div class="required">
  We added a system call <tt>kpgtbl()</tt>, which calls
  <tt>vmprint()</tt> in <tt>vm.c</tt>. It takes
a <tt>pagetable_t</tt> argument, and your job is to print that pagetable
in the format described below.
</div>

<p>
  When you run <tt>print_kpgtbl()</tt> test, your implementation
  should print the following output:

  <pre>
page table 0x0000000087f22000
 ..0x0000000000000000: pte 0x0000000021fc7801 pa 0x0000000087f1e000
 .. ..0x0000000000000000: pte 0x0000000021fc7401 pa 0x0000000087f1d000
 .. .. ..0x0000000000000000: pte 0x0000000021fc7c5b pa 0x0000000087f1f000
 .. .. ..0x0000000000001000: pte 0x0000000021fc70d7 pa 0x0000000087f1c000
 .. .. ..0x0000000000002000: pte 0x0000000021fc6c07 pa 0x0000000087f1b000
 .. .. ..0x0000000000003000: pte 0x0000000021fc68d7 pa 0x0000000087f1a000
 ..0xffffffffc0000000: pte 0x0000000021fc8401 pa 0x0000000087f21000
 .. ..0xffffffffffe00000: pte 0x0000000021fc8001 pa 0x0000000087f20000
 .. .. ..0xffffffffffffd000: pte 0x0000000021fd4c13 pa 0x0000000087f53000
 .. .. ..0xffffffffffffe000: pte 0x0000000021fd00c7 pa 0x0000000087f40000
 .. .. ..0xfffffffffffff000: pte 0x000000002000184b pa 0x0000000080006000
  </pre>

The first line displays the argument to <tt>vmprint</tt>.
After that there is a line for each PTE, including PTEs that
refer to page-table pages deeper in the tree.
Each PTE line is indented by a number of <tt>" .."</tt> that indicates its
depth in the tree.
Each PTE line shows its virtual addresss, the pte bits, and the
physical address extracted from the PTE.
Don't print PTEs that are not valid.  In the above example, the
top-level page-table page has mappings for entries 0 and 255.  The next
level down for entry 0 has only index 0 mapped, and the bottom-level
for that index 0 has a few entries mapped.

<p>
Your code might emit different physical addresses than those shown above.
The number of entries and the virtual addresses should be the same.
</p>

<p>Some hints:
<ul>
  <li>Use the macros at the end of the file kernel/riscv.h.
  <li>The function <tt>freewalk</tt> may be inspirational.
  <li>Use <tt>%p</tt> in your printf calls to print out full 64-bit hex PTEs and addresses as shown in the example.</li>
</ul>

<div class="question">
For every leaf page in the <tt>vmprint</tt> output, explain what it logically
contains and what its permission bits are, and how it relates to the
output of the earlier <tt>print_pgtbl()</tt> exercise above.
Figure 3.4 in the xv6 book might be helpful, although note that the figure might
have a slightly different set of pages than the process that's being inspected here.
</div>

<h2>Use superpages <script>g("moderate")</script>/<script>g("hard")</script></h2>
  
The RISC-V paging hardware supports two-megabyte pages as well as
ordinary 4096-byte pages. The general idea of larger pages is called
superpages, and (since RISC-V supports more than one size) 2M pages
are called megapages. The operating system creates a superpage by
setting the PTE_V and PTE_R bits in the level-1 PTE, and setting the
physical page number to point to the start of a two-megabyte region of
physical memory. This physical address must be two-mega-byte aligned
(i.e., a multiple of two megabytes). You can read about this in the
RISC-V privileged manual by searching for megapage and superpage; in
particular, the top of page 112.

Use of superpages decreases the amount of physical memory used by the
page table, and can decrease misses in the TLB cache. For some
programs this leads to large increases in performance.

<div class="required">
Your job is to modify the xv6 kernel to use superpages. In particular,
if a user program calls sbrk() with a size of 2 megabytes or more, and
the newly created address range includes one or more areas that are
two-megabyte-aligned and at least two megabytes in size, the kernel
should use a single superpage (instead of hundreds of ordinary pages).
You will receive full credit for this part of the lab if
the <tt>superpg_test</tt> test case passes when
running <tt>pgtbltest</tt>.
</div>

<p>Some hints:
  <ul>
    <li> Read <tt>superpg_test</tt> in <tt>user/pgtbltest.c</tt>.
    <li> A good place to start is <tt>sys_sbrk</tt>
      in <tt>kernel/sysproc.c</tt>, which is invoked by
      the <tt>sbrk</tt> system call.  Follow the code path to the
      function that allocates memory for <tt>sbrk</tt>.
    <li> Your kernel will need to be able to allocate and free two-megabyte
regions. Modify kalloc.c to set aside a few two-megabyte areas of
physical memory, and create superalloc() and superfree() functions.
You'll only need a handful of two-megabyte chunks of memory.
    <li>
Superpages must be allocated when a process with superpages forks,
and freed when it exits; you'll need to modify
<tt>uvmcopy()</tt> and <tt>uvmunmap()</tt>.
  </ul>

<p>Real operating systems dynamically promote a collection of pages to
a superpage.  The following reference explains why that is a good idea
and what is hard in a more serious design: <a href="https://www.usenix.org/conference/osdi-02/practical-transparent-operating-system-support-superpages">Juan Navarro, Sitaram Iyer,
Peter Druschel, and Alan Cox. Practical, transparent operating system
support for superpages. SIGOPS Oper.  Syst. Rev., 36(SI):89-104,
December 2002.</a>  This reference summarizes
  superpage-implementations
  for different OSes:
  <a href="https://www.usenix.org/conference/atc20/presentation/zhu-weixi">A
	   comprehensive analysis of superpage management mechanism
	   and policies</a>.
  
<p><a name="submit"></>
<h2>Submit the lab</h2>

<h3>Time spent</h3>

<p>Create a new file, <tt>time.txt</tt>, and put in a single integer, the
number of hours you spent on the lab.
<kbd>git add</kbd> and <kbd>git commit</kbd> the file.

<h3>Answers</h3>

<p>If this lab had questions, write up your answers in <tt>answers-*.txt</tt>.
<kbd>git add</kbd> and <kbd>git commit</kbd> these files.

<h3>Submit</h3>

<p>Assignment submissions are handled by Gradescope.
You will need an MIT gradescope account.
See Piazza for the entry code to join the class.
Use <a href="https://help.gradescope.com/article/gi7gm49peg-student-add-course#joining_a_course_using_a_course_code">this link</a>
if you need more help joining.

<p>When you're ready to submit, run <kbd>make zipball</kbd>,
which will generate <tt>lab.zip</tt>.
Upload this zip file to the corresponding Gradescope assignment.

<p> If you run <kbd>make zipball</kbd> and you have either uncomitted changes or
untracked files, you will see output similar to the following:
<pre>
 M hello.c
?? bar.c
?? foo.pyc
Untracked files will not be handed in.  Continue? [y/N]
</pre>
Inspect the above lines and make sure all files that your lab solution needs
are tracked, i.e., not listed in a line that begins with <tt>??</tt>.
You can cause <tt>git</tt> to track a new file that you create using
<kbd>git add {filename}</kbd>.
</p>

<p>
<div class="warning">
<ul>
  <li>Please run <kbd>make grade</kbd> to ensure that your code passes all of the tests.
    The Gradescope autograder will use the same grading program to assign your submission a grade.</li>
  <li>Commit any modified source code before running <kbd>make zipball</kbd>.</li>
  <li>You can inspect the status of your submission and download the submitted
    code at Gradescope. The Gradescope lab grade is your final lab grade.</li>
</ul>
</div>



<h2>Optional challenge exercises</h2>

<ul>

  <li>Implement some ideas from the paper referenced above to make
    your super-page design more real.

  <li>Unmap the first page of a user process so that dereferencing a
  null pointer will result in a fault.  You will have to
  change <tt>user.ld</tt> to start the user text segment at, for
  example, 4096, instead of 0.

  <li>Add a system call that reports dirty pages (modified pages) using <tt>PTE_D</tt>.

</ul>

</body>
</html>
