<html>
<head>
<title>Lab: System calls</title>
<link rel="stylesheet" href="labs.css" type="text/css" />
<script src="js/jquery-1.10.2.min.js"></script>
<script src="guidance.js"></script>
</head>
<body>

<h1>Lab: system calls</h1>

<p>In the last lab you used system calls to write a few utilities. In
this lab you will add some new system calls to xv6, which will help
you understand how they work and will expose you to some of the
internals of the xv6 kernel.  You will add more system calls in later
labs.

<div class="prereq">
<p>Before you start coding, read Chapter 2 of
    the <a href="../xv6/book-riscv-rev4.pdf">xv6 book</a>, and
    Sections 4.3 and 4.4 of Chapter 4, and
  related source files:

  <ul>

    <li> The user-space "stubs" that route system calls into the kernel are in <tt>user/usys.S</tt>,
      which is generated by <tt>user/usys.pl</tt> when you run <tt>make</tt>.
      Declarations are in <tt>user/user.h</tt>

    <li> The kernel-space code that routes a system call to the
    kernel function that implements it
    is in <tt>kernel/syscall.c</tt> and <tt>kernel/syscall.h</tt>.

    <li> Process-related code is <tt>kernel/proc.h</tt> and <tt>kernel/proc.c</tt>.

  </ul>
</div>

<p>To start the lab, switch to the syscall branch:
  <pre>
  $ <kbd>git fetch</kbd>
  $ <kbd>git checkout syscall</kbd>
  $ <kbd>make clean</kbd>
  </pre>

<p>If you run <tt>make grade</tt> you will see that the grading script cannot
exec
  <tt>trace</tt>.  Your job is to add the
  necessary system calls and stubs to make <tt>trace</tt> work.
  Furthermore, you will notice <tt>attacktest</tt> fails.
  
<h2>Using gdb <script>g("easy")</script></h2>

<p>In many cases, print statements will be sufficient to debug your
kernel, but sometimes it is useful to single step through code
or get a stack back-trace.
The GDB debugger can help.

<p> To help you become familiar with gdb, run <kbd>make qemu-gdb</kbd> and
then fire up gdb in another window
(see the gdb material on the <a href="guidance.html">guidance page</a>). Once you have two windows open, type in the gdb window:
<pre>
(gdb) <kbd>b syscall</kbd>
Breakpoint 1 at 0x80002142: file kernel/syscall.c, line 243.
(gdb) <kbd>c</kbd>
Continuing.
[Switching to Thread 1.2]

Thread 2 hit Breakpoint 1, syscall () at kernel/syscall.c:243
243     {
(gdb) <kbd>layout src</kbd>
(gdb) <kbd>backtrace</kbd>
</pre>

<p>The <tt>layout</tt> command splits the window in two, showing where
  gdb is in the source code. <tt>backtrace</tt> prints a
  stack
  backtrace</tt>.


<p>Answer the following questions in <tt>answers-syscall.txt</tt>.

  <div class="question">
    Looking at the backtrace output, which function
    called <tt>syscall</tt>?
  </div>

<p> Type <kbd>n</kbd> a few times to step past <tt> struct proc *p = myproc();</tt>
    Once past this statement, type <kbd>p /x
    *p</kbd>, which prints the current process's <tt>proc struct</tt>
    (see <tt>kernel/proc.h></tt>) in hex.

  <div class="question">
    What is the value of <tt>p->trapframe->a7</tt> and what does that
    value represent?  (Hint: look <tt>user/initcode.S</tt>, the first
    user program xv6 starts.)
  </div>

<p>The processor is running in supervisor mode, and we can print privileged
  registers such as <tt>sstatus</tt>
  (see <a href="https://github.com/riscv/riscv-isa-manual/releases/download/Priv-v1.12/riscv-privileged-20211203.pdf">RISC-V
    privileged instructions</a> for a description):

  <pre>
    (gdb) <kbd>p /x $sstatus</kbd>
  </pre>

   <div class="question">
     What was the previous mode that the CPU was in?
   </div>

<p>The xv6 kernel code contains consistency checks whose failure
  causes the kernel to panic; you may find that your kernel modifications
  cause panics.
  For example, replace the statement <tt>num = p->trapframe->a7;</tt>
with
  <tt>num = * (int *) 0;</tt>
  at the beginning of <tt>syscall</tt>, run <kbd>make
  qemu</kbd>, and you will see something similar to:
  <pre>
xv6 kernel is booting

hart 2 starting
hart 1 starting
scause=0xd sepc=0x80001bfe stval=0x0
panic: kerneltrap
  </pre>
Quit out of <tt>qemu</tt>.

<p>To track down the source of a kernel page-fault panic, search for
  the <tt>sepc</tt> value printed for the panic you just saw
  in the
  file <tt>kernel/kernel.asm</tt>, which contains the assembly for the
  compiled kernel.

  <div class="question">
    Write down the assembly instruction the kernel is panicing at.
    Which register corresponds to the variable <tt>num</tt>?
  </div>

<p>To inspect the state of the processor and the kernel at the
faulting instruction, fire up gdb, and set a breakpoint at the
faulting <tt>epc</tt>, like this:
<pre>
(gdb) <kbd>b *0x80001bfe</kbd>
Breakpoint 1 at 0x80001bfe: file kernel/syscall.c, line 138.
(gdb) <kbd>layout asm</kbd>
(gdb) <kbd>c</kbd>
Continuing.
[Switching to Thread 1.3]

Thread 3 hit Breakpoint 1, syscall () at kernel/syscall.c:138
</pre>

<p>Confirm that the faulting assembly instruction is the same as the
  one you found above.

  <div class="question">
    Why does the kernel crash? Hint: look at figure 3-3 in the text;
    is address 0 mapped in the kernel address space?  Is that
    confirmed by the value in <tt>scause</tt> above? (See description
    of <tt>scause</tt>
    in <a href="https:n//github.com/riscv/riscv-isa-manual/releases/download/Priv-v1.12/riscv-privileged-20211203.pdf">RISC-V
    privileged instructions</a>)
  </div>

<p>Note that <tt>scause</tt> was printed by the kernel panic above,
  but often you need to look at additional info to track down the
  problem that caused the panic.  For example, to find out which user
  process was running when the kernel paniced, you can print the
  process's name:
  <pre>
    (gdb) <kbd>p p->name</kbd>
  </pre>

    <div class="question">
      What is the name of the process that was running when the kernel
      paniced?  What is its process id (<tt>pid</tt>)?
  </div>

<p>You may want to revisit
  <a href="https://pdos.csail.mit.edu/6.828/2019/lec/gdb_slides.pdf">Using
  the GNU Debugger</a> as needed.
  The <a href="guidance.html">guidance page</a> also has 
  debugging tips.

<h2>System call tracing <script>g("moderate")</script></h2>

<p>
<div class="required">
  In this assignment you will add a system call tracing feature that
  may help you when debugging later labs.  You'll create a
  new <tt>trace</tt> system call that will control tracing. It should
  take one argument, an integer "mask", whose bits specify which
  system calls to trace.  For example, to trace the fork system call,
  a program calls <tt>trace(1 << SYS_fork)</tt>, where <tt>SYS_fork</tt> is a
  syscall number from <tt>kernel/syscall.h</tt>. You have to modify
  the xv6 kernel to print a line when each system call is about to
  return, if the system call's number is set in the mask.
  The line should contain the
  process id, the name of the system call and the
  return value; you don't need to print the system call
  arguments. The <tt>trace</tt> system call should enable tracing
  for the process that calls it and any children that it subsequently forks,
  but should not affect other processes.
</div>

<p>We provide a <tt>trace</tt> user-level program that runs another
  program with tracing enabled (see <tt>user/trace.c</tt>). When you're
  done, you should see output like this:

<pre>
$ trace 32 grep hello README
3: syscall read -> 1023
3: syscall read -> 966
3: syscall read -> 70
3: syscall read -> 0
$
$ trace 2147483647 grep hello README
4: syscall trace -> 0
4: syscall exec -> 3
4: syscall open -> 3
4: syscall read -> 1023
4: syscall read -> 966
4: syscall read -> 70
4: syscall read -> 0
4: syscall close -> 0
$
$ grep hello README
$
$ trace 2 usertests forkforkfork
usertests starting
test forkforkfork: 407: syscall fork -> 408
408: syscall fork -> 409
409: syscall fork -> 410
410: syscall fork -> 411
409: syscall fork -> 412
410: syscall fork -> 413
409: syscall fork -> 414
411: syscall fork -> 415
...
$
</pre>

<p>In the first example above, trace invokes grep tracing just the
read system call. The 32 is <tt>1&lt;&lt;SYS_read</tt>. In the second
example, trace runs grep while tracing all system calls; the
2147483647 has all 31 low bits set. In the third example, the program
isn't traced, so no trace output is printed. In the fourth example,
the fork system calls of all the descendants of the <tt>forkforkfork</tt> test
in <tt>usertests</tt> are being traced. Your solution is correct if your
program behaves as shown above (though the process IDs may be
different).

<p>Some hints:
  <ul>

    <li><p>Add <tt>$U/_trace</tt> to UPROGS in Makefile

    <li><p>Run <kbd>make qemu</kbd> and you will see that the
	compiler cannot compile <tt>user/trace.c</tt>, because the
	user-space stubs for the <tt>trace</tt> system call don't exist yet: add a
	prototype for <tt>trace</tt> to <tt>user/user.h</tt>, a stub
	to <tt>user/usys.pl</tt>, and a syscall number
	to <tt>kernel/syscall.h</tt>.  The Makefile invokes the perl
	script <tt>user/usys.pl</tt>, which produces <tt>user/usys.S</tt>,
	the actual system call stubs, which use the
	RISC-V <tt>ecall</tt> instruction to transition to the
	kernel. Once you fix the compilation issues,
	run <kbd>trace 32 grep hello README</kbd>; it will fail
	because you haven't implemented the system call in the kernel
	yet.

    <li><p>Add a <tt>sys_trace()</tt> function
	in <tt>kernel/sysproc.c</tt> that implements the new system
	call by remembering its argument in a new variable in
	the <tt>proc</tt> structure (see <tt>kernel/proc.h</tt>). The
	functions to retrieve system call arguments from user space are
	in <tt>kernel/syscall.c</tt>, and you can see examples
        of their use in <tt>kernel/sysproc.c</tt>.
        Add your new <tt>sys_trace</tt> to the <tt>syscalls</tt> array
        in <tt>kernel/syscall.c</tt>.
   </li>

    <li><p>Modify <tt>fork()</tt> (see <tt>kernel/proc.c</tt>) to copy
    the trace mask from the parent to the child process. </li>

    <li><p>Modify the <tt>syscall()</tt> function
	in <tt>kernel/syscall.c</tt> to print the trace output. You will need to add an array of syscall names to index into.</li>

  </ul>

<h2>Attack xv6 <script>g("moderate")</script></h2>

<p>The xv6 kernel isolates user programs from each other and isolates
  the kernel from user programs.  As you saw in the above assignments,
  an application cannot directly call a function in the kernel or in
  another user program; instead, interactions occur only through system
  calls.  However, if there is a bug in the implementation of a system
  call, an attacker may be able to exploit that bug to break the
  isolation boundaries.  To get a sense for how bugs can be exploited, we
  have introduced a bug into xv6 and your goal is to exploit that bug to
  trick xv6 into revealing a secret from another process.
  
<p>The bug is that the call to <tt>memset(mem, 0, sz)</tt> at line 272
  in <tt>kernel/vm.c</tt> to clear a newly-allocated page is omitted
  when compiling this lab.  Similarly, when
  compiling <tt>kernel/kalloc.c</tt> for this lab the two lines that
  use <tt>memset</tt> to put garbage into free pages are omitted.  The
  net effect of omitting these 3 lines (all marked by <tt>ifndef
  LAB_SYSCALL</tt>) is that newly allocated memory retains the
  contents from its previous use.

  <div class="required">
    <p><tt>user/secret.c</tt> writes an 8-byte secret in its memory and
    then exits (which frees its memory).
    Your goal is to add a few lines of code
    to <tt>user/attack.c</tt> to find the secret that a
    previous execution of <tt>secret.c</tt> wrote to memory,
    and write the 8 secret bytes to file descriptor 2.
    You'll receive full credit if <tt>attacktest</tt>
    prints: "OK: secret is ebb.ebb". (Note: the secret may be
    different for each run of <tt>attacktest</tt>.)
      
    <p>You are allowed to modify <tt>user/attack.c</tt>,
    but you cannot make any other changes:
    you cannot modify the xv6 kernel sources, secret.c,
    attacktest.c, etc.
  </div>

<p>

</ul>
      
<p>Some hints:
  <ul>

    <li> Run <tt>attacktest</tt> in the xv6 shell. It should the following output:
      <pre>
        $ attacktest
        FAIL: no/incorrect secret
      </pre>
      <p>Note that despite the 3 deleted lines, xv6 appears to work correctly: it started the shell and it ran <tt>attacktest</tt>. In
      fact, if you run <tt>usertests</tt> most of them pass!
  <li>Read <tt>user/attacktest.c</tt>. It generates a random 8-byte string,
    which it passes to the program <tt>secret</tt>, which writes it
    into its memory.  After <tt>secret</tt> exits, <tt>attacktest</tt>
    spawns <tt>attack</tt> and waits for <tt>attack</tt> to write the
    secret string to file descriptor 2.

    <li>Read <tt>user/secret.c</tt> and think about how you could trick
      xv6 into revealing the secret to <tt>attack.c</tt>.

    <li>Test your exploit by running <tt>attacktest</tt> in the xv6 shell.
    
</ul>

  <div class="question">
    <tt>user/secret.c</tt> copies the secret bytes to 
     memory whose address is 32 bytes after the start of a page.
     Change the 32 to 0 and you should see that your
     attack doesn't work anymore; why not?
  </div>

<p>Small bugs that do not directly affect correctness but still can be
  exploited to break security (like the one above) make kernel
  programming challenging.  xv6 is likely to have such bugs, although
  we try to not have them.  Real kernels, which have many more lines
  of code than xv6, have a long history of such bugs. For example, see
  the
  public <a href="https://www.opencve.io/cve?vendor=linux&product=linux_kernel">Linux
  vulnerabilities</a> and
  <a href="https://docs.kernel.org/process/security-bugs.html">how to
  report vulnerabilities</a>.
      
<p><a name="submit"></>
<h2>Submit the lab</h2>

<h3>Time spent</h3>

<p>Create a new file, <tt>time.txt</tt>, and put in a single integer, the
number of hours you spent on the lab.
<kbd>git add</kbd> and <kbd>git commit</kbd> the file.

<h3>Answers</h3>

<p>If this lab had questions, write up your answers in <tt>answers-*.txt</tt>.
<kbd>git add</kbd> and <kbd>git commit</kbd> these files.

<h3>Submit</h3>

<p>Assignment submissions are handled by Gradescope.
You will need an MIT gradescope account.
See Piazza for the entry code to join the class.
Use <a href="https://help.gradescope.com/article/gi7gm49peg-student-add-course#joining_a_course_using_a_course_code">this link</a>
if you need more help joining.

<p>When you're ready to submit, run <kbd>make zipball</kbd>,
which will generate <tt>lab.zip</tt>.
Upload this zip file to the corresponding Gradescope assignment.

<p> If you run <kbd>make zipball</kbd> and you have either uncomitted changes or
untracked files, you will see output similar to the following:
<pre>
 M hello.c
?? bar.c
?? foo.pyc
Untracked files will not be handed in.  Continue? [y/N]
</pre>
Inspect the above lines and make sure all files that your lab solution needs
are tracked, i.e., not listed in a line that begins with <tt>??</tt>.
You can cause <tt>git</tt> to track a new file that you create using
<kbd>git add {filename}</kbd>.
</p>

<p>
<div class="warning">
<ul>
  <li>Please run <kbd>make grade</kbd> to ensure that your code passes all of the tests.
    The Gradescope autograder will use the same grading program to assign your submission a grade.</li>
  <li>Commit any modified source code before running <kbd>make zipball</kbd>.</li>
  <li>You can inspect the status of your submission and download the submitted
    code at Gradescope. The Gradescope lab grade is your final lab grade.</li>
</ul>
</div>



<h2>Optional challenge exercises</h2>

<ul>

  <li>Print the system call arguments for traced system
  calls <script>g("easy")</script>.</li>

  <li>Find a bug in xv6 that allows an adversary to break process
  isolation or crash the kernel and let us know. (Side channels such
  as Meltdown are out of scope, although we will cover them in
  lecture.) </li>

</ul>

</body>
</html>
