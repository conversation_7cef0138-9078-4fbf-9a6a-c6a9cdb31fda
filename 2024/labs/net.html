<html>
<head>
<title>Lab: networking</title>
<link rel="stylesheet" href="labs.css" type="text/css" />
<script src="guidance.js"></script>
</head>
<body>

<h1>Lab: networking</h1>

<p>In this lab you will write an xv6 device driver for a network interface
card (NIC), and then write the receive half of an ethernet/IP/UDP 
protocol processing stack.

<p>Fetch the xv6 source for the lab and check out the <tt>net</tt> branch:

<pre>
  $ <kbd>git fetch</kbd>
  $ <kbd>git checkout net</kbd>
  $ <kbd>make clean</kbd>
</pre>

<h2>Background</h2>

<div class="prereq">
<p>Before writing code, you may find it helpful to review "Chapter 5: Interrupts and
device drivers" in the <a
href="../xv6/book-riscv-rev4.pdf">xv6 book</a>.
</div>
   
<p>You'll use a network device called the E1000 to handle network
communication. To xv6 (and the driver you write), the E1000 looks like a real
piece of hardware connected to a real Ethernet local area network (LAN). In
fact, the E1000 your driver will talk to is an emulation provided by qemu,
connected to a LAN that is also emulated by qemu. On this emulated LAN, xv6 (the "guest")
has an IP address of *********.
Qemu arranges for the computer running qemu (the "host")
to appear on the LAN with
IP address ********.
When xv6 uses the E1000 to send a
packet to ********, qemu delivers the packet to the appropriate application on the
host.

<p>You will use QEMU's "user-mode network stack".
QEMU's documentation has more about the user-mode
stack
<a href="https://wiki.qemu.org/Documentation/Networking#User_Networking_.28SLIRP.29">here</a>.
We've updated the Makefile to enable QEMU's user-mode network stack and 
E1000 network card emulation.

<p>The Makefile configures QEMU to record all incoming and outgoing
packets to the file <tt>packets.pcap</tt> in your lab directory. It may be helpful to review
these recordings to confirm that xv6 is transmitting and receiving the packets you
expect. To display the recorded packets:

<pre>
<kbd>tcpdump -XXnr packets.pcap</kbd>
</pre>

<p>
We've added some files to the xv6 repository for this lab.
The file <tt>kernel/e1000.c</tt> contains initialization
code for the E1000 as well as empty functions for
transmitting and receiving packets, which you'll fill in.
<tt>kernel/e1000_dev.h</tt> contains definitions for
registers and flag bits defined by the E1000 and
described in the Intel E1000 
<a href="../readings/8254x_GBe_SDM.pdf">Software Developer's Manual</a>.
<tt>kernel/net.c</tt> and <tt>kernel/net.h</tt>
contain simple network stack that implements the
<a href="https://en.wikipedia.org/wiki/Internet_Protocol">IP</a>, <a href="https://en.wikipedia.org/wiki/User_Datagram_Protocol">UDP</a>, and <a href="https://en.wikipedia.org/wiki/Address_Resolution_Protocol">ARP</a> protocols;
<tt>net.c</tt> has complete code for user processes to send UDP packets,
but lacks most of the code to receive packets and deliver
them to user space.
Finally, <tt>kernel/pci.c</tt> contains code that 
searches for an E1000 card on the PCI bus when xv6 boots.

<h2>Part One: NIC <script>g("moderate")</script></h2>

<div class="required">
<p>Your job is to complete
<tt>e1000_transmit()</tt> and
<tt>e1000_recv()</tt>,
both in <tt>kernel/e1000.c</tt>,
so that the driver can transmit and receive packets.
You are done with this part when <tt>make grade</tt> says your
solution passes the "txone" and "rxone" tests.
</div>

<div class="prereq">
  <p>While writing your code, you'll find yourself referring to the E1000 <a href="../readings/8254x_GBe_SDM.pdf">Software Developer's Manual</a>. Of particular help may be the following sections:
    <ul>
      <li>Section 2 is essential and gives an overview of the entire device.</li>
      <li>Section 3.2 gives an overview of packet receiving.</li>
      <li>Section 3.3 gives an overview of packet transmission, alongside section 3.4.</li>
      <li>Section 13 gives an overview of the registers used by the E1000.</li>
      <li>Section 14 may help you understand the init code that we've provided.</li>
    </ul>
</div>


<p>Browse the E1000 <a
href="../readings/8254x_GBe_SDM.pdf">Software Developer's Manual</a>.
This manual covers several closely related Ethernet controllers.
QEMU emulates the 82540EM. Skim Chapter 2 now to get a feel for
the device.  To write your driver, you'll need to be familiar with Chapters 3
and 14, as well as 4.1 (though not 4.1's subsections).  You'll also need to use
Chapter 13 as a reference.  The other chapters mostly cover components of the
E1000 that your driver won't have to interact with.  Don't worry about the
details at first; just get a feel for how the document is structured so you can
find things later. The E1000 has many advanced features,
most of which you can ignore. Only a small set of basic features is needed to
complete this lab.

<p>
The <tt>e1000_init()</tt> function we provide you in <tt>e1000.c</tt>
configures the E1000 to read packets to be transmitted from RAM, and
to write received packets to RAM. This technique is called DMA, for
direct memory access, referring to the fact that the E1000 hardware
directly writes and reads packets to/from RAM.

<p>
Because bursts of packets might arrive faster than the driver can process
them, <tt>e1000_init()</tt> provides the E1000 with multiple buffers into
which the E1000 can write packets. The E1000 requires these
buffers to be described by an array of "descriptors" in RAM; each
descriptor contains an address in RAM where the E1000 can
write a received packet.
<tt>struct
rx_desc</tt> describes the descriptor format.
The array of descriptors is called the
receive ring, or receive queue. It's a circular ring in the sense that
when the card or driver reaches the end of the array, it wraps back to
the beginning. <tt>e1000_init()</tt> allocates packet buffers with <tt>kalloc()</tt>
for the E1000 to DMA into.
There is also a transmit ring
into which the driver should place packets it wants the E1000 to send.
<tt>e1000_init()</tt> configures the two rings to have size
<tt>RX_RING_SIZE</tt> and <tt>TX_RING_SIZE</tt>.

<p>
When the network stack in <tt>net.c</tt> needs to send a packet,
it calls <tt>e1000_transmit()</tt> with a
pointer to a buffer that holds the packet to be sent;
<tt>net.c</tt> allocates this buffer with <tt>kalloc()</tt>.
Your transmit code must place a pointer to the packet data
in a descriptor in the TX (transmit) ring.
<tt>struct
tx_desc</tt> describes the descriptor format. You will need to ensure that each
buffer is eventually passed to <tt>kfree()</tt>, but only after the E1000 has finished
transmitting the packet (the E1000
sets the <tt>E1000_TXD_STAT_DD</tt> bit in the descriptor to indicate this).

<p>
When the E1000 receives each packet from the ethernet, it DMAs
the packet to the memory pointed to by
<tt>addr</tt> in the next RX (receive) ring descriptor.
If an E1000 interrupt is not already pending, the E1000 asks the PLIC
to deliver one as soon as interrupts are enabled.
Your <tt>e1000_recv()</tt> code must scan the RX ring and
deliver each new packet to the network stack (in <tt>net.c</tt>) by
calling <tt>net_rx()</tt>. You will then need to allocate a new buffer
and place it into the descriptor, so that when the E1000 reaches
that point in the RX ring again it finds a fresh buffer into which to DMA a new
packet.

<p>In addition to reading and writing the descriptor rings in RAM,
your driver will
need to interact with the E1000 through its memory-mapped control registers,
to detect when
received packets are available and
to inform the E1000 that
the driver has filled in some TX descriptors with packets to send.
The global variable <tt>regs</tt> holds a
pointer to the
E1000's first control register;
your driver can get at the other registers by indexing <tt>regs</tt>
as an array.
You'll need to use indices <tt>E1000_RDT</tt> and
<tt>E1000_TDT</tt> in particular.

<p>
To test e1000_transmit() sending a single packet,
run <tt>python3 nettest.py txone</tt> in one window,
and in another window run <tt>make qemu</tt>
and then run <tt>nettest txone</tt> in xv6,
which sends a single packet.
<tt>nettest.py</tt> will print <tt>txone: OK</tt>
if all went well (i.e. qemu's e1000 emulator
saw the packet on the DMA ring and forwarded it
outside of qemu).

<p>
If transmitting worked, <kbd>tcpdump -XXnr packets.pcap</kbd>
shold produce output like this:
<pre>
reading from file packets.pcap, link-type EN10MB (Ethernet)
21:27:31.688123 IP *********.2000 > ********.25603: UDP, length 5
        0x0000:  5255 0a00 0202 5254 0012 3456 0800 4500  RU....RT..4V..E.
        0x0010:  0021 0000 0000 6411 3ebc 0a00 020f 0a00  .!....d.>.......
        0x0020:  0202 07d0 6403 000d 0000 7478 6f6e 65    ....d.....txone
</pre>

<p>
To test e1000_recv() receiving two packets (an
ARP query, then a IP/UDP packet), run <tt>make qemu</tt>
in one window, and <tt>python3 nettest.py rxone</tt>
in another window. <tt>nettest.py rxone</tt> sends a
single UDP packet via qemu to xv6; qemu actually first
sends an ARP request to xv6, and (after xv6 returns
an ARP reply) qemu forwards the UDP packet to xv6.
If e1000_recv() works correctly and passes those
packets to <tt>net_rx()</tt>, <tt>net.c</tt> should print
<pre>
arp_rx: received an ARP packet
ip_rx: received an IP packet
</pre>
<tt>net.c</tt> already contains the code to detect qemu's
ARP request and call <tt>e1000_transmit()</tt> to send
its reply.
This test requires that both e1000_transmit() and e1000_recv() work.
In addition, if all went well,
<kbd>tcpdump -XXnr packets.pcap</kbd> should produce output like this:
<pre>
reading from file packets.pcap, link-type EN10MB (Ethernet)
21:29:16.893600 ARP, Request who-has ********* tell ********, length 28
        0x0000:  ffff ffff ffff 5255 0a00 0202 0806 0001  ......RU........
        0x0010:  0800 0604 0001 5255 0a00 0202 0a00 0202  ......RU........
        0x0020:  0000 0000 0000 0a00 020f                 ..........
21:29:16.894543 ARP, Reply ********* is-at 52:54:00:12:34:56, length 28
        0x0000:  5255 0a00 0202 5254 0012 3456 0806 0001  RU....RT..4V....
        0x0010:  0800 0604 0002 5254 0012 3456 0a00 020f  ......RT..4V....
        0x0020:  5255 0a00 0202 0a00 0202                 RU........
21:29:16.902656 IP ********.61350 > *********.2000: UDP, length 3
        0x0000:  5254 0012 3456 5255 0a00 0202 0800 4500  RT..4VRU......E.
        0x0010:  001f 0000 0000 4011 62be 0a00 0202 0a00  ......@.b.......
        0x0020:  020f efa6 07d0 000b fdd6 7879 7a         ..........xyz
</pre>

<p>
Your output will look somewhat different, but it should contain
the strings "ARP, Request", "ARP, Reply", "UDP", 
and "....xyz".

<p>
If both of the above tests work, then <tt>make grade</tt>
should show that the first two tests pass.

<h2>e1000 hints</h2>

Start by adding print statements to <tt>e1000_transmit()</tt>
and <tt>e1000_recv()</tt>, and running (in
xv6) <tt>nettest txone</tt>. You should see from your print statements
that <tt>nettest txone</tt> generates a call to <tt>e1000_transmit</tt>.

<p>Some hints for implementing <tt>e1000_transmit</tt>:

<ul>

<li>First ask the E1000 for the TX ring index
at which it's expecting the next packet, by reading the
<tt>E1000_TDT</tt> control register.

<li> Then check if the the ring is overflowing. If <tt>E1000_TXD_STAT_DD</tt> is
not set in the descriptor indexed by <tt>E1000_TDT</tt>,
the E1000 hasn't finished the corresponding previous transmission
request, so return an error.

<li> Otherwise, use <tt>kfree()</tt> to free the last buffer that was
transmitted from that descriptor (if there was one).

<li> Then fill in the descriptor.
Set the necessary cmd flags (look at Section 3.3 in the E1000 manual) and stash away a
pointer to the buffer for later freeing.

<li> Finally, update the ring position by adding one to <tt>E1000_TDT</tt>
  modulo <tt>TX_RING_SIZE</tt>.

<li> If <tt>e1000_transmit()</tt> added the packet successfully to the ring, return 0.
  On failure (e.g., there
  is no descriptor available), return -1 so that
  the caller knows to free the buffer.
  
</ul>

<p>Some hints for implementing <tt>e1000_recv</tt>:

<ul>
    
<li> First ask the E1000 for the ring index at which the next waiting
received packet (if any) is located, by fetching the <tt>E1000_RDT</tt>
control register and adding
one modulo <tt>RX_RING_SIZE</tt>.

<li> Then check if a new packet is available by checking for the
<tt>E1000_RXD_STAT_DD</tt> bit in the <tt>status</tt> portion of the descriptor.
If not, stop.

<li> Deliver the packet buffer to the
  network stack by calling <tt>net_rx()</tt>.
 
<li> Then allocate a new buffer using <tt>kalloc()</tt> to replace the one just given to <tt>net_rx()</tt>.
  Clear the descriptor's status bits to zero.

<li> Finally, update the <tt>E1000_RDT</tt> register to be the index
of the last ring descriptor processed.

<li><tt>e1000_init()</tt> initializes the RX ring with buffers,
and you'll want to look at how it does that and perhaps borrow code.

<li>
At some point the total number of packets that have ever
arrived will exceed the ring size (16); make sure your
code can handle that.

<li>
The e1000 can deliver more than one packet per interrupt;
your <tt>e1000_recv</tt> should handle that situation.

</ul>

<p>You'll need locks to cope with the possibility that xv6 might
use the E1000 from more than one process, or might be using the E1000
in a kernel thread when an interrupt arrives.

<h2>Part Two: UDP Receive <script>g("moderate")</script></h2>

<p>
UDP, the User Datagram Protocol, allows user processes on different
Internet hosts to exchange individual packets (datagrams). UDP is
layered on top of IP. A user process indicates which host it wants to
send a packet to by specifying a 32-bit IP address. Each UDP
packet contains a source port number and a destination port number;
processes can request to receive packets that arrive addressed to
particular port numbers, and can specify the destination port number
when sending. Thus two processes on different hosts can communicate
with UDP if they know each others' IP addresses and the port numbers
each is listening for. For example, Google operates a DNS name
server on the host with IP address *******, listening on UDP port 53.

<p>
In this task, you'll add code to <tt>kernel/net.c</tt> to receive
UDP packets, queue them, and allow user processes to read them.
<tt>net.c</tt> already contains the code required for user
processes to transmit UDP packets (with the exception of
e1000_transmit(), which you provide).

<div class="required">
<p>Your job is to implement
<tt>ip_rx()</tt>,
<tt>sys_recv()</tt>,
and
<tt>sys_bind()</tt>
in <tt>kernel/net.c</tt>.
You are done when <tt>make grade</tt> says your
solution passes all of the tests.

<p>
You can run the same tests that <tt>make grade</tt> runs by running
<tt>python3 nettest.py grade</tt> in one window, and (in another window)
then running <tt>nettest grade</tt> inside xv6. If all goes well,
<tt>nettest.py</tt> should print <tt>txone: OK</tt>, and you should
see this in the xv6 window:

<pre>
$ nettest grade
txone: sending one packet
arp_rx: received an ARP packet
ip_rx: received an IP packet
ping0: starting
ping0: OK
ping1: starting
ping1: OK
ping2: starting
ping2: OK
ping3: starting
ping3: OK
dns: starting
DNS arecord for pdos.csail.mit.edu. is **************
dns: OK
</pre>
</div>

<p>
The system-call API specification for UDP looks like this:

<ul>

<li><tt>send(short sport, int dst, short dport, char *buf, int len)</tt>:
This system call sends a UDP packet to the host with IP address <tt>dst</tt>,
and (on that host) the process listening to port <tt>dport</tt>. The packet's
source port number will be <tt>sport</tt> (this port number is reported
to the receiving process, so that it can reply to the sender). The content
("payload") of the UDP packet will the <tt>len</tt> bytes at address <tt>buf</tt>.
The return value is 0 on success, and -1 on failure.

<li><tt>recv(short dport, int *src, short *sport, char *buf, int maxlen)</tt>:
This system call returns the payload of a UDP packet that arrives
with destination port <tt>dport</tt>. If one or more packets arrived
before the call to <tt>recv()</tt>, it should return right away with
the earliest waiting packet. If no packets are waiting, <tt>recv()</tt>
should wait until a packet for <tt>dport</tt> arrives.
<tt>recv()</tt> should see arriving packets for a given port in
arrival order.
<tt>recv()</tt>
copies the packet's 32-bit source IP address to <tt>*src</tt>,
copies the packet's 16-bit UDP source port number to <tt>*sport</tt>,
copies at most <tt>maxlen</tt> bytes of the packet's UDP payload 
to <tt>buf</tt>, and removes the packet from the queue. The system call
returns the number of bytes of the UDP payload copied, or -1 if there 
was an error.

<li><tt>bind(short port)</tt>:
A process should call <tt>bind(port)</tt> before it 
calls <tt>recv(port, ...)</tt>. If a UDP packet arrives with
a destination port that hasn't been passed to <tt>bind()</tt>,
<tt>net.c</tt> should discard that packet. The reason for this
system call is to initialize any structures <tt>net.c</tt>
needs in order to store arriving packets for a subsequent
<tt>recv()</tt> call.

<li><tt>unbind(short port)</tt>: You do not need to implement
this system call, since the test code does not use it. But you
can if you like in order to provide symmetry with <tt>bind()</tt>.

</ul>

<p>
All the addresses and port numbers passed as arguments to these
system calls, and returned by them, must be in host byte order
(see below).

<p>
You'll need to provide the kernel implementations of the system
calls, with the exception of <tt>send()</tt>. The
program <tt>user/nettest.c</tt> uses this API.

<p>
To make <tt>recv()</tt> work, you'll need to add code
to <tt>ip_rx()</tt>, which <tt>net_rx()</tt> calls for each received
IP packet. <tt>ip_rx()</tt> should decide if the arriving packet is
UDP, and whether its destination port has been passed
to <tt>bind()</tt>; if both are true, it should save the packet
where <tt>recv()</tt> can find it. However, for any given port, no
more than 16 packets should be saved; if 16 are already waiting
for <tt>recv()</tt>, an incoming packet for that port should be
dropped. The point of this rule is to prevent a fast or abusive sender
from forcing xv6 to run out of memory. Furthermore, if packets are
being dropped for one port because it already has 16 packets waiting,
that should not affect packets arriving for other ports.

<p>
The packet buffers that <tt>ip_rx()</tt> looks at contain a 14-byte
ethernet header, followed by a 20-byte IP header, followed by an
8-byte UDP header, followed by the UDP payload. You'll find C
struct definitions for each of these in <tt>kernel/net.h</tt>.
Wikipedia has a description of the IP header
<a href="https://en.wikipedia.org/wiki/Internet_Protocol_version_4#Header">here</a>,
and UDP
<a href="https://en.wikipedia.org/wiki/User_Datagram_Protocol">here</a>.

<p>
Production IP/UDP implementations are complex, handling protocol
options and validating invariants. You only need to do enough to
pass <tt>make grade</tt>. Your code needs to look at ip_p and ip_src
in the IP header, and dport, sport, and ulen in the UDP header.

<p>
You will have to pay attention to byte order. Ethernet, IP, and UDP
header fields that contain multi-byte integers place the most
significant byte first in the packet. The RISC-V CPU, when it lays out
a multi-byte integer in memory, places the least-significant byte
first. This means that, when code extracts a multi-byte integer from a
packet, it must re-arrange the bytes. This applies to short (2-byte)
and int (4-byte) fields. You can use the <tt>ntohs()</tt>
and <tt>ntohl()</tt> functions for 2-byte and 4-byte fields,
respectively. Look at <tt>net_rx()</tt> for an example of this when
looking at the 2-byte ethernet type field.

<p>
If there are errors or omissions in your E1000 code, they
may only start to cause problems during the ping tests.
For example, the ping tests send and receive enough packets
that the descriptor ring indices will wrap around.

<p>Some hints:

<ul>

<li> Create a struct to keep track of bound ports and the packets in their queues.

<li> Refer to the <tt>sleep(void *chan, struct spinlock *lk)</tt> and <tt>wakeup(void *chan)</tt>
functions in <tt>kernel/proc.c</tt> to implement the waiting logic for <tt>recv()</tt>.

<li> The destination addresses that <tt>sys_recv()</tt> copies the packets to are virtual addresses;
you will have to copy from the kernel to the current user process. 

<li> Make sure to free packets that have been copied over or have been dropped. 

</ul>

<p><a name="submit"></>
<h2>Submit the lab</h2>

<h3>Time spent</h3>

<p>Create a new file, <tt>time.txt</tt>, and put in a single integer, the
number of hours you spent on the lab.
<kbd>git add</kbd> and <kbd>git commit</kbd> the file.

<h3>Answers</h3>

<p>If this lab had questions, write up your answers in <tt>answers-*.txt</tt>.
<kbd>git add</kbd> and <kbd>git commit</kbd> these files.

<h3>Submit</h3>

<p>Assignment submissions are handled by Gradescope.
You will need an MIT gradescope account.
See Piazza for the entry code to join the class.
Use <a href="https://help.gradescope.com/article/gi7gm49peg-student-add-course#joining_a_course_using_a_course_code">this link</a>
if you need more help joining.

<p>When you're ready to submit, run <kbd>make zipball</kbd>,
which will generate <tt>lab.zip</tt>.
Upload this zip file to the corresponding Gradescope assignment.

<p> If you run <kbd>make zipball</kbd> and you have either uncomitted changes or
untracked files, you will see output similar to the following:
<pre>
 M hello.c
?? bar.c
?? foo.pyc
Untracked files will not be handed in.  Continue? [y/N]
</pre>
Inspect the above lines and make sure all files that your lab solution needs
are tracked, i.e., not listed in a line that begins with <tt>??</tt>.
You can cause <tt>git</tt> to track a new file that you create using
<kbd>git add {filename}</kbd>.
</p>

<p>
<div class="warning">
<ul>
  <li>Please run <kbd>make grade</kbd> to ensure that your code passes all of the tests.
    The Gradescope autograder will use the same grading program to assign your submission a grade.</li>
  <li>Commit any modified source code before running <kbd>make zipball</kbd>.</li>
  <li>You can inspect the status of your submission and download the submitted
    code at Gradescope. The Gradescope lab grade is your final lab grade.</li>
</ul>
</div>



<h2>Optional Challenges:</h2>
  
<ul>
  
<li> In this lab, the networking stack uses interrupts to handle ingress packet
processing, but not egress packet processing. A more sophisticated strategy
would be to queue egress packets in software and only provide a limited number
to the NIC at any one time. You can then rely on TX interrupts to refill the
transmit ring. Using this technique, it becomes possible to prioritize
  different types of egress traffic. <script>g("easy")</script>
  
<li> The provided networking code only partially supports ARP. Implement a full
<a href="https://tools.ietf.org/html/rfc826">ARP cache</a>. <script>g("moderate")</script>

<li> The E1000 supports multiple RX and TX rings. Configure the E1000 to
provide a ring pair for each core and modify your networking stack to support
multiple rings. Doing so has the potential to increase the throughput that
your networking stack can support as well as reduce lock contention.
<script>g("moderate")</script>, but difficult to test/measure
  
<li> <a href="https://tools.ietf.org/html/rfc792">ICMP</a> can provide
notifications of failed networking flows. Detect these notifications and
  propagate them as errors to the user process.
  
<li> The E1000 supports several stateless hardware offloads, including
checksum calculation, RSC, and GRO. Use one or more of these offloads
to increase the throughput of your networking
stack.  <script>g("moderate")</script>, but hard to test/measure

<li> The networking stack in this lab is susceptible to receive livelock. Using
the material in lecture and the reading assignment, devise and implement a
  solution to fix it.   <script>g("moderate")</script>, but hard to test.

<li> Implement a minimal TCP stack and download a web
  page. <script>g("hard")</script>
  
</ul>

<p> Some of these challenges are intended to increase performance
in ways that may not be apparent or measurable under QEMU.

<p> If you pursue a challenge problem, whether it is related to networking or
  not, please let the course staff know!
  
</body>
</html>
