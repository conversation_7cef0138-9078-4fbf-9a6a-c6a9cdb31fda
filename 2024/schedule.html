<!-- Automatically generated by ./schedule.cgi on Sat Dec 14 10:00:18 2024 -->
<!-- DO NOT EDIT -->
<HTML>
<HEAD>
<TITLE>
6.1810 / Fall 2024
</TITLE>
<META HTTP-EQUIV="pragma" CONTENT="no-cache">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">

<link href="css/bootstrap.min.css" rel="stylesheet" media="screen">
<link rel="stylesheet" type="text/css" href="css/style.css">
<link rel="stylesheet" href="labs/labs.css" type="text/css" />

</HEAD>

<BODY BGCOLOR=#ffffff TEXT=#000000>

    <!-- Fixed navbar -->
    <div class="navbar navbar-default navbar-fixed-top" role="navigation">
      <div class="container-fluid">
        <div class="navbar-header">
          <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
            <span class="sr-only">Toggle navigation</span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
          <a class="navbar-brand" href="index.html">6.1810: Operating System Engineering</a>
        </div>
        <div class="navbar-collapse collapse">
          <ul class="nav navbar-nav">
	    <li><a href="schedule.html">Schedule</a></li>
	    <li class="dropdown">
              <a href="#" class="dropdown-toggle" data-toggle="dropdown">Class<span class="caret"></span></a>
	      <ul class="dropdown-menu" role="menu">
                <li><a href="overview.html">Overview</a></li>
                <li><a href="general.html">Course Structure</a></li>
		<li><a href="https://pdos.csail.mit.edu/6.1810/2023/">6.1810 2023</a></li>
	      </ul>
	    </li>
            <li class="dropdown">
              <a href="#" class="dropdown-toggle" data-toggle="dropdown">Labs<span class="caret"></span></a>
              <ul class="dropdown-menu" role="menu">
		<li><a href="tools.html">Tools</a></li>
		<li><a href="labs/guidance.html">Guidance</a></li>
                <li><a href="labs/util.html">Lab Utilities</a></li>
		<li><a href="labs/syscall.html">Lab System calls</a></li>
		<li><a href="labs/pgtbl.html">Lab Page tables</a></li>
		<li><a href="labs/traps.html">Lab Traps</a></li>
		<li><a href="labs/cow.html">Lab Copy on-write</a></li>
		<li><a href="labs/net.html">Lab network driver</a></li>
		<li><a href="labs/lock.html">Lab Lock</a></li>
		<li><a href="labs/fs.html">Lab File system</a></li>
		<li><a href="labs/mmap.html">Lab mmap</a></li>
              </ul>
            </li>
            <li class="dropdown">
              <a href="#" class="dropdown-toggle" data-toggle="dropdown">xv6<span class="caret"></span></a>
	      <ul class="dropdown-menu" role="menu">
		<li><a href="xv6.html">xv6</a></li>
		<li><a href="xv6/book-riscv-rev4.pdf">xv6 book</a></li>
	      </ul>
	    </li>
	    <li><a href="reference.html">References</a></li>  
	    <li><a href="https://piazza.com/mit/fall2024/61810">Piazza</a></li>
          </ul>
	  <ul class="nav navbar-nav navbar-right">
	    <p class="navbar-text">2024</p>
	  </ul>
        </div>
      </div>
    </div>


<div class="container">





<style>
table.calendar {
    font-family: arial, helvetica;
    font-size: 10pt;
    empty-cells: show;
    border: 1px solid #000000;
    border-collapse: collapse;
}
table.calendar tr td {
    border: 1px solid #aaaaaa;
}
table.calendar tr {
    vertical-align: top;
    height: 6em;
    background: #eeeeee;
}
table.calendar thead tr {
    text-align: center;
    background: #444444;
    color: #ffffff;
    height: auto;
    font-weight: bold;
}
.lecture {
    background: #ffffaa;
}
.holiday {
    background: #ccffcc;
}
.special {
    background: #aaaaff;
}
.important {
    background: #ffaaaa;
}
.reading {
    color: #3333ff;
}
.deadline {
    color: #ff0000;
}
.hwdue {
    color: #ff0000;
}
.assignment {
    color: #0aa00a;
}
.date {
    color: #444444;
}
</style>
Links to notes, videos etc. on future
days are copies of materials from the 2023 version of 6.1810.
We will update the notes as the course
progresses.  The lecture notes may help you remember the lecture
content, but they are <i>not</i> a replacement for attending lectures.
<br><br>
  
<p>
<table class="calendar" cellspacing="0" cellpadding="6" width="100%">
 <thead>
  <tr>
   <td width="35%">Monday</td><td width="10%">Tuesday</td>
   <td width="35%">Wednesday</td><td width="10%">Thursday</td>
   <td width="10%">Friday</td>
  </tr>
 </thead>

<tr> <!-- week of sep 2 -->
  <td id="2024-9-2" class="holiday"><span class="date">sep 2</span><br />
    Labor Day</td>
  <td id="2024-9-3" class="special"><span class="date">sep 3</span><br />
    Reg Day</td>
  <td id="2024-9-4" class="lecture"><span class="date">sep 4</span><br />
    <b>LEC 1 (rtm):</b> <a href="lec/l-overview.txt">Introduction</a> and <a href="lec/l-overview/">examples</a> (handouts: <a href="xv6/book-riscv-rev4.pdf">xv6 book</a>)<br />
    <span class="reading"><b>Preparation</b>: <a href="xv6/book-riscv-rev4.pdf">Read chapter 1</a> (for your amusement: <a href="https://www.youtube.com/watch?v=tc4ROCJYbm0">Unix</a>)</span><br />
    <span class="assignment"><b>Assignment</b>: <a href="labs/util.html">Lab util: Unix utilities</a></span></td>
  <td id="2024-9-5"><span class="date">sep 5</span></td>
  <td id="2024-9-6"><span class="date">sep 6</span></td>
</tr>
<tr> <!-- week of sep 9 -->
  <td id="2024-9-9" class="lecture"><span class="date">sep 9</span><br />
    <b>LEC 2 (fk):</b> <a href="lec/l-c.txt">C in xv6</a>, <a href="lec/l-c_slides.pdf">slides</a>, and <a href="lec/l-c">examples</a><br />
    <span class="reading"><b>Preparation</b>: 2.9 (Bitwise operators) and 5.1 (Pointers and addresses) through 5.6 (Pointer arrays) and 6.4 (pointers to structures) by Kernighan and Ritchie  (K&amp;R)</span></td>
  <td id="2024-9-10"><span class="date">sep 10</span></td>
  <td id="2024-9-11" class="lecture"><span class="date">sep 11</span><br />
    <b>LEC 3 (fk):</b> <a href="lec/l-os.txt">OS design</a><br />
    <span class="reading"><b>Preparation</b>: <a href="xv6/book-riscv-rev4.pdf">Read chapter 2</a> and xv6 code: <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/proc.h">kernel/proc.h</a>, <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/defs.h">kernel/defs.h</a>, <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/entry.S">kernel/entry.S</a>, <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/main.c">kernel/main.c</a>, <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/user/initcode.S">user/initcode.S</a>, <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/user/init.c">user/init.c</a>, and skim <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/proc.c">kernel/proc.c</a> and <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/exec.c">kernel/exec.c</a></span><br />
    <span class="hwdue"><b>Homework 1 due:</b> <a href="homework/q.html">Question</a></span><br />
    <span class="assignment"><b>Assignment</b>: <a href="labs/syscall.html">Lab syscall: System calls</a></span></td>
  <td id="2024-9-12" class="important"><span class="date">sep 12</span><br />
    <span class="deadline"><b>DUE</b>: Lab util</span></td>
  <td id="2024-9-13"><span class="date">sep 13</span></td>
</tr>
<tr> <!-- week of sep 16 -->
  <td id="2024-9-16" class="lecture"><span class="date">sep 16</span><br />
    <b>LEC 4 (fk):</b> <a href="lec/l-vm.txt">page tables</a><br />
    <span class="reading"><b>Preparation</b>: Read <a href="xv6/book-riscv-rev4.pdf">Chapter 3</a> and <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/memlayout.h">kernel/memlayout.h</a>, <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/vm.c">kernel/vm.c</a>, <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/kalloc.c">kernel/kalloc.c</a>, <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/riscv.h">kernel/riscv.h</a>, and <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/exec.c">kernel/exec.c</a></span><br />
    <span class="hwdue"><b>Homework 2 due:</b> <a href="homework/q.html">Question</a></span></td>
  <td id="2024-9-17"><span class="date">sep 17</span></td>
  <td id="2024-9-18" class="lecture"><span class="date">sep 18</span><br />
    <b>LEC 5 (rtm):</b> <a href="lec/l-internal.txt">System call entry/exit</a><br />
    <span class="reading"><b>Preparation</b>: Read <a href="xv6/book-riscv-rev4.pdf">Chapter 4, except 4.6</a> and <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/riscv.h">kernel/riscv.h</a>, <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/trampoline.S">kernel/trampoline.S</a>, and <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/trap.c">kernel/trap.c</a></span><br />
    <span class="hwdue"><b>Homework 3 due:</b> <a href="homework/q.html">Question</a></span><br />
    <span class="assignment"><b>Assignment</b>: <a href="labs/pgtbl.html">Lab pgtbl: Page tables</a></span></td>
  <td id="2024-9-19" class="important"><span class="date">sep 19</span><br />
    <span class="deadline"><b>DUE</b>: Lab syscall</span></td>
  <td id="2024-9-20"><span class="date">sep 20</span></td>
</tr>
<tr> <!-- week of sep 23 -->
  <td id="2024-9-23" class="lecture"><span class="date">sep 23</span><br />
    <b>LEC 6 (TAs):</b> <a href="lec/gdb_slides.pdf">GDB</a> <a href="lec/l-riscv.txt">Calling conventions</a><br />
    <span class="reading"><b>Preparation</b>: Read <a href="readings/riscv-calling.pdf">Calling Convention</a></span></td>
  <td id="2024-9-24"><span class="date">sep 24</span></td>
  <td id="2024-9-25" class="lecture"><span class="date">sep 25</span><br />
    <b>LEC 7 (fk):</b> <a href="lec/l-pgfaults.txt">Page faults</a> <br />
    <span class="reading"><b>Preparation</b>: Read <a href="xv6/book-riscv-rev4.pdf">Section 4.6</a></span><br />
    <span class="hwdue"><b>Homework 4 due:</b> <a href="homework/q.html">Question</a></span><br />
    <span class="assignment"><b>Assignment</b>: <a href="labs/traps.html">Lab traps: Traps</a></span></td>
  <td id="2024-9-26" class="important"><span class="date">sep 26</span><br />
    <span class="deadline"><b>DUE</b>: Lab pgtbl</span></td>
  <td id="2024-9-27"><span class="date">sep 27</span></td>
</tr>
<tr> <!-- week of sep 30 -->
  <td id="2024-9-30" class="lecture"><span class="date">sep 30</span><br />
    <b>LEC 8 (fk):</b> <a href="lec/l-QA1.txt">Q&A labs</a><br />
    <span class="hwdue"><b>Homework 5 due:</b> <a href="homework/QA.html">Question</a></span></td>
  <td id="2024-10-1"><span class="date">oct 1</span></td>
  <td id="2024-10-2" class="lecture"><span class="date">oct 2</span><br />
    <b>LEC 9 (rtm):</b> <a href="lec/l-interrupt.txt">Device drivers</a>, <a href="lec/16550.pdf">16550.pdf</a><br />
    <span class="reading"><b>Preparation</b>: Read <a href="xv6/book-riscv-rev4.pdf">Chapter 5</a> and <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/kernelvec.S">kernel/kernelvec.S</a>, <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/plic.c">kernel/plic.c</a>, <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/console.c">kernel/console.c</a>, <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/uart.c">kernel/uart.c</a>, <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/printf.c">kernel/printf.c</a></span><br />
    <span class="hwdue"><b>Homework 6 due:</b> <a href="homework/q.html">Question</a></span><br />
    <span class="assignment"><b>Assignment</b>: <a href="labs/cow.html">Lab cow: Copy-on-write fork</a></span></td>
  <td id="2024-10-3" class="important"><span class="date">oct 3</span><br />
    <span class="deadline"><b>DUE</b>: Lab traps</span></td>
  <td id="2024-10-4" class="special"><span class="date">oct 4</span><br />
    ADD DATE</td>
</tr>
<tr> <!-- week of oct 7 -->
  <td id="2024-10-7" class="lecture"><span class="date">oct 7</span><br />
    <b>LEC 10 (fk):</b> <a href="lec/l-lockv2.txt">Locking</a><br />
    <span class="reading"><b>Preparation</b>: Read <a href="xv6/book-riscv-rev4.pdf">"Locking"</a> with <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/spinlock.h">kernel/spinlock.h</a> and <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/spinlock.c">kernel/spinlock.c</a></span><br />
    <span class="hwdue"><b>Homework 7 due:</b> <a href="homework/q.html">Question</a></span></td>
  <td id="2024-10-8"><span class="date">oct 8</span></td>
  <td id="2024-10-9" class="lecture"><span class="date">oct 9</span><br />
    <b>LEC 11 (rtm):</b> <a href="lec/l-threads.txt">Scheduling 1</a> <br />
    <span class="reading"><b>Preparation</b>: Read <a href="xv6/book-riscv-rev4.pdf">"Scheduling"</a> through Section 7.4, and <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/proc.c">kernel/proc.c</a>, <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/swtch.S">kernel/swtch.S</a></span><br />
    <span class="hwdue"><b>Homework 8 due:</b> <a href="homework/q.html">Question</a></span></td>
  <td id="2024-10-10"><span class="date">oct 10</span></td>
  <td id="2024-10-11"><span class="date">oct 11</span></td>
</tr>
<tr> <!-- week of oct 14 -->
  <td id="2024-10-14" class="holiday"><span class="date">oct 14</span><br />
    Indigenous Peoples Day</td>
  <td id="2024-10-15"><span class="date">oct 15</span></td>
  <td id="2024-10-16" class="lecture"><span class="date">oct 16</span><br />
    <b>LEC 12 (rtm):</b> <a href="lec/l-coordination.txt">Coordination</a>, <a href="lec/l-coordination.c">code</a><br />
    <span class="reading"><b>Preparation</b>: Read remainder of <a href="xv6/book-riscv-rev4.pdf">"Scheduling"</a>, and corresponding parts of <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/proc.c">kernel/proc.c</a>, <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/sleeplock.c">kernel/sleeplock.c</a></span><br />
    <span class="hwdue"><b>Homework 9 due:</b> <a href="homework/q.html">Question</a></span></td>
  <td id="2024-10-17" class="important"><span class="date">oct 17</span><br />
    <span class="deadline"><b>DUE</b>: Lab cow</span></td>
  <td id="2024-10-18"><span class="date">oct 18</span></td>
</tr>
<tr> <!-- week of oct 21 -->
  <td id="2024-10-21" class="lecture"><span class="date">oct 21</span><br />
    <b>LEC 13 (fk):</b> <a href="lec/l-fs.txt">File systems</a>  (<a href="lec/l-fs1.pdf">slides</a>)<br />
    <span class="reading"><b>Preparation</b>: Read <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/bio.c">kernel/bio.c</a>, <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/fs.c">kernel/fs.c</a>, <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/sysfile.c">kernel/sysfile.c</a>, <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/file.c">kernel/file.c</a> and <a href="xv6/book-riscv-rev4.pdf">"File system" (except for the logging sections)</a></span><br />
    <span class="hwdue"><b>Homework 10 due:</b> <a href="homework/q.html">Question</a></span></td>
  <td id="2024-10-22"><span class="date">oct 22</span></td>
  <td id="2024-10-23" class="important"><span class="date">oct 23</span><br />
    <b class="deadline">Midterm</b><br /> <b>in class during class hours</b><br /> <b>open book and notes but closed network</b><br /> <b>scope</b>: Lectures 1 through 12, labs through lab cow <br /> <b>practice:</b> <a href="quiz.html">previous quizzes</a>.<br />
    <span class="assignment"><b>Assignment</b>: <a href="labs/net.html">Lab net: Network driver</a></span></td>
  <td id="2024-10-24"><span class="date">oct 24</span></td>
  <td id="2024-10-25"><span class="date">oct 25</span></td>
</tr>
<tr> <!-- week of oct 28 -->
  <td id="2024-10-28" class="lecture"><span class="date">oct 28</span><br />
    <b>LEC 14 (fk):</b> <a href="lec/l-crash.txt">Crash recovery</a> (<a href="lec/l-fs2.pdf">slides</a>)<br />
    <span class="reading"><b>Preparation</b>: Read <a href="https://github.com/mit-pdos/xv6-riscv/blob/riscv/kernel/log.c">kernel/log.c</a> and <a href="xv6-book-riscv-rev4.pdf">the logging sections of the "File system" chapter</a></span><br />
    <span class="hwdue"><b>Homework 11 due:</b> <a href="homework/q.html">Question</a></span></td>
  <td id="2024-10-29"><span class="date">oct 29</span></td>
  <td id="2024-10-30" class="lecture"><span class="date">oct 30</span><br />
    <b>LEC 15 (rtm):</b> <a href="lec/l-journal.txt">File system performance and fast crash recovery</a><br />
    <span class="reading"><b>Preparation</b>: Read <a href="readings/journal-ext2fs.html">Journaling the Linux ext2fs Filesystem (1998)</a>, <a href="lec/ext3-faq.txt">FAQ</a></span><br />
    <span class="hwdue"><b>Homework 12 due:</b> <a href="homework/q.html">Question</a></span></td>
  <td id="2024-10-31"><span class="date">oct 31</span></td>
  <td id="2024-11-1"><span class="date">nov 1</span></td>
</tr>
<tr> <!-- week of nov 4 -->
  <td id="2024-11-4" class="holiday"><span class="date">nov 4</span><br />
    Hacking day: no class meeting; work on the lab<br />
    <span class="assignment"><b>Assignment</b>: <a href="labs/lock.html">Lab lock: Parallelism/locking</a></span></td>
  <td id="2024-11-5" class="important"><span class="date">nov 5</span><br />
    <span class="deadline"><b>DUE</b>: Lab net</span></td>
  <td id="2024-11-6" class="holiday"><span class="date">nov 6</span><br />
    Hacking day: no class meeting; work on the lab</td>
  <td id="2024-11-7"><span class="date">nov 7</span></td>
  <td id="2024-11-8"><span class="date">nov 8</span></td>
</tr>
<tr> <!-- week of nov 11 -->
  <td id="2024-11-11" class="holiday"><span class="date">nov 11</span><br />
    Veteran's Day</td>
  <td id="2024-11-12"><span class="date">nov 12</span></td>
  <td id="2024-11-13" class="lecture"><span class="date">nov 13</span><br />
    <b>LEC 16 (fk):</b> <a href="lec/l-uservm.txt">Virtual memory for applications</a>, <a href="lec/uservm-faq.txt">FAQ</a> (<a href="lec/mmap.c">sqrt example</a>)<br />
    <span class="reading"><b>Preparation</b>: Read <a href="readings/appel-li.pdf">Virtual Memory Primitives for User Programs (1991)</a></span><br />
    <span class="hwdue"><b>Homework 13 due:</b> <a href="homework/q.html">Question</a></span><br />
    <span class="assignment"><b>Assignment</b>: <a href="labs/fs.html">Lab fs: File system</a></span></td>
  <td id="2024-11-14" class="important"><span class="date">nov 14</span><br />
    <span class="deadline"><b>DUE</b>: Lab lock</span></td>
  <td id="2024-11-15"><span class="date">nov 15</span></td>
</tr>
<tr> <!-- week of nov 18 -->
  <td id="2024-11-18" class="lecture"><span class="date">nov 18</span><br />
    <b>LEC 17 (rtm):</b> <a href="lec/l-organization.txt">OS Organization</a>, <a href="lec/l4-faq.txt">FAQ</a><br />
    <span class="reading"><b>Preparation</b>: Read <a href="readings/microkernel.pdf">The Performance of micro-Kernel-Based Systems (1997)</a></span><br />
    <span class="hwdue"><b>Homework 14 due:</b> <a href="homework/q.html">Question</a></span></td>
  <td id="2024-11-19"><span class="date">nov 19</span></td>
  <td id="2024-11-20" class="lecture"><span class="date">nov 20</span><br />
    <b class="deadline">DROP DATE</b><br />
    <b>LEC 18 (rtm):</b> <a href="lec/l-vmm.txt">Virtual Machines</a>, <a href="lec/dune-faq.txt">FAQ</a><br />
    <span class="reading"><b>Preparation</b>: Read <a href="readings/belay-dune.pdf">Dune: Safe User-level Access to Privileged CPU Features (2012)</a></span><br />
    <span class="hwdue"><b>Homework 15 due:</b> <a href="homework/q.html">Question</a></span><br />
    <span class="assignment"><b>Assignment</b>: <a href="labs/mmap.html">Lab mmap: Mmap</a></span></td>
  <td id="2024-11-21" class="important"><span class="date">nov 21</span><br />
    <span class="deadline"><b>DUE</b>: Lab fs</span></td>
  <td id="2024-11-22"><span class="date">nov 22</span></td>
</tr>
<tr> <!-- week of nov 25 -->
  <td id="2024-11-25" class="lecture"><span class="date">nov 25</span><br />
    <b>LEC 19 (fk):</b> <a href="lec/l-redleaf.txt">Kernels and HLL</a>, <a href="lec/redleaf-faq.txt">FAQ</a><br />
    <span class="reading"><b>Preparation</b>: Read <a href="readings/redleaf.pdf">the RedLeaf paper (2020)</a></span><br />
    <span class="hwdue"><b>Homework 16 due:</b> <a href="homework/q.html">Question</a></span></td>
  <td id="2024-11-26"><span class="date">nov 26</span></td>
  <td id="2024-11-27" class="holiday"><span class="date">nov 27</span><br />
    Hacking day: no class meeting; work on the lab</td>
  <td id="2024-11-28" class="holiday" colspan="2"><span class="date">nov 28 - nov 29</span><br />
    Thanksgiving</td>
</tr>
<tr> <!-- week of dec 2 -->
  <td id="2024-12-2" class="lecture"><span class="date">dec 2</span><br />
    <b>LEC 20 (rtm):</b> <a href="lec/l-net.txt">Networking</a><br />
    <span class="reading"><b>Preparation</b>: Read <a href="readings/mogul96usenix.pdf">Receive Livelock (1996)</a>, <a href="lec/livelock-faq.txt">FAQ</a></span><br />
    <span class="hwdue"><b>Homework 17 due:</b> <a href="homework/q.html">Question</a></span></td>
  <td id="2024-12-3"><span class="date">dec 3</span></td>
  <td id="2024-12-4" class="holiday"><span class="date">dec 4</span><br />
    Hacking day: no class meeting; work on the lab</td>
  <td id="2024-12-5"><span class="date">dec 5</span></td>
  <td id="2024-12-6" class="important"><span class="date">dec 6</span><br />
    <span class="deadline"><b>DUE</b>: Lab mmap</span></td>
</tr>
<tr> <!-- week of dec 9 -->
  <td id="2024-12-9" class="lecture"><span class="date">dec 9</span><br />
    <b>LEC 21 (fk):</b> <a href="lec/l-meltdown.txt">Meltdown</a>, <a href="lec/meltdown-faq.txt">FAQ</a><br />
    <span class="reading"><b>Preparation</b>: Read <a href="readings/meltdown.pdf">Meltdown (2018)</a></span><br />
    <span class="hwdue"><b>Homework 18 due:</b> <a href="homework/q.html">Question</a></span></td>
  <td id="2024-12-10"><span class="date">dec 10</span></td>
  <td id="2024-12-11" class="lecture"><span class="date">dec 11</span><br />
    LAST DAY OF CLASSES<br />
    <b>LEC 22 (rtm):</b> <a href="lec/l-rcu.txt">Multi-Core scalability and RCU</a> <br />
    <span class="reading"><b>Preparation</b>: Read <a href="readings/rcu-decade-later.pdf">RCU paper (2013)</a>, <a href="lec/rcu-faq.txt">FAQ</a></span><br />
    <span class="hwdue"><b>Homework 19 due:</b> <a href="homework/q.html">Question</a></span></td>
  <td id="2024-12-12"><span class="date">dec 12</span></td>
  <td id="2024-12-13"><span class="date">dec 13</span></td>
</tr>
<tr> <!-- week of dec 16 - dec 20 -->
  <td id="2024-12-16" class="special" colspan="5"><span class="date">dec 16 - dec 20</span><br />
    Final in finals week: <b>Dec 19, DUPONT, 9am-11am</b><br /> <b>open book and notes but closed network</b><br /> <b>scope</b>: Lectures 13 through 22, labs net through mmap <br /> <b>practice:</b> <a href="quiz.html">previous quizzes</a>.</td>
</tr>

</table>
<script type="text/javascript">
var d = new Date();
for (var fwd = 0; fwd < 3; fwd++) {
  var i = d.getFullYear()+'-'+(d.getMonth()+1)+'-'+d.getDate();
  var e = document.getElementById(i);
  if (e) { e.style.border = '2px solid blue'; break; }
  d.setTime(d.getTime() + 24*60*60*1000);
}
</script>

<!-- End Page Content -->

<hr>

<font style="font-size: 12px;">
<p>Questions or comments regarding 6.1810?  Send e-mail to the course staff at
<A
HREF="mailto:<EMAIL>"><I><EMAIL></I></A>.

<p><a rel="license" href="https://creativecommons.org/licenses/by/3.0/us/"><img
alt="Creative Commons License" style="border-width:0"
src="https://i.creativecommons.org/l/by/3.0/us/88x31.png" ></a> <B><A HREF="#top">Top</A></B> //
<B><A HREF="index.html">6.1810 home</A></B> // 
<i>Last updated Saturday, 14-Dec-2024 10:00:11 EST</i>
</font>

</div>

<script src="js/jquery-1.10.2.min.js"></script>
<script src="js/bootstrap.min.js"></script>

</BODY>
</HTML>

