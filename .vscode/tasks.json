{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "make pdf",
            "type": "shell",
            "command": "cd xv6-riscv-book && make pdf"
        },
        {
            "label": "make zh pdf",
            "type": "shell",
            "command": "cd xv6-riscv-book && make book_zh.pdf"
        },
        {
            "label": "make kernel",
            "type": "shell",
            "command": "cd xv6-riscv && make"
        },
        {
            "label": "open qemu",
            "type": "shell",
            "command": "cd xv6-riscv && make qemu"
        },
        {
            "label": "open qemu gdb",
            "type": "shell",
            "command": "cd xv6-riscv && make qemu-gdb"
        },
        {
            "label": "build-xv6",
            "type": "shell",
            "command": "make",
            "args": [],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "options": {
                "cwd": "${workspaceFolder}/xv6-riscv"
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": {
                "owner": "gcc",
                "fileLocation": ["relative", "${workspaceFolder}/xv6-riscv"],
                "pattern": {
                    "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
                    "file": 1,
                    "line": 2,
                    "column": 3,
                    "severity": 4,
                    "message": 5
                }
            }
        },
        {
            "label": "clean-xv6",
            "type": "shell",
            "command": "make",
            "args": ["clean"],
            "group": "build",
            "options": {
                "cwd": "${workspaceFolder}/xv6-riscv"
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            }
        },
        {
            "label": "start-qemu-debug",
            "type": "shell",
            "command": "make",
            "args": ["qemu-gdb"],
            "group": "test",
            "options": {
                "cwd": "${workspaceFolder}/xv6-riscv"
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "dedicated",
                "showReuseMessage": false
            },
            "isBackground": true,
            "problemMatcher": {
                "pattern": {
                    "regexp": "^.*$"
                },
                "background": {
                    "activeOnStart": true,
                    "beginsPattern": "^.*Now run 'gdb' in another window.*$",
                    "endsPattern": "^.*Now run 'gdb' in another window.*$"
                }
            },
            "dependsOn": "build-xv6"
        },
        {
            "label": "run-xv6",
            "type": "shell",
            "command": "make",
            "args": ["qemu"],
            "group": "test",
            "options": {
                "cwd": "${workspaceFolder}/xv6-riscv"
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "dedicated"
            },
            "dependsOn": "build-xv6"
        },
        {
            "label": "cleanup-qemu",
            "type": "shell",
            "command": "pkill",
            "args": ["-f", "qemu-system-riscv64"],
            "group": "test",
            "options": {
                "cwd": "${workspaceFolder}/xv6-riscv"
            },
            "presentation": {
                "echo": false,
                "reveal": "never",
                "focus": false,
                "panel": "shared"
            }
        },
        {
            "label": "build-user-program",
            "type": "shell",
            "command": "make",
            "args": ["user/_${input:userProgram}"],
            "group": "build",
            "options": {
                "cwd": "${workspaceFolder}/xv6-riscv"
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            }
        },
        {
            "label": "run-tests",
            "type": "shell",
            "command": "make",
            "args": ["grade"],
            "group": "test",
            "options": {
                "cwd": "${workspaceFolder}/xv6-riscv"
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "dedicated"
            },
            "dependsOn": "build-xv6"
        }
    ],
    "inputs": [
        {
            "id": "userProgram",
            "description": "Enter the name of the user program to build (without underscore prefix)",
            "default": "find",
            "type": "promptString"
        }
    ]
}