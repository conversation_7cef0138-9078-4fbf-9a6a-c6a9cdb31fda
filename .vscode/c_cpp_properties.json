{"configurations": [{"name": "xv6-riscv", "includePath": ["${workspaceFolder}/xv6-riscv", "${workspaceFolder}/xv6-riscv/kernel", "${workspaceFolder}/xv6-riscv/user"], "defines": ["__riscv", "__riscv_xlen=64"], "compilerPath": "riscv64-unknown-elf-gcc", "cStandard": "c11", "cppStandard": "c++17", "intelliSenseMode": "gcc-x64", "compilerArgs": ["-Wall", "-Werror", "-O", "-fno-omit-frame-pointer", "-ggdb", "-gdwarf-2", "-mcmodel=medany", "-fno-common", "-nostdlib", "-fno-builtin-strncpy", "-fno-builtin-strncmp", "-fno-builtin-strlen", "-fno-builtin-memset", "-fno-builtin-memmove", "-fno-builtin-memcmp", "-fno-builtin-log", "-fno-builtin-bzero", "-fno-builtin-strchr", "-fno-builtin-exit", "-fno-builtin-malloc", "-fno-builtin-putc", "-fno-builtin-free", "-fno-builtin-memcpy", "-Wno-main", "-fno-builtin-printf", "-fno-builtin-fprintf", "-fno-builtin-vprintf", "-fno-stack-protector", "-fno-pie", "-no-pie"]}], "version": 4}