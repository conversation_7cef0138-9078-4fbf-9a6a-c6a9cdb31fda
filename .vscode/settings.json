{"C_Cpp.default.compilerPath": "riscv64-unknown-elf-gcc", "C_Cpp.default.cStandard": "c11", "C_Cpp.default.cppStandard": "c++17", "C_Cpp.default.intelliSenseMode": "gcc-x64", "C_Cpp.default.includePath": ["${workspaceFolder}/xv6-riscv", "${workspaceFolder}/xv6-riscv/kernel", "${workspaceFolder}/xv6-riscv/user"], "C_Cpp.default.defines": ["__riscv", "__riscv_xlen=64"], "C_Cpp.default.compilerArgs": ["-Wall", "-Werror", "-O", "-fno-omit-frame-pointer", "-ggdb", "-gdwarf-2", "-mcmodel=medany", "-fno-common", "-nostdlib", "-fno-builtin-strncpy", "-fno-builtin-strncmp", "-fno-builtin-strlen", "-fno-builtin-memset", "-fno-builtin-memmove", "-fno-builtin-memcmp", "-fno-builtin-log", "-fno-builtin-bzero", "-fno-builtin-strchr", "-fno-builtin-exit", "-fno-builtin-malloc", "-fno-builtin-putc", "-fno-builtin-free", "-fno-builtin-memcpy", "-Wno-main", "-fno-builtin-printf", "-fno-builtin-fprintf", "-fno-builtin-vprintf", "-fno-stack-protector", "-fno-pie", "-no-pie"], "files.associations": {"*.h": "c", "*.c": "c", "*.S": "asm-intel-x86-generic", "riscv.h": "c", "sleeplock.h": "c", "param.h": "c", "defs.h": "c", "types.h": "c", "user.h": "c", "stat.h": "c", "fs.h": "c", "stdarg.h": "c", "fcntl.h": "c"}, "files.exclude": {"**/*.o": true, "**/*.d": true, "**/*.asm": true, "**/*.sym": true, "**/fs.img": true, "**/mkfs/mkfs": true, "**/__pycache__": true, "**/xv6.out*": true}, "search.exclude": {"**/*.o": true, "**/*.d": true, "**/*.asm": true, "**/*.sym": true, "**/fs.img": true, "**/__pycache__": true, "**/xv6.out*": true}}