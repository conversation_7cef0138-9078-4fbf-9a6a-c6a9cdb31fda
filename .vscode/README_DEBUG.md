# VSCode Debug Configuration for xv6-riscv

This directory contains VSCode configuration files for debugging the xv6 operating system on RISC-V architecture.

## Prerequisites

Before using these debug configurations, ensure you have the following tools installed:

1. **RISC-V GNU Toolchain**: 
   - `riscv64-unknown-elf-gcc`
   - `riscv64-unknown-elf-gdb`
   - Alternative: `riscv64-linux-gnu-gcc` and `riscv64-linux-gnu-gdb`

2. **QEMU**: 
   - `qemu-system-riscv64`

3. **VSCode Extensions**:
   - C/C++ Extension Pack
   - C/C++ Extension (ms-vscode.cpptools)

## Debug Configurations

### 1. Debug xv6 Kernel
- **Name**: "Debug xv6 Kernel"
- **Purpose**: Debug the xv6 kernel code
- **Usage**: 
  1. Set breakpoints in kernel source files
  2. Press F5 or go to Run and Debug view
  3. Select "Debug xv6 Kernel" configuration
  4. The debugger will build xv6, start QEMU with GDB stub, and attach GDB

### 2. Debug xv6 User Program
- **Name**: "Debug xv6 User Program"
- **Purpose**: Debug user-space programs
- **Usage**:
  1. Set breakpoints in user program source files
  2. Press F5 or go to Run and Debug view
  3. Select "Debug xv6 User Program" configuration
  4. Enter the name of the user program (e.g., "find", "cat", "ls")
  5. The debugger will load both kernel and user program symbols

### 3. Attach to Running xv6
- **Name**: "Attach to Running xv6"
- **Purpose**: Attach to an already running xv6 instance
- **Usage**:
  1. Start xv6 manually with `make qemu-gdb`
  2. Use this configuration to attach the debugger

## Available Tasks

### Build Tasks
- **build-xv6**: Build the entire xv6 system (default build task)
- **clean-xv6**: Clean all build artifacts
- **build-user-program**: Build a specific user program

### Run Tasks
- **run-xv6**: Run xv6 in QEMU (non-debug mode)
- **start-qemu-debug**: Start QEMU with GDB stub for debugging
- **run-tests**: Run the xv6 test suite

### Utility Tasks
- **cleanup-qemu**: Kill any running QEMU processes

## Quick Start

1. **Debug the kernel**:
   - Open a kernel source file (e.g., `xv6-riscv/kernel/main.c`)
   - Set a breakpoint in the `main()` function
   - Press F5 and select "Debug xv6 Kernel"

2. **Debug a user program**:
   - Open a user program source file (e.g., `xv6-riscv/user/find.c`)
   - Set a breakpoint in the `main()` function
   - Press F5 and select "Debug xv6 User Program"
   - Enter "find" when prompted for the program name

## Troubleshooting

### Common Issues

1. **"riscv64-unknown-elf-gdb not found"**:
   - Install the RISC-V GNU toolchain
   - Or modify the `miDebuggerPath` in launch.json to use `riscv64-linux-gnu-gdb`

2. **"Connection refused" or GDB connection errors**:
   - Ensure QEMU is running with GDB stub enabled
   - Check that the GDB port (25000) is not in use
   - Try running `make qemu-gdb` manually first

3. **Symbols not loading**:
   - Ensure the project is built with debug symbols (`make clean && make`)
   - Check that the paths in the configuration match your project structure

4. **Breakpoints not hitting**:
   - Verify that the code you're debugging is actually being executed
   - For user programs, make sure the program is run within xv6
   - For kernel code, ensure the breakpoint is in code that gets executed during boot or system calls

### Manual Debugging

If the automated debug configurations don't work, you can debug manually:

1. Start QEMU with GDB stub:
   ```bash
   cd xv6-riscv
   make qemu-gdb
   ```

2. In another terminal, start GDB:
   ```bash
   cd xv6-riscv
   riscv64-unknown-elf-gdb kernel/kernel
   (gdb) target remote localhost:25000
   (gdb) continue
   ```

## Configuration Files

- **launch.json**: Debug configurations
- **tasks.json**: Build and run tasks
- **settings.json**: VSCode workspace settings
- **c_cpp_properties.json**: C/C++ IntelliSense configuration

## Notes

- The debug configurations automatically build xv6 before starting the debugger
- QEMU processes are automatically cleaned up after debugging sessions
- The configurations support both kernel and user-space debugging
- GDB port is set to 25000 (matching the Makefile's GDBPORT calculation)
