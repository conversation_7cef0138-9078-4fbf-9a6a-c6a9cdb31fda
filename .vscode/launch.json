{"version": "0.2.0", "configurations": [{"name": "Debug xv6 Kernel", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/xv6-riscv/kernel/kernel", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}/xv6-riscv", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "riscv64-unknown-elf-gdb", "miDebuggerServerAddress": "localhost:25000", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set architecture to RISC-V 64-bit", "text": "set architecture riscv:rv64", "ignoreFailures": false}, {"description": "Load kernel symbols", "text": "symbol-file kernel/kernel", "ignoreFailures": false}, {"description": "Enable disassemble next line", "text": "set disassemble-next-line auto", "ignoreFailures": true}, {"description": "Use compressed breakpoints", "text": "set riscv use-compressed-breakpoints yes", "ignoreFailures": true}], "preLaunchTask": "build-xv6", "postDebugTask": "cleanup-qemu"}, {"name": "Debug xv6 User Program", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/xv6-riscv/user/_${input:userProgram}", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}/xv6-riscv", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "riscv64-unknown-elf-gdb", "miDebuggerServerAddress": "localhost:25000", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set architecture to RISC-V 64-bit", "text": "set architecture riscv:rv64", "ignoreFailures": false}, {"description": "Load kernel symbols first", "text": "symbol-file kernel/kernel", "ignoreFailures": false}, {"description": "Add user program symbols", "text": "add-symbol-file user/_${input:userProgram}", "ignoreFailures": false}, {"description": "Enable disassemble next line", "text": "set disassemble-next-line auto", "ignoreFailures": true}], "preLaunchTask": "build-xv6", "postDebugTask": "cleanup-qemu"}, {"name": "Attach to Running xv6", "type": "cppdbg", "request": "attach", "program": "${workspaceFolder}/xv6-riscv/kernel/kernel", "processId": "${command:pickProcess}", "MIMode": "gdb", "miDebuggerPath": "riscv64-unknown-elf-gdb", "miDebuggerServerAddress": "localhost:25000", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set architecture to RISC-V 64-bit", "text": "set architecture riscv:rv64", "ignoreFailures": false}, {"description": "Load kernel symbols", "text": "symbol-file kernel/kernel", "ignoreFailures": false}]}], "inputs": [{"id": "userProgram", "description": "Enter the name of the user program to debug (without underscore prefix)", "default": "find", "type": "promptString"}]}