#!/usr/bin/env python3

import re
from gradelib import *

r = Runner(save("xv6.out"))

@test(5, "sleep, no arguments")
def test_sleep_no_args():
    r.run_qemu(shell_script([
        'sleep'
    ]))
    r.match(no=["exec .* failed", "$ sleep\n$"])

@test(5, "sleep, returns")
def test_sleep_no_args():
    r.run_qemu(shell_script([
        'sleep',
        'echo OK'
    ]))
    r.match('^OK$', no=["exec .* failed", "$ sleep\n$"])

@test(10, "sleep, makes syscall")
def test_sleep():
    r.run_qemu(shell_script([
        'sleep 10',
        'echo FAIL'
    ]), stop_breakpoint('sys_sleep'))
    r.match('\\$ sleep 10', no=['FAIL'])

@test(20, "pingpong")
def test_pingpong():
    r.run_qemu(shell_script([
        'pingpong', 'echo OK'
    ]))
    r.match('^\\d+: received ping$', '^\\d+: received pong$', '^OK$')

@test(20, "primes")
def test_primes():
    r.run_qemu(shell_script([
        'primes', 'echo OK'
    ]))
    args = ['prime %d' % i for i in [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97, 101, 103, 107, 109, 113, 127, 131, 137, 139, 149, 151, 157, 163, 167, 173, 179, 181, 191, 193, 197, 199, 211, 223, 227, 229, 233, 239, 241, 251, 257, 263, 269]]
    args.append('^OK$')
    r.match(*args)

@test(10, "find, in current directory")
def test_find_curdir():
    fn = random_str()
    r.run_qemu(shell_script([
        'echo > %s' % fn,
        'find . %s' % fn
    ]))
    r.match('./%s' % fn)
    r.match(no=["./README", "README"])

@test(10, "find, in sub-directory")
def test_find_curdir():
    dd = random_str()
    fn = random_str()
    r.run_qemu(shell_script([
        'echo > %s' % fn,
        'mkdir %s' % dd,
        'echo > %s/%s' % (dd, fn),
        'find %s %s' % (dd, fn)
    ]))
    r.match('%s/%s' % (dd, fn))
    r.match(no=['./%s' % fn])

@test(10, "find, recursive")
def test_find_recursive():
    needle = random_str()
    dirs = [random_str() for _ in range(3)]
    r.run_qemu(shell_script([
        'mkdir %s' % dirs[0],
        'echo > %s/%s' % (dirs[0], needle),
        'mkdir %s/%s' % (dirs[0], dirs[1]),
        'echo > %s/%s/%s' % (dirs[0], dirs[1], needle),
        'mkdir %s' % dirs[2],
        'echo > %s/%s' % (dirs[2], needle),
        'find . %s' % needle
    ]))
    r.match('./%s/%s' % (dirs[0], needle),
            './%s/%s/%s' % (dirs[0], dirs[1], needle),
            './%s/%s' % (dirs[2], needle))

@test(10, "xargs")
def test_xargs():
    r.run_qemu(shell_script([
        'sh < xargstest.sh',
        'echo DONE',
    ], 'DONE'))
    matches = re.findall("hello", r.qemu.output)
    assert_equal(len(matches), 3, "Number of appearances of 'hello'")

@test(9, "xargs, multi-line echo")
def test_xargs_multiline():
    r.run_qemu(shell_script(['(echo 1 ; echo 2) | xargs echo']))
    r.match('^1$', '^2$')

@test(1, "time")
def test_time():
    check_time()
    
run_tests()
