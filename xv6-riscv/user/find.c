#include "kernel/types.h"
#include "kernel/stat.h"
#include "user/user.h"
#include "kernel/fs.h"
#include "kernel/fcntl.h"

char *
fmtname(char *path)
{
    static char buf[DIRSIZ + 1];
    char *p;

    // Find first character after last slash.
    for (p = path + strlen(path); p >= path && *p != '/'; p--)
        ;
    p++;

    // Return blank-padded name.
    if (strlen(p) >= DIRSIZ)
        return p;
    memmove(buf, p, strlen(p));
    memset(buf + strlen(p), ' ', DIRSIZ - strlen(p));
    return buf;
}

int match(char *filename, char *pattern)
{
    // Simple wildcard matching
    while (*pattern && *filename)
    {
        if (*pattern == '*')
        {
            pattern++;
            while (*filename && *filename != *pattern)
                filename++;
        }
        else if (*pattern != *filename)
        {
            return 0;
        }
        pattern++;
        filename++;
    }
    return *pattern == '*' || *filename == '\0';
}

void looping(char *path, char *pattern, char *prefix)
{
    int fd;
    struct dirent de;
    struct stat st;
    char buf[512], *p;
    if ((fd = open(path, O_RDONLY)) < 0)
    {
        printf("find: cannot open %s\n", path);
        return;
    }
    if (fstat(fd, &st) < 0)
    {
        fprintf(2, "ls: cannot stat %s\n", path);
        close(fd);
        return;
    }

    while (read(fd, buf, sizeof(buf)) > 0)
    {
        char *filename = buf;
        char *pattern = pattern;
        if (match(filename, pattern))
        {
            printf("%s\n", filename);
        }
    }
    switch (st.type)
    {
    case T_DEVICE:
    case T_FILE:
        if (match(fmtname(path), pattern))
            printf("%s %s %d %d %d\n", prefix, fmtname(path), st.type, st.ino, (int)st.size);
        break;

    case T_DIR:
        if (strlen(path) + 1 + DIRSIZ + 1 > sizeof buf)
        {
            printf("ls: path too long\n");
            break;
        }
        strcpy(buf, path);
        p = buf + strlen(buf);
        *p++ = '/';
        while (read(fd, &de, sizeof(de)) == sizeof(de))
        {
            if (de.inum == 0)
                continue;
            memmove(p, de.name, DIRSIZ);
            p[DIRSIZ] = 0;
            if (stat(buf, &st) < 0)
            {
                printf("ls: cannot stat %s\n", buf);
                continue;
            }
            if (buf[0] == '.')
            {
                continue;
            }
            looping(buf, pattern, prefix);
            // printf("%s %d %d %d\n", fmtname(buf), st.type, st.ino, (int)st.size);
        }
        break;
    }
    close(fd);
}

int main(int argc, char *argv[])
{
    if (argc < 3)
    {
        printf("Usage: find <directory> <pattern>\n");
        return 0;
    }
    looping(argv[1], argv[2], "./");

    return 0;
}